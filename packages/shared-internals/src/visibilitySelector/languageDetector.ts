import path from 'node:path';
import anyMatch from 'anymatch';
import {languageData} from './languageData.js';

interface LanguageConfiguration {
    extensions?: string[];
    names?: string[];
    patterns?: string[];
    firstLine?: string;
}

type LanguageDetect = (name: string, firstLine: string) => boolean;

const compile = (configuration: LanguageConfiguration): LanguageDetect => {
    const {extensions, names, patterns, firstLine} = configuration;
    const firstLineRegExp = firstLine ? new RegExp(firstLine) : null;
    // @ts-expect-error 导出与ESM不匹配
    const matchPatterns = patterns ? anyMatch(patterns.map(v => (v.includes('/') ? `**/${v}` : v))) : null;

    return (name, firstLine) => {
        if (extensions && extensions.includes(path.extname(name))) {
            return true;
        }
        if (names && names.includes(name)) {
            return true;
        }
        if (matchPatterns && matchPatterns(name)) {
            return true;
        }
        if (firstLineRegExp && firstLineRegExp.test(firstLine)) {
            return true;
        }
        return false;
    };
};

interface LanguageEntry {
    id: string;
    detect: LanguageDetect;
}

export class LanguageDetector {
    static readonly default = new LanguageDetector();

    private readonly entries: LanguageEntry[] = [];

    static {
        for (const config of languageData) {
            LanguageDetector.default.registerLanguage(config.id, config);
        }
    }

    registerLanguage(id: string, configuration: LanguageConfiguration) {
        const detect = compile(configuration);
        this.entries.push({id, detect});
    }

    detect(name: string, content: string) {
        const indexOfLineBreak = content.indexOf('\n');
        const firstLine = indexOfLineBreak === -1 ? content : content.slice(0, indexOfLineBreak);
        const matched = this.entries.find(v => v.detect(name, firstLine.trim()));
        return matched?.id ?? null;
    }
}
