import {AgentConversationType} from '../schema.js';

export interface ChatSession {
    /** chat的来源，包括普通对话、各个智能体，用户区分唯一id，向前兼容默认为chat */
    source: 'Chat' | AgentConversationType;
    /** 从头到尾的一次对话 */
    sessionUuid: string;
    /** 一次对话的第一条消息的内容 */
    title: string;
    /** 创建时间 */
    ctime: number;
    /** 最后一次修改时间 */
    utime: number;
    /** 会话当次的工作区目录 */
    workspaceDirectory: string;
}

export interface ChatSessionDetail extends ChatSession {
    messages: any[];
}
