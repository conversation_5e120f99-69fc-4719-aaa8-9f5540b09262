import {parse as shellParse} from 'shell-quote';

/** Shell 关键字，不算作普通命令名 */
const SHELL_KEYWORDS = new Set([
    'if',
    'then',
    'else',
    'elif',
    'fi',
    'for',
    'while',
    'until',
    'do',
    'done',
    'case',
    'esac',
    'function',
    'select',
    'time',
    'in',
]);

/**
 * 解析shell命令字符串，提取其中的命令名称
 *
 * 该函数会解析给定的shell命令字符串，识别其中的命令名称
 * 比如: 'cd /path/to/dir && ls -l' 会返回 ['cd', 'ls']
 *
 * @param command - 要解析的shell命令字符串
 * @returns 包含命令名称的字符串数组，按命令在原始字符串中的出现顺序排列
 */
export function parseShellCommandName(command: string) {
    const parsedParts = shellParse(command);

    const subCommands: string[] = [];
    let expectingCommand = true;

    for (const part of parsedParts) {
        if (typeof part === 'object' && 'op' in part) {
            const op = part.op;
            if (['&&', '||', ';', '|', '|&', '&', '(', ')'].includes(op)) {
                expectingCommand = true;
            }
            continue;
        }

        if (typeof part === 'string' && expectingCommand && !SHELL_KEYWORDS.has(part)) {
            subCommands.push(part);
            expectingCommand = false;
        }
    }
    return subCommands;
}
