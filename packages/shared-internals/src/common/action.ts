export const ACTION_COMATE_PLUS_CHAT_QUERY = 'COMATE_PLUS_CHAT_QUERY';
export const ACTION_COMATE_PLUS_CHAT_CANCEL = 'COMATE_PLUS_CHAT_CANCEL';
export const ACTION_COMATE_PLUS_CUSTOM_COMMAND = 'COMATE_PLUS_CUSTOM_COMMAND';
export const ACTION_COMATE_PLUS_AGENT_COMMAND = 'COMATE_PLUS_AGENT_COMMAND';
export const ACTION_COMATE_PLUS_WEBVIEW_INIT_DATA = 'COMATE_PLUS_WEBVIEW_INIT_DATA';
export const ACTION_COMATE_PLUS_DRAW_CHAT_UPDATE = 'COMATE_PLUS_DRAW_CHAT_UPDATE';
export const ACTION_COMATE_PLUS_DRAW_CHAT_APPEND = 'COMATE_PLUS_DRAW_CHAT_APPEND';
export const ACTION_COMATE_PLUS_DRAW_CHAT_FINISH = 'COMATE_PLUS_DRAW_CHAT_FINISH';
export const ACTION_COMATE_PLUS_DRAW_CHAT_FAIL = 'COMATE_PLUS_DRAW_CHAT_FAIL';
export const ACTION_COMATE_PLUS_REQUEST_PERMISSION = 'REQUEST_PERMISSION';
export const ACTION_COMATE_PLUS_QUERY_SELECTOR = 'COMATE_PLUS_QUERY_SELECTOR';
export const ACTION_USER_DETAIL = 'USER_DETAIL';
export const ACTION_COMATE_PLUS_INITIALIZED = 'COMATE_PLUS_INITIALIZED';
export const ACTION_ENGINE_PROCESS_START_SUCCESS = 'ENGINE_PROCESS_START_SUCCESS';
export const ACTION_USER_DETAIL_ERROR = 'USER_DETAIL_ERROR'; // notification
export const ACTION_COMATE_PLUS_AGENT_NOTIFICATION = 'COMATE_PLUS_AGENT_NOTIFICATION'; // notification
export const ACTION_COMATE_LSP_WORKSPACE_FOLDERS = 'COMATE_LSP_WORKSPACE_FOLDERS';
export const ACTION_COMATE_LSP_REMOTE_CONSOLE = 'COMATE_LSP_REMOTE_CONSOLE';
export const ACTION_COMATE_PLUS_SECTION_CHAT_UPDATE = 'COMATE_PLUS_SECTION_CHAT_UPDATE';
export const ACTION_COMATE_PLUS_RECREATE_INDEX = 'COMATE_PLUS_RECREATE_INDEX';
export const ACTION_COMATE_PLUS_CODE_SEARCH = 'COMATE_PLUS_CODE_SEARCH';
export const ACTION_COMATE_PLUS_BATCH_ACCEPT = 'COMATE_PLUS_ACCEPT_DIR';
// 智能采纳
export const ACTION_COMATE_SMART_APPLY = 'ACTION_COMATE_SMART_APPLY';
// 会话管理，即历史消息的管理
export const ACTION_CHAT_SESSION_LIST = 'kernel/chatSession/list';
export const ACTION_CHAT_SESSION_SAVE = 'kernel/chatSession/save';
export const ACTION_CHAT_SESSION_DELETE = 'kernel/chatSession/delete';
export const ACTION_CHAT_SESSION_FIND = 'kernel/chatSession/find';

// 以下是智能体相关事件

/** 获取全部智能体任务 */
export const ACTION_COMATE_GET_AGENT_TASKS = 'COMATE_GET_AGENT_TASKS';
/** 新增智能体任务 */
export const ACTION_COMATE_ADD_AGENT_TASK = 'COMATE_ADD_AGENT_TASK';
/** 设置某个智能体任务到前台 */
export const ACTION_COMATE_SET_FOREGROUND_TASK = 'COMATE_SET_FOREGROUND_TASK';
/** 更新前台智能体消息 */
export const ACTION_COMATE_UPDATE_AGENT_TASK_MESSAGES = 'COMATE_UPDATE_AGENT_TASK_MESSAGES';
export const ACTION_MOCK_VIRTUAL_EDITOR_EVENT = 'ACTION_MOCK_VIRTUAL_EDITOR_EVENT';
export const ACTION_COMPOSER = 'ACTION_COMPOSER';
/** Debug 智能体自定义事件 */
export const AGENT_DEBUG_CUSTOM_ACTION = 'COMATE_AGENT_DEBUG_CUSTOM_ACTION';

// 以上是智能体相关事件

/** 增加缓存 */
export const ACTION_COMATE_ADD_CACHE = 'COMATE_ADD_CACHE';
/** 获取缓存数据 */
export const ACTION_COMATE_GET_CACHE = 'COMATE_GET_CACHE';

// 获取全部自定义Prompt
export const ACTION_PROMPTTEMPLATE_LIST = 'kernel/promptTemplate/list';
// 新建自定义Prompt
export const ACTION_PROMPTTEMPLATE_CREATE = 'kernel/promptTemplate/create';
// 新建自定义Prompt
export const ACTION_PROMPTTEMPLATE_UPDATE = 'kernel/promptTemplate/update';
// 删除自定义Prompt
export const ACTION_PROMPTTEMPLATE_DELETE = 'kernel/promptTemplate/delete';

export const ACTION_ENGINE_SERVICE_INITIALIZED = 'ENGINE_SERVICE_INITIALIZED';

/** 规则面板触发的操作的事件 */
export const RULE_PANEL_ACTION_EVENT = 'RULE_PANEL_ACTION_EVENT';

/** 更新规则面板界面的事件 */
export const UPDATE_RULE_PANEL_EVENT = 'UPDATE_RULE_PANEL_EVENT';
