/* eslint-disable no-param-reassign */
/* eslint-disable @typescript-eslint/await-thenable */
/* eslint-disable @typescript-eslint/no-floating-promises */
import {Tokens, marked} from 'marked';
import {basename} from 'path';

const replaceMarkdownPathText = (content: string, paths: string[]) => {
    const basenames = paths.map(path => basename(path));
    // 过滤掉出现超过1次的和原始路径里出现过的
    const filterDuplicate = basenames.filter(basename =>
        !paths.includes(basename) && basenames.filter(b => b === basename).length === 1
    );

    const pathsSortedByLength = [...paths, ...filterDuplicate].sort((a, b) => b.length - a.length);

    let input = content;
    let matched = false;
    for (const [i, path] of Object.entries(pathsSortedByLength)) {
        matched = matched || input.includes(path);
        input = input.replaceAll(path, `@@PLACEHOLDER_${i}@@`);
    }

    if (!matched) {
        throw new Error('Input did not match any path');
    }

    return input
        .replaceAll(/([ ]?)@@PLACEHOLDER_([0-9]+)@@([ \n]?)/g, (...args) => {
            const i = Number(args[2]);
            const charAfter = args[3];
            const path = pathsSortedByLength[i];
            if (charAfter === '\n') {
                return ` [${path}](${path})\n`;
            }
            else {
                return ` [${path}](${path}) `;
            }
        })
        .replace(/ $/, '');
};

const processParagraphNode = (token: Tokens.Paragraph | Tokens.Generic, paths: string[]) => {
    const children = token.tokens || [];
    const hasEOL = token.raw.endsWith('\n');

    let replaced = false;
    const newTokenRaw = children.reduce<string>(
        // eslint-disable-next-line complexity
        (acc, token) => {
            if (token.type === 'text') {
                if ((token as Tokens.Paragraph).tokens?.length) {
                    const result = processParagraphNode(token, paths);
                    replaced = replaced || result.replaced;
                }
                else {
                    try {
                        token.raw = replaceMarkdownPathText(token.raw, paths);
                        replaced = true;
                    }
                    catch (ex) {
                        //
                    }
                }
            }
            else if (token.type === 'codespan') {
                const matched = /^\[([^\]]+)\]\(([^)]+)\)$/.exec(token.text);
                if (matched && matched[1] && matched[2]) {
                    replaced = true;
                    token.raw = `[\`${matched[1]}\`](${matched[2]})`;
                }
                else if (/^http(s?):\/\//.test(token.text)) {
                    replaced = true;
                    token.raw = `[\`${token.text}\`](${token.text})`;
                }
                else if (token.raw.includes('.')) {
                    const matched = paths.find(path => path === token.text);
                    if (matched) {
                        replaced = true;
                        token.raw = `[\`${token.text}\`](${matched})`;
                    }
                }
            }
            return acc + token.raw;
        },
        ''
    );

    if (!replaced) {
        return {replaced, token};
    }

    token.raw = newTokenRaw;
    if (hasEOL) {
        token.raw += '\n';
    }
    return {replaced, token};
};

const processListNode = (token: Tokens.List, paths: string[], depth: number = 0) => {
    const indent = '  ';
    const items = token.items || [];
    const hasEOL = token.raw.endsWith('\n');
    let replaced = false;
    const newTokenRaw = items
        .map(item => {
            // 匹配列表前缀
            const match = /^(((\d+\.)|-|[a-z]\.|\*)[ ]?)(.*)/.exec(item.raw);
            const raw = item
                .tokens
                .map(token => {
                    if (token.type === 'text') {
                        const result = processParagraphNode(item, paths);
                        replaced = replaced || result.replaced;
                    }
                    else if (token.type === 'list') {
                        const result = processListNode(token as Tokens.List, paths, depth + 1);
                        replaced = replaced || result.replaced;
                    }
                    else if (token.type === 'codespan') {
                        const result = processParagraphNode(item, paths);
                        replaced = replaced || result.replaced;
                    }
                    return token.raw;
                })
                .join('');

            if (match) {
                const prefix = match[1];
                const result = processParagraphNode(item, paths);
                replaced = replaced || result.replaced;
                return indent.repeat(depth) + prefix + raw;
            }

            return raw;
        })
        .join('\n');
    if (!replaced) {
        return {replaced, token};
    }

    token.raw = newTokenRaw;
    if (hasEOL) {
        token.raw += '\n';
    }
    return {replaced, token};
};

export const replacePathTextInMarkdown = async (content: string, paths: string[]) => {
    if (!paths.length) {
        return content;
    }

    const tokens = marked.lexer(content);
    await marked.walkTokens(tokens, token => {
        if (token.type === 'paragraph') {
            processParagraphNode(token, paths);
        }
        else if (token.type === 'list') {
            processListNode(token as Tokens.List, paths);
        }
    });
    const summary = tokens.map(token => token.raw).join('');
    return summary;
};
