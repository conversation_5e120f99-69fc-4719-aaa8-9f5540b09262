/* eslint-disable max-depth */
import {IncomingMessage} from 'http';
import * as vscode from 'vscode';

function splitChunk(text: string) {
    const lines = text.split('\n');
    const last = lines.pop() ?? '';
    return [lines.filter(line => line !== ''), last] as const;
}

const defaultParser = (chunk: string) => JSON.parse(chunk);
export class SSEProcessor<T> {
    error: boolean = false;
    errorMsg: string = '';

    constructor(
        private readonly body: IncomingMessage,
        private readonly parse: (chunk: string) => T = defaultParser,
        private readonly cancellationToken?: vscode.CancellationToken
    ) {}

    async *processSSE(): AsyncGenerator<T, void, void> {
        let lastRemainingData = '';
        const decoder = new TextDecoder();
        try {
            for await (const data of this.body) {
                const decodedData = decoder.decode(data, {stream: true});
                if (this.maybeCancel()) {
                    return;
                }
                const [items, remaining] = splitChunk(lastRemainingData + decodedData);
                lastRemainingData = remaining;
                for (const item of items) {
                    // 心跳事件忽略，本来想判断不是 data: 开头就忽略的，但这个方法用的太多了，不敢改
                    if (item.startsWith(':heartbeat')) {
                        continue;
                    }
                    const text = item.replace(/^data:/, '').trim();
                    if (text === '') {
                        continue;
                    }
                    try {
                        const res = this.parse(text);
                        if (res) {
                            yield res;
                        }
                    }
                    catch (e: any) {
                        this.cancel();
                        this.error = true;
                        this.errorMsg = text;
                        return;
                    }
                }
            }
        }
        catch (e: any) {
            if (this.maybeCancel()) {
                return;
            }
            throw e;
        }
    }

    maybeCancel() {
        const cancel = !!this.cancellationToken?.isCancellationRequested;
        if (cancel) {
            this.cancel();
        }
        return cancel;
    }

    cancel() {
        this.body.destroy();
    }
}
