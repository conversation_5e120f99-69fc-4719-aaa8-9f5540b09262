export enum PLATFORM {
    SAAS = 'saas',
    INTERNAL = 'internal',
    POC = 'poc',
}

export enum ENVIRONMENT {
    PRODUCTION = 'production',
    DEVELOPMENT = 'development',
    TEST = 'test',
}

export enum WEBVIEW_CONSUMER {
    VSCODE = 'vscode',
    JETBRAINS = 'jetbrains',
}

export interface $features {
    PLATFORM: PLATFORM;
    WEBVIEW_CONSUMER: WEBVIEW_CONSUMER;
    ENVIRONMENT: ENVIRONMENT;
}
