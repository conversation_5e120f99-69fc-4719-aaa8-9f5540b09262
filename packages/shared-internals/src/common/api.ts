import axios, {CreateAxiosDefaults} from 'axios';
import {ENVIRONMENT, PLATFORM} from './features.js';
export const SAAS_API_HOST = 'https://comate.baidu.com';
export const INTERNAL_API_HOST = 'https://comate.baidu-int.com';
export const SAAS_TEST_API_HOST = 'https://comate-cop.now.baidu.com';
export const INTERNAL_TEST_API_HOST = 'https://comate-test.now.baidu.com';

let platform: PLATFORM = process.env.PLATFORM as PLATFORM || PLATFORM.INTERNAL;
let environment: ENVIRONMENT = process.env.ENVIRONMENT as ENVIRONMENT || ENVIRONMENT.PRODUCTION;

const API_MAP = {
    [PLATFORM.SAAS]: {
        [ENVIRONMENT.PRODUCTION]: SAAS_API_HOST,
        [ENVIRONMENT.DEVELOPMENT]: SAAS_API_HOST,
        [ENVIRONMENT.TEST]: SAAS_TEST_API_HOST,
    },
    [PLATFORM.INTERNAL]: {
        [ENVIRONMENT.PRODUCTION]: INTERNAL_API_HOST,
        [ENVIRONMENT.DEVELOPMENT]: INTERNAL_API_HOST,
        [ENVIRONMENT.TEST]: INTERNAL_TEST_API_HOST,
    },
    // TODO 暂时没用到
    [PLATFORM.POC]: {
        [ENVIRONMENT.PRODUCTION]: SAAS_API_HOST,
        [ENVIRONMENT.DEVELOPMENT]: SAAS_API_HOST,
        [ENVIRONMENT.TEST]: SAAS_TEST_API_HOST,
    },
};

export function setPlatform(newPlatform: PLATFORM) {
    platform = newPlatform;
}

export function setPlatformAndEnvironment(newPlatform: PLATFORM, newEnvironment: ENVIRONMENT) {
    platform = newPlatform;
    environment = newEnvironment;
}

let httpHost = process.env.HTTP_HOST;

export function setApiHost(newHttpHost: string) {
    httpHost = newHttpHost;
}

export const getApiHost = () => {
    if (httpHost) {
        return httpHost;
    }
    return API_MAP?.[platform]?.[environment] ?? INTERNAL_API_HOST;
};

// 解析代理URL的工具函数
function parseProxyUrl(proxyUrl: string) {
    const defaults = {
        protocol: 'http',
        hostname: 'localhost',
        port: '9090',
    };

    if (!proxyUrl) {
        return defaults;
    }

    try {
        const urlWithoutProtocol = proxyUrl.replace(/^\w+:\/\//, '');
        const [hostname, port] = urlWithoutProtocol.split(':');
        const protocol = (/^(\w+):\/\//.exec(proxyUrl))?.[1] || defaults.protocol;

        return {
            protocol,
            hostname: hostname || defaults.hostname,
            port: port || defaults.port,
        };
    }
    catch (error) {
        console.error('Error parsing proxy URL:', error);
        return defaults;
    }
}

export const API_HOST = platform === PLATFORM.SAAS ? SAAS_API_HOST : INTERNAL_API_HOST;

const config: CreateAxiosDefaults = {
    // httpAgent: new http.Agent({
    //     keepAlive: true,
    //     keepAliveMsecs: 300000, // 禁用超时
    //     timeout: 0,
    // }),
    // httpsAgent: new https.Agent({
    //     keepAlive: true,
    //     keepAliveMsecs: 300000, // 禁用超时
    //     timeout: 0,
    // }),
};
const proxyURL = process.env.HTTP_PROXY_URL || '';
// 暂时注释
// const noProxyStr = process.env.HTTP_NO_PROXY_URL || '';
const {protocol, hostname: host, port} = parseProxyUrl(proxyURL);

// 加了proxy，除此之外没区别
export const axiosInstance = axios.create(config);
export const knowledgeAxiosInstance = axios.create(config);
axiosInstance.interceptors.request.use(async config => {
    const requestConfig = {
        ...config,
        baseURL: getApiHost(),
    };
    if (proxyURL) {
        requestConfig.proxy = {
            host,
            port: Number(port),
            protocol,
        };
        /* eslint-disable camelcase */
        // @ts-ignore 需要merge axios http_proxy原有字段
        // requestConfig.http_proxy = {
        //     host,
        //     port: Number(port),
        //     protocol,
        // };
    }

    // if (noProxyStr) {
    //     // @ts-ignore 需要merge axios no_proxy原有字段
    //     requestConfig.no_proxy = noProxyStr;

    // }
    return requestConfig;
});
// 补一些类型
export type * as Axios from 'axios';

// 实例
export const createAxiosInstance = (config?: CreateAxiosDefaults) => {
    return axios.create(config);
};

export const createAxiosCancelTokenSource = () => {
    return axios.CancelToken.source();
};

export const CanceledError = axios.CanceledError;
