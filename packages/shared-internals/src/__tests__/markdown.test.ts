/* eslint-disable max-len */
import {describe, test, expect} from 'vitest';
import {replacePathTextInMarkdown} from '../common/markdown.js';

describe('markdown.replacePathTextInMarkdown', () => {
    test('如果没有路径，返回原始字符串', async () => {
        const input = '@@@@@@\n@@@@@@';
        const result = await replacePathTextInMarkdown(input, []);
        expect(result).toBe(input);
    });

    test('如果有路径，返回替换后的字符串', async () => {
        const input = '@@@@@@ README.md\n@@@@@@';
        const expectOutput = '@@@@@@ [README.md](README.md)\n@@@@@@';
        const result = await replacePathTextInMarkdown(input, ['README.md']);
        expect(result).toBe(expectOutput);
    });

    test('支持替换链接后前后补齐空格', async () => {
        const input = '@@@@@@README.md@@@@@@';
        const expectOutput = '@@@@@@ [README.md](README.md) @@@@@@';
        const result = await replacePathTextInMarkdown(input, ['README.md']);
        expect(result).toBe(expectOutput);
    });

    test('如果包含多级路径，返回替换后的字符串', async () => {
        const input = '@@@@@@ package/core/README.md\n@@@@@@';
        const expectOutput = '@@@@@@ [package/core/README.md](package/core/README.md)\n@@@@@@';
        const result = await replacePathTextInMarkdown(input, ['package/core/README.md']);
        expect(result).toBe(expectOutput);
    });

    test('如果包含多个路径，返回替换后的字符串', async () => {
        const input = '@@@@@@ package/core/README.md\n@@@@@@ package/core/index.ts';
        const expectOutput
            = '@@@@@@ [package/core/README.md](package/core/README.md)\n@@@@@@ [package/core/index.ts](package/core/index.ts)';
        const result = await replacePathTextInMarkdown(input, ['package/core/README.md', 'package/core/index.ts']);
        expect(result).toBe(expectOutput);
    });

    test('如果包含父子层级路径，仅替换父路径，且不被二次替换', async () => {
        const input = '@@@@@@ package/core/README.md\n@@@@@@ README.md';
        const expectOutput = '@@@@@@ [package/core/README.md](package/core/README.md)\n@@@@@@ README.md';
        const result = await replacePathTextInMarkdown(input, ['package/core/README.md', 'package/cli/README.md']);
        expect(result).toBe(expectOutput);
    });

    test('如果包含段落和列表，也能被正常替换', async () => {
        const input = '@@@@@@ README.md\n- @@@@@@ README.md\n- @@@@@@ README.md';
        const expectOutput
            = '@@@@@@ [README.md](README.md)\n- @@@@@@ [README.md](README.md)\n- @@@@@@ [README.md](README.md)';
        const result = await replacePathTextInMarkdown(input, ['README.md']);
        expect(result).toBe(expectOutput);
    });

    test('如果段落里本身包含链接，则不能被影响到', async () => {
        const input = '@@@@@@ [README.md](src/README.md)';
        const expectOutput = input;
        const result = await replacePathTextInMarkdown(input, ['README.md']);
        expect(result).toBe(expectOutput);
    });

    test('如果段落里本身包含代码，也需要替换', async () => {
        const input = '@@@@@@ `src/README.md`';
        const expectOutput = '@@@@@@ [`src/README.md`](src/README.md)';
        const result = await replacePathTextInMarkdown(input, ['src/README.md']);
        expect(result).toBe(expectOutput);
    });

    test('如果codespan节点中的链接需要豁免', async () => {
        const input = '@@@@@@ `[src/README.md](src/README.md)`';
        const expectOutput = '@@@@@@ [`src/README.md`](src/README.md)';
        const result = await replacePathTextInMarkdown(input, ['src/README.md']);
        expect(result).toBe(expectOutput);
    });

    test('如果codespan节点中路径还包含其它字符串，不替换', async () => {
        const input = '@@@@@@ `cat src/README.md`';
        const expectOutput = input;
        const result = await replacePathTextInMarkdown(input, ['src/README.md']);
        expect(result).toBe(expectOutput);
    });

    test('如果包含其它不处理的段落节点，可以join回来', async () => {
        const input = '@@@@@@ `cat src/README.md`\n```javascript\nconsole.log("src/README.md")\n```\n';
        const expectOutput = input;
        const result = await replacePathTextInMarkdown(input, ['src/README.md']);
        expect(result).toBe(expectOutput);
    });

    test('如果codespan节点中的链接需要豁免', async () => {
        const input = '@@@@@@ `[src/README.md](src/README.md)`';
        const expectOutput = '@@@@@@ [`src/README.md`](src/README.md)';
        const result = await replacePathTextInMarkdown(input, ['src/README.md']);
        expect(result).toBe(expectOutput);
    });

    test('如果codespan节点中包含http格式的链接，替换成markdown链接', async () => {
        const input = '@@@@@@ `https://localhost:8080`';
        const expectOutput = '@@@@@@ [`https://localhost:8080`](https://localhost:8080)';
        const result = await replacePathTextInMarkdown(input, ['README.md']);
        expect(result).toBe(expectOutput);
    });

    test('支持嵌套列表替换', async () => {
        const input = '- List 1\n  - List 1.1\n    - List README.md';
        const expectOutput = '- List 1\n  - List 1.1\n    - List [README.md](README.md)';
        const result = await replacePathTextInMarkdown(input, ['README.md']);
        expect(result).toBe(expectOutput);
    });

    test('真实case: 列表替换', async () => {
        const input
            = '从`Timeline.tsx`文件分析，视频标注的时间轴控制实现如下：\n\n1. **时间轴核心功能**：\n- 帧精确控制：支持按帧(`hopSize`)或自定义步长(`step`)跳转\n- 播放状态管理：集成播放/暂停控制(`onPlay/onPause`)\n- 区域标注支持：提供`regions`属性管理标注区域\n\n2. **关键交互逻辑**：\n```typescript\n// 帧跳转实现\nconst setInternalPosition \u003d (newPosition: number) \u003d\u003e {\n    setCurrentPosition(clamp(newPosition, 1, length));\n    handlers.onPositionChange?.(clampedValue);\n}\n\n// 前进/后退控制\nconst increasePosition \u003d () \u003d\u003e setInternalPosition(currentPosition + hopSize);\nconst decreasePosition \u003d () \u003d\u003e setInternalPosition(currentPosition - hopSize);\n```\n\n3. **视图控制**：\n- 支持缩放(`zoom`)和滚动(`seekOffset`)\n- 可折叠视图(`viewCollapsed`状态)\n- 多种显示模式(`mode`参数支持\u0027frames\u0027等视图)\n\n4. **标注操作回调**：\n```typescript\n{\n    onStartDrawing: (frame) \u003d\u003e {}  // 开始标注\n    onDrawing: (id, frames) \u003d\u003e {}  // 标注中\n    onFinishDrawing: () \u003d\u003e {}      // 结束标注\n    onAddRegion: (reg) \u003d\u003e {}       // 添加区域\n    onDeleteRegion: (id) \u003d\u003e {}      // 删除区域\n}\n```\n\n5. **性能优化**：\n- 使用`useMemoizedHandlers`缓存回调函数\n- 通过`requestAnimationFrame`实现流畅更新\n- 区域数据观察(`fixMobxObserve`处理MobX响应式数据)\n\n完整的视频标注流程总结：\n1. `VideoCanvas`负责视频解码和基础播放控制\n2. 通过`currentFrame`和`length`同步视频帧与标注位置\n3. `Timeline`组件提供精细的帧级控制能力\n4. 标注操作通过回调函数与业务逻辑交互\n5. 标注数据存储在`regions`属性中\n\n需要查看标注数据的具体结构吗？我可以继续分析相关区域管理组件。';
        const expectOutput = input;
        const result = await replacePathTextInMarkdown(input, ['src/README.md']);
        expect(result).toBe(expectOutput);
    });

    test('真实case: 一个节点里有多个路径时，替换失败', async () => {
        const input = '3. 整体架构：\n- 前端三层结构：\n  * 表现层(index.html + style.css)\n  * 逻辑层(game.js)';
        const expectOutput
            = '3. 整体架构：\n- 前端三层结构：\n  * 表现层( [index.html](index.html) + [style.css](style.css) )\n  * 逻辑层(game.js)';
        const result = await replacePathTextInMarkdown(input, ['index.html', 'style.css']);
        expect(result).toBe(expectOutput);
    });
});
