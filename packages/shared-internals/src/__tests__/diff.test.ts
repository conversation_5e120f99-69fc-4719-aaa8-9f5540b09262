// 可以单独执行 ./node_modules/.bin/vitest packages/vscode/src/services/Composer/__tests__/
import path from 'node:path';
import {fileURLToPath} from 'node:url';
import {readFileSync, readdirSync} from 'node:fs';
import {describe, test, expect} from 'vitest';
import {fixUdiffLineMarker} from '../bridge/diff.js';

const filename = fileURLToPath(import.meta.url);
const dirname = path.dirname(filename);

const extractDiffsFromContent = (testcase: string) => {
    const lines = testcase.split('\n');
    const [original, diff, newContent] = lines.reduce<string[][]>(
        (result, line) => {
            if (line.startsWith('========')) {
                result.push([]);
            }
            else {
                result[result.length - 1].push(line);
            }
            return result;
        },
        []
    );
    return {original: original.join('\n'), diff: diff.join('\n'), newContent: newContent.join('\n')};
};

const submodules = readdirSync(path.join(dirname, 'diff_cases'));
const cases = submodules.filter(name => name.startsWith('case')).map(submodule => {
    const testcase = readFileSync(path.join(dirname, 'diff_cases', submodule), 'utf-8');
    return {name: submodule, ...extractDiffsFromContent(testcase)};
});

describe('Composer', () => {
    for (const testcase of cases) {
        const {name, original, diff, newContent} = testcase;
        test(`fixUdiffLineMarker.${name}`, () => {
            expect(fixUdiffLineMarker(diff, original)).toEqual(newContent);
        });
    }
});
