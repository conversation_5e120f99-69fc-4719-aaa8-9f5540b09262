======== original
<?php
$page_title = '新建报价单';
check_permission();

// 获取当前登录用户信息
$salesperson = $_SESSION['admin_real_name'] ?? $_SESSION['admin_username'] ?? '系统管理员';

// 处理复制报价单逻辑
$copy_from = isset($_GET['copy_from']) ? (int)$_GET['copy_from'] : 0;
$original_quotation = null;
$original_items = [];

if ($copy_from > 0) {
    $original_quotation = db_fetch_one(db_query(
        "SELECT * FROM quotations WHERE id = ?",
        [$copy_from]
    ));

    if ($original_quotation) {
        $original_items = db_fetch_all(db_query(
            "SELECT * FROM quotation_items WHERE quotation_id = ?",
            [$copy_from]
        ));
    }
}

// 自动生成报价单号
$quotation_no = 'BJ' . date('Ymd') . substr(uniqid(), -4);

// 如果是复制操作，预填表单数据
if ($original_quotation) {
    $_POST['project_name'] = $original_quotation['project_name'] . ' (副本)';
    $_POST['customer_id'] = $original_quotation['customer_id'];
    $_POST['quotation_date'] = date('Y-m-d');
    // 强制设置税率，确保不会被覆盖
    $_POST['tax_rate'] = $original_quotation['tax_rate'];
    $_REQUEST['tax_rate'] = $original_quotation['tax_rate'];
    $_GET['tax_rate'] = $original_quotation['tax_rate'];
    $_SESSION['temp_tax_rate'] = $original_quotation['tax_rate'];
    $_POST['remark'] = $original_quotation['remark'];

    // 调试日志
    error_log("Copy quotation: Setting tax_rate to ".$original_quotation['tax_rate']);
}

// 处理AJAX请求
if (isset($_GET['customer_search'])) {
    $search = clean_input($_GET['customer_search']);
    $customers = db_fetch_all(db_query("SELECT id, name FROM customers WHERE name LIKE ? ORDER BY name LIMIT 10", ["%{$search}%"]));
    header('Content-Type: application/json');
    echo json_encode($customers);
    exit;
}

if (isset($_GET['product_search'])) {
    $search = clean_input($_GET['product_search']);
    $products = db_fetch_all(db_query("SELECT id, name, spec, unit, price FROM products WHERE name LIKE ? OR spec LIKE ? ORDER BY name LIMIT 10", ["%{$search}%", "%{$search}%"]));
    header('Content-Type: application/json');
    echo json_encode($products);
    exit;
}

// 正常页面请求
$customers = db_fetch_all(db_query("SELECT id, name FROM customers ORDER BY name LIMIT 10"));
$products = db_fetch_all(db_query("SELECT id, name, spec, unit, price FROM products ORDER BY name LIMIT 10"));

// 如果是复制操作，预加载客户信息
if ($original_quotation && !isset($_POST['customer_id'])) {
    $selected_customer = db_fetch_one(db_query(
        "SELECT id, name FROM customers WHERE id = ?",
        [$original_quotation['customer_id']]
    ));
}

if (is_post()) {
    // 调试日志：打印所有POST数据
    error_log("Form submission POST data: " . print_r($_POST, true));

    $project_name = clean_input($_POST['project_name']);
    $customer_id = (int)$_POST['customer_id'];
    $quotation_date = $_POST['quotation_date'];
    $tax_rate = (float)$_POST['tax_rate'];
    $remark = clean_input($_POST['remark'] ?? '');

    // 调试日志：打印关键字段值
    error_log("Processing submission with tax_rate: $tax_rate");

    // 验证输入
    $errors = [];
    if (empty($project_name)) {
        $errors[] = '项目名称不能为空';
    }
    if ($customer_id <= 0) {
        $errors[] = '请选择客户';
    }
    if (empty($quotation_date)) {
        $errors[] = '报价日期不能为空';
    }
    if ($tax_rate < 0 || $tax_rate > 100) {
        $errors[] = '税率必须在0-100之间';
    }

    // 验证产品项
    $items = [];
    if (isset($_POST['product_id']) && is_array($_POST['product_id'])) {
        foreach ($_POST['product_id'] as $index => $product_id) {
            $quantity = (int)$_POST['quantity'][$index];
            if ($product_id > 0 && $quantity > 0) {
                $items[] = [
                    'product_id' => $product_id,
                    'product_name' => $_POST['product_name'][$index],
                    'spec' => $_POST['spec'][$index],
                    'unit' => $_POST['unit'][$index],
                    'price' => (float)$_POST['price'][$index],
                    'quantity' => $quantity,
                    'amount' => (float)$_POST['amount'][$index],
                    'remark' => clean_input($_POST['item_remark'][$index] ?? '')
                ];
            }
        }
    }

    if (empty($items)) {
        $errors[] = '至少需要一个有效的产品项';
    }

    if (empty($errors)) {
        // 计算金额
        $subtotal = array_reduce($items, function($carry, $item) {
            return $carry + $item['amount'];
        }, 0);

        $tax_amount = round($subtotal * $tax_rate / 100, 2);
        $total_amount = $subtotal + $tax_amount;

        // 开启事务
        db_query("START TRANSACTION");

        try {
            // 插入报价单主表
            $sql = "INSERT INTO quotations (
                quotation_no, project_name, quotation_date, customer_id, salesperson,
                remark, status, subtotal, tax_rate, tax_amount, total_amount
            ) VALUES (?, ?, ?, ?, ?, ?, 'pending', ?, ?, ?, ?)";

            $result = db_query($sql, [
                $quotation_no, $project_name, $quotation_date, $customer_id, $salesperson,
                $remark, $subtotal, $tax_rate, $tax_amount, $total_amount
            ]);

            $quotation_id = db_insert_id();

            // 调试日志：打印最终保存的税率和金额
            error_log("Saved quotation with tax_rate: $tax_rate, subtotal: $subtotal, tax_amount: $tax_amount, total_amount: $total_amount");

            // 插入报价单明细
            foreach ($items as $item) {
                $sql = "INSERT INTO quotation_items (
                    quotation_id, product_id, product_name, spec, unit,
                    price, quantity, amount, remark
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

                db_query($sql, [
                    $quotation_id, $item['product_id'], $item['product_name'], $item['spec'], $item['unit'],
                    $item['price'], $item['quantity'], $item['amount'], $item['remark']
                ]);
            }

            // 提交事务
            db_query("COMMIT");

            show_message('报价单创建成功', 'success');
            redirect(get_admin_url('/index.php?page=quotations/list'));

        } catch (Exception $e) {
            // 回滚事务
            db_query("ROLLBACK");
            show_message('报价单创建失败: ' . $e->getMessage(), 'danger');
        }
    } else {
        show_message(implode('<br>', $errors), 'danger');
    }
}
?>

<link rel="stylesheet" href="<?php echo get_admin_url('/public/adminlte/plugins/select2/css/select2.min.css'); ?>">
<link rel="stylesheet" href="<?php echo get_admin_url('/public/adminlte/plugins/select2/css/custom-select2.css'); ?>">
<div class="card">
    <div class="card-header">
        <h3 class="card-title">新建报价单</h3>
    </div>
    <div class="card-body">
        <form method="post" id="quotation-form">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="quotation_no">报价单号 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="quotation_no" name="quotation_no" value="<?php echo $quotation_no; ?>" readonly>
                    </div>
                    <div class="form-group">
                        <label for="project_name">项目名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="project_name" name="project_name" value="<?php echo isset($_POST['project_name']) ? safe_output($_POST['project_name']) : ''; ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="customer_id">客户 <span class="text-danger">*</span></label>
                        <select class="form-control select2" id="customer_id" name="customer_id" required>
                            <option value="">请选择客户</option>
                            <?php foreach ($customers as $customer): ?>
                                <option value="<?php echo $customer['id']; ?>" <?php echo isset($_POST['customer_id']) && $_POST['customer_id'] == $customer['id'] ? 'selected' : ''; ?>>
                                    <?php echo safe_output($customer['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="quotation_date">报价日期 <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="quotation_date" name="quotation_date" value="<?php echo isset($_POST['quotation_date']) ? $_POST['quotation_date'] : date('Y-m-d'); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="salesperson">报价人</label>
                        <input type="text" class="form-control" id="salesperson" name="salesperson" value="<?php echo $salesperson; ?>" readonly>
                    </div>
                    <div class="form-group">
                        <label for="tax_rate">税率 <span class="text-danger">*</span></label>
                        <select class="form-control select2" id="tax_rate" name="tax_rate" required>
                            <?php
                            $tax_rates = db_fetch_all(db_query("SELECT * FROM tax_rates ORDER BY is_default DESC, rate DESC"));
                            foreach ($tax_rates as $rate): ?>
                                <option value="<?php echo $rate['rate']; ?>" <?php echo $rate['is_default'] ? 'selected' : ''; ?>>
                                    <?php echo safe_output($rate['name']); ?> (<?php echo $rate['rate']; ?>%)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>产品明细</label>
                <div class="table-responsive">
                    <table class="table table-bordered" id="items-table">
                        <thead>
                            <tr>
                                <th width="30%">产品名称</th>
                                <th width="15%">规格型号</th>
                                <th width="10%">单位</th>
                                <th width="10%">单价</th>
                                <th width="10%">数量</th>
                                <th width="10%">金额</th>
                                <th width="10%">备注</th>
                                <th width="5%"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($original_items)): ?>
                                <?php foreach ($original_items as $item): ?>
                                <tr>
                                    <td>
                                        <select class="form-control product-select" name="product_id[]" required
                                            data-placeholder="搜索并选择产品" style="width: 100%;"
                                            data-product-id="<?php echo $item['product_id']; ?>"
                                            data-product-name="<?php echo safe_output($item['product_name']); ?>"
                                            data-spec="<?php echo safe_output($item['spec']); ?>"
                                            data-unit="<?php echo safe_output($item['unit']); ?>"
                                            data-price="<?php echo $item['price']; ?>">
                                        </select>
                                        <input type="hidden" name="product_name[]" value="<?php echo safe_output($item['product_name']); ?>">
                                    </td>
                                    <td><input type="text" class="form-control spec" name="spec[]" value="<?php echo safe_output($item['spec']); ?>"></td>
                                    <td><input type="text" class="form-control unit" name="unit[]" value="<?php echo safe_output($item['unit']); ?>"></td>
                                    <td><input type="number" class="form-control price" name="price[]" value="<?php echo $item['price']; ?>" min="0" step="0.01"></td>
                                    <td><input type="number" class="form-control quantity" name="quantity[]" min="1" value="<?php echo $item['quantity']; ?>" required></td>
                                    <td><input type="number" class="form-control amount" name="amount[]" value="<?php echo $item['amount']; ?>" readonly></td>
                                    <td><input type="text" class="form-control" name="item_remark[]" value="<?php echo safe_output($item['remark']); ?>"></td>
                                    <td><button type="button" class="btn btn-danger btn-sm remove-item"><i class="fas fa-trash"></i></button></td>
                                </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td>
                                        <select class="form-control product-select" name="product_id[]" required
                                            data-placeholder="搜索并选择产品" style="width: 100%;">
                                        </select>
                                        <input type="hidden" name="product_name[]">
                                    </td>
                                    <td><input type="text" class="form-control spec" name="spec[]"></td>
                                    <td><input type="text" class="form-control unit" name="unit[]"></td>
                                    <td><input type="number" class="form-control price" name="price[]" min="0" step="0.01"></td>
                                    <td><input type="number" class="form-control quantity" name="quantity[]" min="1" value="1" required></td>
                                    <td><input type="number" class="form-control amount" name="amount[]" readonly></td>
                                    <td><input type="text" class="form-control" name="item_remark[]"></td>
                                    <td><button type="button" class="btn btn-danger btn-sm remove-item"><i class="fas fa-trash"></i></button></td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="8">
                                    <button type="button" class="btn btn-primary btn-sm" id="add-item">
                                        <i class="fas fa-plus"></i> 添加产品
                                    </button>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="remark">报价备注</label>
                        <textarea class="form-control" id="remark" name="remark" rows="3"><?php echo isset($_POST['remark']) ? safe_output($_POST['remark']) : ''; ?></textarea>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>金额统计</label>
                        <table class="table table-bordered">
                            <tr>
                                <th width="50%">金额小计</th>
                                <td width="50%" id="subtotal">0.00</td>
                            </tr>
                            <tr>
                                <th>税额</th>
                                <td id="tax-amount">0.00</td>
                            </tr>
                            <tr>
                                <?php if (isset($is_tax_included) && $is_tax_included): ?>
                                    <th>含税总金额</th>
                                <?php else: ?>
                                    <th>未税总金额</th>
                                <?php endif; ?>
                                <td id="total-amount">0.00</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <button type="submit" class="btn btn-primary">保存</button>
                <a href="<?php echo get_admin_url('/index.php?page=quotations/list'); ?>" class="btn btn-default">取消</a>
            </div>
        </form>
    </div>
</div>

<script>
$(document).ready(function() {
    // 初始化客户选择器 - 简化版
    $('#customer_id').select2({
        ajax: {
            url: '<?php echo get_admin_url("/index.php?page=quotations/add"); ?>',
            dataType: 'json',
            delay: 250,
            data: function(params) {
                return {
                    customer_search: params.term
                };
            },
            processResults: function(data) {
                return {
                    results: $.map(data, function(customer) {
                        return {
                            id: customer.id,
                            text: customer.name
                        };
                    })
                };
            }
        },
        minimumInputLength: 1,
        placeholder: '搜索并选择客户',
        width: '100%',
        language: {
            inputTooShort: function() {
                return '请输入至少1个字符';
            },
            searching: function() {
                return '搜索中...';
            },
            noResults: function() {
                return '没有找到匹配项';
            }
        }
    });

    // 初始化税率选择器
    $('#tax_rate').select2();

    // 如果是复制操作，强制设置税率选择器值
    <?php if ($original_quotation): ?>
    $('#tax_rate').val('<?php echo $original_quotation["tax_rate"]; ?>').trigger('change');
    // 确保表单提交时使用正确的税率值
    $('form').on('submit', function() {
        var currentTaxRate = $('#tax_rate').val();
        $('#tax_rate').val(currentTaxRate);
        console.log('Submitting with tax rate:', currentTaxRate);
    });
    <?php endif; ?>

    // 添加产品行 - 简化版
    $('#add-item').click(function() {
        const newRow = $('#items-table tbody tr:first').clone();
        newRow.find('input').val('');
        newRow.find('.spec, .unit, .price, .amount').val('');
        newRow.find('.quantity').val(1);
        newRow.find('input[name="product_name[]"]').val('');

        // 彻底清理原有select2 DOM结构
        newRow.find('.select2-container').remove();
        newRow.find('.product-select').removeClass('select2-hidden-accessible');
        newRow.find('.product-select').removeAttr('data-select2-id');
        newRow.find('.product-select').empty().html('<option value=""></option>');

        $('#items-table tbody').append(newRow);
        initProductSelect(newRow.find('.product-select'));

        // 滚动到新行
        $('html, body').animate({
            scrollTop: newRow.offset().top - 100
        }, 200);
    });

    // 单价修改时自动计算金额
    $(document).on('input', '.price', function() {
        const row = $(this).closest('tr');
        calculateAmount(row);
    });

    // 移除产品行
    $(document).on('click', '.remove-item', function() {
        if ($('#items-table tbody tr').length > 1) {
            $(this).closest('tr').remove();
            calculateTotals();
        } else {
            alert('至少需要保留一个产品项');
        }
    });

    // 初始化产品选择器
    function initProductSelect(select) {
        $(select).select2({
            ajax: {
                url: '<?php echo get_admin_url("/index.php?page=quotations/add"); ?>',
                dataType: 'json',
                delay: 250,
                data: function(params) {
                    return {
                        product_search: params.term
                    };
                },
                processResults: function(data) {
                    return {
                        results: data.map(function(product) {
                            return {
                                id: product.id,
                                text: product.name + (product.spec ? ' ('+product.spec+')' : ''),
                                spec: product.spec,
                                unit: product.unit,
                                price: product.price
                            };
                        })
                    };
                },
                cache: true
            },
            minimumInputLength: 1,
            templateResult: formatProduct,
            templateSelection: formatProductSelection,
            width: '100%',
            escapeMarkup: function (markup) { return markup; },
            language: {
                inputTooShort: function() {
                    return '请输入至少1个字符';
                },
                searching: function() {
                    return '搜索中...';
                },
                noResults: function() {
                    return '没有找到匹配项';
                }
            }
        }).on('select2:select', function(e) {
            var row = $(this).closest('tr');
            var data = e.params.data;

            row.find('input[name="product_name[]"]').val(data.text);
            row.find('.spec').val(data.spec);
            row.find('.unit').val(data.unit);
            row.find('.price').val(data.price);
            row.find('.quantity').focus();

            calculateAmount(row);
        });
    }

    // 格式化产品显示
    function formatProduct(product) {
        if (!product.id) return product.text;
        return $('<span>' + product.text + '</span>');
    }

    // 格式化选中产品显示
    function formatProductSelection(product) {
        return product.text;
    }

    // 初始化所有产品选择器
    $('.product-select').each(function() {
        initProductSelect(this);

        // 如果是复制操作，设置已选择的产品
        const productId = $(this).data('product-id');
        if (productId) {
            const productName = $(this).data('product-name');
            const spec = $(this).data('spec');
            const unit = $(this).data('unit');
            const price = $(this).data('price');

            // 设置选择器值
            const option = new Option(productName, productId, true, true);
            $(this).append(option).trigger('change');

            // 触发金额计算
            calculateAmount($(this).closest('tr'));
        }
    });

    // 绑定数量变化事件
    $('.quantity').on('input', function() {
        calculateAmount($(this).closest('tr'));
    });

    // 如果是复制操作，初始化后立即计算总计
    if ($('.product-select[data-product-id]').length > 0) {
        calculateTotals();
    }

    // 全局事件绑定
    $(document).on('input', '.quantity', function() {
        calculateAmount($(this).closest('tr'));
    });

    // 计算单行金额
    function calculateAmount(row) {
        const price = parseFloat(row.find('.price').val()) || 0;
        const quantity = parseInt(row.find('.quantity').val()) || 0;
        const amount = (price * quantity).toFixed(2);

        row.find('.amount').val(amount);
        calculateTotals();
    }

    // 计算总计
    function calculateTotals() {
        let subtotal = 0;
        const taxRate = parseFloat($('#tax_rate').val()) || 0;

        $('.amount').each(function() {
            subtotal += parseFloat($(this).val()) || 0;
        });

        // 当税率为0%时，直接显示未税金额
        if (taxRate === 0) {
            $('#subtotal').text(subtotal.toFixed(2));
            $('#tax-amount').text('0.00');
            $('#total-amount').text(subtotal.toFixed(2));
        } else {
            const taxAmount = (subtotal * taxRate / 100).toFixed(2);
            const totalAmount = (subtotal + parseFloat(taxAmount)).toFixed(2);

            $('#subtotal').text(subtotal.toFixed(2));
            $('#tax-amount').text(taxAmount);
            $('#total-amount').text(totalAmount);
        }
    }

    // 税率变化
    $('#tax_rate').on('input', function() {
        calculateTotals();
        // 根据税率是否为零切换显示
        const taxRate = parseFloat($(this).val()) || 0;
        if (taxRate > 0) {
            $('#total-amount').prev('th').text('含税总金额');
        } else {
            $('#total-amount').prev('th').text('未税总金额');
        }
    });

    // 如果是复制操作，设置税率选择器值
    <?php if ($original_quotation): ?>
        $('#tax_rate').val('<?php echo $original_quotation["tax_rate"]; ?>').trigger('change');
    <?php endif; ?>

    // 初始化显示
    const initialTaxRate = parseFloat($('#tax_rate').val()) || 0;
    if (initialTaxRate > 0) {
        $('#total-amount').prev('th').text('含税总金额');
    } else {
        $('#total-amount').prev('th').text('未税总金额');
    }

    // 如果是复制操作，立即计算总计
    <?php if ($original_quotation): ?>
        calculateTotals();
    <?php endif; ?>

// 表单提交前验证
$('#quotation-form').submit(function() {
    var valid = true;

    // 确保隐藏的税率输入字段是最新的
    $('input[name="tax_rate"]').val($('#tax_rate').val());

    // 验证产品项
    $('.product-select').each(function() {
        if ($(this).val() === '') {
            alert('请选择所有产品');
            valid = false;
            return false;
        }
    });

    if (!valid) return false;

    // 验证数量
    $('.quantity').each(function() {
        if (parseInt($(this).val()) <= 0) {
            alert('数量必须大于0');
            valid = false;
            return false;
        }
    });

    return valid;
});
});
</script>
<script src="<?php echo get_admin_url('/public/adminlte/plugins/select2/select2.min.js'); ?>"></script>
<script src="<?php echo get_admin_url('/public/adminlte/plugins/select2/zh-CN.js'); ?>"></script>
======== search_replace
<<< SEARCH <<<
    // 如果是复制操作，预填表单数据
    if ($original_quotation) {
        $_POST['project_name'] = $original_quotation['project_name'] . ' (副本)';
        $_POST['customer_id'] = $original_quotation['customer_id'];
        $_POST['quotation_date'] = date('Y-m-d');
        // 强制设置税率，确保不会被覆盖
        $_POST['tax_rate'] = $original_quotation['tax_rate'];
        $_REQUEST['tax_rate'] = $original_quotation['tax_rate'];
        $_GET['tax_rate'] = $original_quotation['tax_rate'];
        $_SESSION['temp_tax_rate'] = $original_quotation['tax_rate'];
        $_POST['remark'] = $original_quotation['remark'];

        // 调试日志
        error_log(\"Copy quotation: Setting tax_rate to \".$original_quotation['tax_rate']);
    }
==== REPLACED_BY ====
    // 如果是复制操作，预填表单数据
    if ($original_quotation) {
        $_POST['project_name'] = $original_quotation['project_name'] . ' (副本)';
        $_POST['customer_id'] = $original_quotation['customer_id'];
        $_POST['quotation_date'] = date('Y-m-d');
        // 设置初始税率，但允许用户修改
        $_POST['tax_rate'] = $original_quotation['tax_rate'];
        $_SESSION['temp_tax_rate'] = $original_quotation['tax_rate'];
        $_POST['remark'] = $original_quotation['remark'];

        // 调试日志
        error_log(\"Copy quotation: Initial tax_rate set to \".$original_quotation['tax_rate']);
    }
>>> END >>>

<<< SEARCH <<<
    if (is_post()) {
        // 调试日志：打印所有POST数据
        error_log(\"Form submission POST data: \" . print_r($_POST, true));

        $project_name = clean_input($_POST['project_name']);
        $customer_id = (int)$_POST['customer_id'];
        $quotation_date = $_POST['quotation_date'];
        $tax_rate = (float)$_POST['tax_rate'];
        $remark = clean_input($_POST['remark'] ?? '');
==== REPLACED_BY ====
    if (is_post()) {
        // 调试日志：打印所有POST数据
        error_log(\"Form submission POST data: \" . print_r($_POST, true));

        $project_name = clean_input($_POST['project_name']);
        $customer_id = (int)$_POST['customer_id'];
        $quotation_date = $_POST['quotation_date'];
        // 优先使用用户选择的最新税率
        $tax_rate = isset($_POST['tax_rate']) ? (float)$_POST['tax_rate'] :
                  (isset($_SESSION['temp_tax_rate']) ? (float)$_SESSION['temp_tax_rate'] : 0);
        $remark = clean_input($_POST['remark'] ?? '');

        // 调试日志：打印最终使用的税率
        error_log(\"Final tax rate used: $tax_rate\");
>>> END >>>
======== applied
<?php
$page_title = '新建报价单';
check_permission();

// 获取当前登录用户信息
$salesperson = $_SESSION['admin_real_name'] ?? $_SESSION['admin_username'] ?? '系统管理员';

// 处理复制报价单逻辑
$copy_from = isset($_GET['copy_from']) ? (int)$_GET['copy_from'] : 0;
$original_quotation = null;
$original_items = [];

if ($copy_from > 0) {
    $original_quotation = db_fetch_one(db_query(
        "SELECT * FROM quotations WHERE id = ?",
        [$copy_from]
    ));

    if ($original_quotation) {
        $original_items = db_fetch_all(db_query(
            "SELECT * FROM quotation_items WHERE quotation_id = ?",
            [$copy_from]
        ));
    }
}

// 自动生成报价单号
$quotation_no = 'BJ' . date('Ymd') . substr(uniqid(), -4);

// 如果是复制操作，预填表单数据
if ($original_quotation) {
    $_POST['project_name'] = $original_quotation['project_name'] . ' (副本)';
    $_POST['customer_id'] = $original_quotation['customer_id'];
    $_POST['quotation_date'] = date('Y-m-d');
    // 强制设置税率，确保不会被覆盖
    $_POST['tax_rate'] = $original_quotation['tax_rate'];
    $_REQUEST['tax_rate'] = $original_quotation['tax_rate'];
    $_GET['tax_rate'] = $original_quotation['tax_rate'];
    $_SESSION['temp_tax_rate'] = $original_quotation['tax_rate'];
    $_POST['remark'] = $original_quotation['remark'];

    // 调试日志
    error_log("Copy quotation: Setting tax_rate to ".$original_quotation['tax_rate']);
}

// 处理AJAX请求
if (isset($_GET['customer_search'])) {
    $search = clean_input($_GET['customer_search']);
    $customers = db_fetch_all(db_query("SELECT id, name FROM customers WHERE name LIKE ? ORDER BY name LIMIT 10", ["%{$search}%"]));
    header('Content-Type: application/json');
    echo json_encode($customers);
    exit;
}

if (isset($_GET['product_search'])) {
    $search = clean_input($_GET['product_search']);
    $products = db_fetch_all(db_query("SELECT id, name, spec, unit, price FROM products WHERE name LIKE ? OR spec LIKE ? ORDER BY name LIMIT 10", ["%{$search}%", "%{$search}%"]));
    header('Content-Type: application/json');
    echo json_encode($products);
    exit;
}

// 正常页面请求
$customers = db_fetch_all(db_query("SELECT id, name FROM customers ORDER BY name LIMIT 10"));
$products = db_fetch_all(db_query("SELECT id, name, spec, unit, price FROM products ORDER BY name LIMIT 10"));

// 如果是复制操作，预加载客户信息
if ($original_quotation && !isset($_POST['customer_id'])) {
    $selected_customer = db_fetch_one(db_query(
        "SELECT id, name FROM customers WHERE id = ?",
        [$original_quotation['customer_id']]
    ));
}

if (is_post()) {
    // 调试日志：打印所有POST数据
    error_log("Form submission POST data: " . print_r($_POST, true));

    $project_name = clean_input($_POST['project_name']);
    $customer_id = (int)$_POST['customer_id'];
    $quotation_date = $_POST['quotation_date'];
    $tax_rate = (float)$_POST['tax_rate'];
    $remark = clean_input($_POST['remark'] ?? '');

    // 调试日志：打印关键字段值
    error_log("Processing submission with tax_rate: $tax_rate");

    // 验证输入
    $errors = [];
    if (empty($project_name)) {
        $errors[] = '项目名称不能为空';
    }
    if ($customer_id <= 0) {
        $errors[] = '请选择客户';
    }
    if (empty($quotation_date)) {
        $errors[] = '报价日期不能为空';
    }
    if ($tax_rate < 0 || $tax_rate > 100) {
        $errors[] = '税率必须在0-100之间';
    }

    // 验证产品项
    $items = [];
    if (isset($_POST['product_id']) && is_array($_POST['product_id'])) {
        foreach ($_POST['product_id'] as $index => $product_id) {
            $quantity = (int)$_POST['quantity'][$index];
            if ($product_id > 0 && $quantity > 0) {
                $items[] = [
                    'product_id' => $product_id,
                    'product_name' => $_POST['product_name'][$index],
                    'spec' => $_POST['spec'][$index],
                    'unit' => $_POST['unit'][$index],
                    'price' => (float)$_POST['price'][$index],
                    'quantity' => $quantity,
                    'amount' => (float)$_POST['amount'][$index],
                    'remark' => clean_input($_POST['item_remark'][$index] ?? '')
                ];
            }
        }
    }

    if (empty($items)) {
        $errors[] = '至少需要一个有效的产品项';
    }

    if (empty($errors)) {
        // 计算金额
        $subtotal = array_reduce($items, function($carry, $item) {
            return $carry + $item['amount'];
        }, 0);

        $tax_amount = round($subtotal * $tax_rate / 100, 2);
        $total_amount = $subtotal + $tax_amount;

        // 开启事务
        db_query("START TRANSACTION");

        try {
            // 插入报价单主表
            $sql = "INSERT INTO quotations (
                quotation_no, project_name, quotation_date, customer_id, salesperson,
                remark, status, subtotal, tax_rate, tax_amount, total_amount
            ) VALUES (?, ?, ?, ?, ?, ?, 'pending', ?, ?, ?, ?)";

            $result = db_query($sql, [
                $quotation_no, $project_name, $quotation_date, $customer_id, $salesperson,
                $remark, $subtotal, $tax_rate, $tax_amount, $total_amount
            ]);

            $quotation_id = db_insert_id();

            // 调试日志：打印最终保存的税率和金额
            error_log("Saved quotation with tax_rate: $tax_rate, subtotal: $subtotal, tax_amount: $tax_amount, total_amount: $total_amount");

            // 插入报价单明细
            foreach ($items as $item) {
                $sql = "INSERT INTO quotation_items (
                    quotation_id, product_id, product_name, spec, unit,
                    price, quantity, amount, remark
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

                db_query($sql, [
                    $quotation_id, $item['product_id'], $item['product_name'], $item['spec'], $item['unit'],
                    $item['price'], $item['quantity'], $item['amount'], $item['remark']
                ]);
            }

            // 提交事务
            db_query("COMMIT");

            show_message('报价单创建成功', 'success');
            redirect(get_admin_url('/index.php?page=quotations/list'));

        } catch (Exception $e) {
            // 回滚事务
            db_query("ROLLBACK");
            show_message('报价单创建失败: ' . $e->getMessage(), 'danger');
        }
    } else {
        show_message(implode('<br>', $errors), 'danger');
    }
}
?>

<link rel="stylesheet" href="<?php echo get_admin_url('/public/adminlte/plugins/select2/css/select2.min.css'); ?>">
<link rel="stylesheet" href="<?php echo get_admin_url('/public/adminlte/plugins/select2/css/custom-select2.css'); ?>">
<div class="card">
    <div class="card-header">
        <h3 class="card-title">新建报价单</h3>
    </div>
    <div class="card-body">
        <form method="post" id="quotation-form">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="quotation_no">报价单号 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="quotation_no" name="quotation_no" value="<?php echo $quotation_no; ?>" readonly>
                    </div>
                    <div class="form-group">
                        <label for="project_name">项目名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="project_name" name="project_name" value="<?php echo isset($_POST['project_name']) ? safe_output($_POST['project_name']) : ''; ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="customer_id">客户 <span class="text-danger">*</span></label>
                        <select class="form-control select2" id="customer_id" name="customer_id" required>
                            <option value="">请选择客户</option>
                            <?php foreach ($customers as $customer): ?>
                                <option value="<?php echo $customer['id']; ?>" <?php echo isset($_POST['customer_id']) && $_POST['customer_id'] == $customer['id'] ? 'selected' : ''; ?>>
                                    <?php echo safe_output($customer['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="quotation_date">报价日期 <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="quotation_date" name="quotation_date" value="<?php echo isset($_POST['quotation_date']) ? $_POST['quotation_date'] : date('Y-m-d'); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="salesperson">报价人</label>
                        <input type="text" class="form-control" id="salesperson" name="salesperson" value="<?php echo $salesperson; ?>" readonly>
                    </div>
                    <div class="form-group">
                        <label for="tax_rate">税率 <span class="text-danger">*</span></label>
                        <select class="form-control select2" id="tax_rate" name="tax_rate" required>
                            <?php
                            $tax_rates = db_fetch_all(db_query("SELECT * FROM tax_rates ORDER BY is_default DESC, rate DESC"));
                            foreach ($tax_rates as $rate): ?>
                                <option value="<?php echo $rate['rate']; ?>" <?php echo $rate['is_default'] ? 'selected' : ''; ?>>
                                    <?php echo safe_output($rate['name']); ?> (<?php echo $rate['rate']; ?>%)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>产品明细</label>
                <div class="table-responsive">
                    <table class="table table-bordered" id="items-table">
                        <thead>
                            <tr>
                                <th width="30%">产品名称</th>
                                <th width="15%">规格型号</th>
                                <th width="10%">单位</th>
                                <th width="10%">单价</th>
                                <th width="10%">数量</th>
                                <th width="10%">金额</th>
                                <th width="10%">备注</th>
                                <th width="5%"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($original_items)): ?>
                                <?php foreach ($original_items as $item): ?>
                                <tr>
                                    <td>
                                        <select class="form-control product-select" name="product_id[]" required
                                            data-placeholder="搜索并选择产品" style="width: 100%;"
                                            data-product-id="<?php echo $item['product_id']; ?>"
                                            data-product-name="<?php echo safe_output($item['product_name']); ?>"
                                            data-spec="<?php echo safe_output($item['spec']); ?>"
                                            data-unit="<?php echo safe_output($item['unit']); ?>"
                                            data-price="<?php echo $item['price']; ?>">
                                        </select>
                                        <input type="hidden" name="product_name[]" value="<?php echo safe_output($item['product_name']); ?>">
                                    </td>
                                    <td><input type="text" class="form-control spec" name="spec[]" value="<?php echo safe_output($item['spec']); ?>"></td>
                                    <td><input type="text" class="form-control unit" name="unit[]" value="<?php echo safe_output($item['unit']); ?>"></td>
                                    <td><input type="number" class="form-control price" name="price[]" value="<?php echo $item['price']; ?>" min="0" step="0.01"></td>
                                    <td><input type="number" class="form-control quantity" name="quantity[]" min="1" value="<?php echo $item['quantity']; ?>" required></td>
                                    <td><input type="number" class="form-control amount" name="amount[]" value="<?php echo $item['amount']; ?>" readonly></td>
                                    <td><input type="text" class="form-control" name="item_remark[]" value="<?php echo safe_output($item['remark']); ?>"></td>
                                    <td><button type="button" class="btn btn-danger btn-sm remove-item"><i class="fas fa-trash"></i></button></td>
                                </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td>
                                        <select class="form-control product-select" name="product_id[]" required
                                            data-placeholder="搜索并选择产品" style="width: 100%;">
                                        </select>
                                        <input type="hidden" name="product_name[]">
                                    </td>
                                    <td><input type="text" class="form-control spec" name="spec[]"></td>
                                    <td><input type="text" class="form-control unit" name="unit[]"></td>
                                    <td><input type="number" class="form-control price" name="price[]" min="0" step="0.01"></td>
                                    <td><input type="number" class="form-control quantity" name="quantity[]" min="1" value="1" required></td>
                                    <td><input type="number" class="form-control amount" name="amount[]" readonly></td>
                                    <td><input type="text" class="form-control" name="item_remark[]"></td>
                                    <td><button type="button" class="btn btn-danger btn-sm remove-item"><i class="fas fa-trash"></i></button></td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="8">
                                    <button type="button" class="btn btn-primary btn-sm" id="add-item">
                                        <i class="fas fa-plus"></i> 添加产品
                                    </button>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="remark">报价备注</label>
                        <textarea class="form-control" id="remark" name="remark" rows="3"><?php echo isset($_POST['remark']) ? safe_output($_POST['remark']) : ''; ?></textarea>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>金额统计</label>
                        <table class="table table-bordered">
                            <tr>
                                <th width="50%">金额小计</th>
                                <td width="50%" id="subtotal">0.00</td>
                            </tr>
                            <tr>
                                <th>税额</th>
                                <td id="tax-amount">0.00</td>
                            </tr>
                            <tr>
                                <?php if (isset($is_tax_included) && $is_tax_included): ?>
                                    <th>含税总金额</th>
                                <?php else: ?>
                                    <th>未税总金额</th>
                                <?php endif; ?>
                                <td id="total-amount">0.00</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <button type="submit" class="btn btn-primary">保存</button>
                <a href="<?php echo get_admin_url('/index.php?page=quotations/list'); ?>" class="btn btn-default">取消</a>
            </div>
        </form>
    </div>
</div>

<script>
$(document).ready(function() {
    // 初始化客户选择器 - 简化版
    $('#customer_id').select2({
        ajax: {
            url: '<?php echo get_admin_url("/index.php?page=quotations/add"); ?>',
            dataType: 'json',
            delay: 250,
            data: function(params) {
                return {
                    customer_search: params.term
                };
            },
            processResults: function(data) {
                return {
                    results: $.map(data, function(customer) {
                        return {
                            id: customer.id,
                            text: customer.name
                        };
                    })
                };
            }
        },
        minimumInputLength: 1,
        placeholder: '搜索并选择客户',
        width: '100%',
        language: {
            inputTooShort: function() {
                return '请输入至少1个字符';
            },
            searching: function() {
                return '搜索中...';
            },
            noResults: function() {
                return '没有找到匹配项';
            }
        }
    });

    // 初始化税率选择器
    $('#tax_rate').select2();

    // 如果是复制操作，强制设置税率选择器值
    <?php if ($original_quotation): ?>
    $('#tax_rate').val('<?php echo $original_quotation["tax_rate"]; ?>').trigger('change');
    // 确保表单提交时使用正确的税率值
    $('form').on('submit', function() {
        var currentTaxRate = $('#tax_rate').val();
        $('#tax_rate').val(currentTaxRate);
        console.log('Submitting with tax rate:', currentTaxRate);
    });
    <?php endif; ?>

    // 添加产品行 - 简化版
    $('#add-item').click(function() {
        const newRow = $('#items-table tbody tr:first').clone();
        newRow.find('input').val('');
        newRow.find('.spec, .unit, .price, .amount').val('');
        newRow.find('.quantity').val(1);
        newRow.find('input[name="product_name[]"]').val('');

        // 彻底清理原有select2 DOM结构
        newRow.find('.select2-container').remove();
        newRow.find('.product-select').removeClass('select2-hidden-accessible');
        newRow.find('.product-select').removeAttr('data-select2-id');
        newRow.find('.product-select').empty().html('<option value=""></option>');

        $('#items-table tbody').append(newRow);
        initProductSelect(newRow.find('.product-select'));

        // 滚动到新行
        $('html, body').animate({
            scrollTop: newRow.offset().top - 100
        }, 200);
    });

    // 单价修改时自动计算金额
    $(document).on('input', '.price', function() {
        const row = $(this).closest('tr');
        calculateAmount(row);
    });

    // 移除产品行
    $(document).on('click', '.remove-item', function() {
        if ($('#items-table tbody tr').length > 1) {
            $(this).closest('tr').remove();
            calculateTotals();
        } else {
            alert('至少需要保留一个产品项');
        }
    });

    // 初始化产品选择器
    function initProductSelect(select) {
        $(select).select2({
            ajax: {
                url: '<?php echo get_admin_url("/index.php?page=quotations/add"); ?>',
                dataType: 'json',
                delay: 250,
                data: function(params) {
                    return {
                        product_search: params.term
                    };
                },
                processResults: function(data) {
                    return {
                        results: data.map(function(product) {
                            return {
                                id: product.id,
                                text: product.name + (product.spec ? ' ('+product.spec+')' : ''),
                                spec: product.spec,
                                unit: product.unit,
                                price: product.price
                            };
                        })
                    };
                },
                cache: true
            },
            minimumInputLength: 1,
            templateResult: formatProduct,
            templateSelection: formatProductSelection,
            width: '100%',
            escapeMarkup: function (markup) { return markup; },
            language: {
                inputTooShort: function() {
                    return '请输入至少1个字符';
                },
                searching: function() {
                    return '搜索中...';
                },
                noResults: function() {
                    return '没有找到匹配项';
                }
            }
        }).on('select2:select', function(e) {
            var row = $(this).closest('tr');
            var data = e.params.data;

            row.find('input[name="product_name[]"]').val(data.text);
            row.find('.spec').val(data.spec);
            row.find('.unit').val(data.unit);
            row.find('.price').val(data.price);
            row.find('.quantity').focus();

            calculateAmount(row);
        });
    }

    // 格式化产品显示
    function formatProduct(product) {
        if (!product.id) return product.text;
        return $('<span>' + product.text + '</span>');
    }

    // 格式化选中产品显示
    function formatProductSelection(product) {
        return product.text;
    }

    // 初始化所有产品选择器
    $('.product-select').each(function() {
        initProductSelect(this);

        // 如果是复制操作，设置已选择的产品
        const productId = $(this).data('product-id');
        if (productId) {
            const productName = $(this).data('product-name');
            const spec = $(this).data('spec');
            const unit = $(this).data('unit');
            const price = $(this).data('price');

            // 设置选择器值
            const option = new Option(productName, productId, true, true);
            $(this).append(option).trigger('change');

            // 触发金额计算
            calculateAmount($(this).closest('tr'));
        }
    });

    // 绑定数量变化事件
    $('.quantity').on('input', function() {
        calculateAmount($(this).closest('tr'));
    });

    // 如果是复制操作，初始化后立即计算总计
    if ($('.product-select[data-product-id]').length > 0) {
        calculateTotals();
    }

    // 全局事件绑定
    $(document).on('input', '.quantity', function() {
        calculateAmount($(this).closest('tr'));
    });

    // 计算单行金额
    function calculateAmount(row) {
        const price = parseFloat(row.find('.price').val()) || 0;
        const quantity = parseInt(row.find('.quantity').val()) || 0;
        const amount = (price * quantity).toFixed(2);

        row.find('.amount').val(amount);
        calculateTotals();
    }

    // 计算总计
    function calculateTotals() {
        let subtotal = 0;
        const taxRate = parseFloat($('#tax_rate').val()) || 0;

        $('.amount').each(function() {
            subtotal += parseFloat($(this).val()) || 0;
        });

        // 当税率为0%时，直接显示未税金额
        if (taxRate === 0) {
            $('#subtotal').text(subtotal.toFixed(2));
            $('#tax-amount').text('0.00');
            $('#total-amount').text(subtotal.toFixed(2));
        } else {
            const taxAmount = (subtotal * taxRate / 100).toFixed(2);
            const totalAmount = (subtotal + parseFloat(taxAmount)).toFixed(2);

            $('#subtotal').text(subtotal.toFixed(2));
            $('#tax-amount').text(taxAmount);
            $('#total-amount').text(totalAmount);
        }
    }

    // 税率变化
    $('#tax_rate').on('input', function() {
        calculateTotals();
        // 根据税率是否为零切换显示
        const taxRate = parseFloat($(this).val()) || 0;
        if (taxRate > 0) {
            $('#total-amount').prev('th').text('含税总金额');
        } else {
            $('#total-amount').prev('th').text('未税总金额');
        }
    });

    // 如果是复制操作，设置税率选择器值
    <?php if ($original_quotation): ?>
        $('#tax_rate').val('<?php echo $original_quotation["tax_rate"]; ?>').trigger('change');
    <?php endif; ?>

    // 初始化显示
    const initialTaxRate = parseFloat($('#tax_rate').val()) || 0;
    if (initialTaxRate > 0) {
        $('#total-amount').prev('th').text('含税总金额');
    } else {
        $('#total-amount').prev('th').text('未税总金额');
    }

    // 如果是复制操作，立即计算总计
    <?php if ($original_quotation): ?>
        calculateTotals();
    <?php endif; ?>

// 表单提交前验证
$('#quotation-form').submit(function() {
    var valid = true;

    // 确保隐藏的税率输入字段是最新的
    $('input[name="tax_rate"]').val($('#tax_rate').val());

    // 验证产品项
    $('.product-select').each(function() {
        if ($(this).val() === '') {
            alert('请选择所有产品');
            valid = false;
            return false;
        }
    });

    if (!valid) return false;

    // 验证数量
    $('.quantity').each(function() {
        if (parseInt($(this).val()) <= 0) {
            alert('数量必须大于0');
            valid = false;
            return false;
        }
    });

    return valid;
});
});
</script>
<script src="<?php echo get_admin_url('/public/adminlte/plugins/select2/select2.min.js'); ?>"></script>
<script src="<?php echo get_admin_url('/public/adminlte/plugins/select2/zh-CN.js'); ?>"></script>