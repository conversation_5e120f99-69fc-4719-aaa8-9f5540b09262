======== original
import express, { Express, Request, Response } from 'express';
import fs from 'fs';
import path from 'path';
import net from 'net';

interface ScoreData {
  bestScore: number;
}

const app: Express = express();

// 自动寻找可用端口
const findAvailablePort = (startPort: number): Promise<number> => {
  return new Promise((resolve) => {
    const server = net.createServer();
    server.unref();
    server.on('error', () => {
      resolve(findAvailablePort(startPort + 1));
    });
    server.listen(startPort, () => {
      server.close(() => {
        resolve(startPort);
      });
    });
  });
};

// 初始化服务器
const startServer = async (): Promise<void> => {
  const port = await findAvailablePort(3000);

  app.use(express.json());
  app.use(express.static(__dirname));

  // API路由
  app.get('/api/score', (req: Request, res: Response) => {
    try {
      const scores: ScoreData = JSON.parse(fs.readFileSync(path.join(__dirname, 'scores.json'), 'utf8'));
      res.json(scores);
    } catch (error) {
      res.status(500).json({ error: '无法读取分数' });
    }
  });

  app.post('/api/score', (req: Request, res: Response) => {
    try {
      const { score }: { score: number } = req.body;
      const scores: ScoreData = JSON.parse(fs.readFileSync(path.join(__dirname, 'scores.json'), 'utf8'));

      if (score > scores.bestScore) {
        scores.bestScore = score;
        fs.writeFileSync(path.join(__dirname, 'scores.json'), JSON.stringify(scores, null, 4));
      }

      res.json(scores);
    } catch (error) {
      res.status(500).json({ error: '无法更新分数' });
    }
  });

  app.listen(port, () => {
    console.log(`服务器正在运行于 http://localhost:${port}`);
    console.log('按 Ctrl+C 停止服务器');
  });
};

startServer().catch((err: Error) => {
  console.error('启动服务器失败:', err);
});
======== search_replace
<<< SEARCH <<<
import express, { Express, Request, Response } from 'express';
import fs from 'fs';
import path from 'path';
import net from 'net';
==== REPLACED_BY ====
import express, { Express, Request, Response } from 'express';
import fs from 'fs';
import path from 'path';
import net from 'net';
import { createClient } from 'redis';
import swaggerUi from 'swagger-ui-express';
import jwt from 'jsonwebtoken';
import YAML from 'yamljs';
>>> END >>>

<<< SEARCH <<<
interface ScoreData {
  bestScore: number;
}
==== REPLACED_BY ====
interface ScoreData {
  bestScore: number;
  playerId?: string;
}

interface User {
  id: string;
  name: string;
}

interface AuthRequest extends Request {
  user?: User;
}
>>> END >>>

<<< SEARCH <<<
const app: Express = express();
==== REPLACED_BY ====
const app: Express = express();
const redisClient = createClient();
const swaggerDocument = YAML.load(path.join(__dirname, 'swagger.yaml'));
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// Redis连接
redisClient.on('error', (err) => console.log('Redis Client Error', err));
redisClient.connect();
>>> END >>>

<<< SEARCH <<<
// API路由
app.get('/api/score', (req: Request, res: Response) => {
    try {
      const scores: ScoreData = JSON.parse(fs.readFileSync(path.join(__dirname, 'scores.json'), 'utf8'));
      res.json(scores);
    } catch (error) {
      res.status(500).json({ error: '无法读取分数' });
    }
});

app.post('/api/score', (req: Request, res: Response) => {
    try {
      const { score }: { score: number } = req.body;
      const scores: ScoreData = JSON.parse(fs.readFileSync(path.join(__dirname, 'scores.json'), 'utf8'));

      if (score > scores.bestScore) {
        scores.bestScore = score;
        fs.writeFileSync(path.join(__dirname, 'scores.json'), JSON.stringify(scores, null, 4));
      }

      res.json(scores);
    } catch (error) {
      res.status(500).json({ error: '无法更新分数' });
    }
});
==== REPLACED_BY ====
// 中间件
app.use(express.json());
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocument));

// JWT验证中间件
const authenticate = (req: AuthRequest, res: Response, next: Function) => {
  const token = req.headers.authorization?.split(' ')[1];
  if (!token) return res.status(401).json({ error: '未授权' });

  try {
    const decoded = jwt.verify(token, JWT_SECRET) as User;
    req.user = decoded;
    next();
  } catch (err) {
    res.status(401).json({ error: '无效令牌' });
  }
};

// API路由
app.get('/api/score', async (req: Request, res: Response) => {
    try {
      // 尝试从Redis获取
      const cachedScores = await redisClient.get('scores');
      if (cachedScores) {
        return res.json(JSON.parse(cachedScores));
      }

      // 回退到文件
      const scores: ScoreData = JSON.parse(fs.readFileSync(path.join(__dirname, 'scores.json'), 'utf8'));

      // 缓存到Redis，有效期1小时
      await redisClient.set('scores', JSON.stringify(scores), { EX: 3600 });

      res.json(scores);
    } catch (error) {
      res.status(500).json({ error: '无法读取分数' });
    }
});

app.post('/api/score', authenticate, async (req: AuthRequest, res: Response) => {
    try {
      const { score }: { score: number } = req.body;
      const scores: ScoreData = JSON.parse(fs.readFileSync(path.join(__dirname, 'scores.json'), 'utf8'));

      if (score > scores.bestScore) {
        scores.bestScore = score;
        scores.playerId = req.user?.id;

        fs.writeFileSync(path.join(__dirname, 'scores.json'), JSON.stringify(scores, null, 4));

        // 更新Redis缓存
        await redisClient.set('scores', JSON.stringify(scores), { EX: 3600 });
      }

      res.json(scores);
    } catch (error) {
      res.status(500).json({ error: '无法更新分数' });
    }
});

// 登录路由
app.post('/api/login', (req: Request, res: Response) => {
  const { username, password } = req.body;

  // 简单验证
  if (username === 'admin' && password === 'admin') {
    const token = jwt.sign({ id: '1', name: 'Admin' }, JWT_SECRET, { expiresIn: '1h' });
    return res.json({ token });
  }

  res.status(401).json({ error: '无效凭证' });
});
>>> END >>>
======== applied
import express, { Express, Request, Response } from 'express';
import fs from 'fs';
import path from 'path';
import net from 'net';
import { createClient } from 'redis';
import swaggerUi from 'swagger-ui-express';
import jwt from 'jsonwebtoken';
import YAML from 'yamljs';

interface ScoreData {
  bestScore: number;
  playerId?: string;
}

interface User {
  id: string;
  name: string;
}

interface AuthRequest extends Request {
  user?: User;
}

const app: Express = express();
const redisClient = createClient();
const swaggerDocument = YAML.load(path.join(__dirname, 'swagger.yaml'));
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// Redis连接
redisClient.on('error', (err) => console.log('Redis Client Error', err));
redisClient.connect();

// 自动寻找可用端口
const findAvailablePort = (startPort: number): Promise<number> => {
  return new Promise((resolve) => {
    const server = net.createServer();
    server.unref();
    server.on('error', () => {
      resolve(findAvailablePort(startPort + 1));
    });
    server.listen(startPort, () => {
      server.close(() => {
        resolve(startPort);
      });
    });
  });
};

// 初始化服务器
const startServer = async (): Promise<void> => {
  const port = await findAvailablePort(3000);

  app.use(express.json());
  app.use(express.static(__dirname));

  // API路由
  app.get('/api/score', (req: Request, res: Response) => {
    try {
      const scores: ScoreData = JSON.parse(fs.readFileSync(path.join(__dirname, 'scores.json'), 'utf8'));
      res.json(scores);
    } catch (error) {
      res.status(500).json({ error: '无法读取分数' });
    }
  });

  app.post('/api/score', (req: Request, res: Response) => {
    try {
      const { score }: { score: number } = req.body;
      const scores: ScoreData = JSON.parse(fs.readFileSync(path.join(__dirname, 'scores.json'), 'utf8'));

      if (score > scores.bestScore) {
        scores.bestScore = score;
        fs.writeFileSync(path.join(__dirname, 'scores.json'), JSON.stringify(scores, null, 4));
      }

      res.json(scores);
    } catch (error) {
      res.status(500).json({ error: '无法更新分数' });
    }
  });

  app.listen(port, () => {
    console.log(`服务器正在运行于 http://localhost:${port}`);
    console.log('按 Ctrl+C 停止服务器');
  });
};

startServer().catch((err: Error) => {
  console.error('启动服务器失败:', err);
});