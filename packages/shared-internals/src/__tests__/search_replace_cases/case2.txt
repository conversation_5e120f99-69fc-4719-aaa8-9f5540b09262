======== original
@NoArgsConstructor
public class CompletionSettingsPanel implements SettingsPanel {
    private void renderSuggestMode() {
        // 行间补全-自动推荐
        suggestModeComboBox.setSwingPopup(true); // 使得下拉框拥有Swing风格的弹出菜单
        groupPanel.add(new Spacer(), new GridConstraints(1, 1, 1, 1, 0, 1, 6, 1, null, new Dimension(-1, 10), null));

        // 补全模式下拉框
        JPanel comBoxPanel = new JPanel();
        JBLabel suggestModeLabel = new JBLabel();
        loadLabelText(suggestModeLabel, Bundle.get(\"settings.completion.completion_mode\"));
        comBoxPanel.add(suggestModeLabel);
        comBoxPanel.add(suggestModeComboBox);
        groupPanel.add(comBoxPanel, new GridConstraints(2, 0, 1, 3, ANCHOR_WEST, FILL_NONE, SIZEPOLICY_FIXED, SIZEPOLICY_FIXED, null, null, null));

        groupPanel.add(new Spacer(), new GridConstraints(3, 0, 1, 1, ANCHOR_WEST, FILL_NONE, SIZEPOLICY_FIXED, SIZEPOLICY_FIXED, null, null, null));

        JPanel hintPanel = new JPanel();
        hintPanel.setLayout(new GridLayoutManager(2, 1, JBUI.insets(-2, 4, 3, 0), -1, -1, false, false));
        // 自动推荐快捷键提示
        JPanel autoSuggestHintPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 0));
        autoSuggestHintPanel.setBorder(BorderFactory.createEmptyBorder(-5, 0, -5, 0));
        JBLabel autoSuggestHintLabel = new JBLabel();
        autoSuggestHintLabel.setForeground(JBUI.CurrentTheme.ContextHelp.FOREGROUND);
        loadLabelText(autoSuggestHintLabel, Bundle.get(\"settings.completion.completion_mode.hint_auto\"));
        ActionLink acceptSuggestKeyLink = new ActionLink(Bundle.get(\"settings.completion.completion_mode.hint_goto_modify\"), e -> {
            jumpToActionConfig(\"comate.applyInlays\");
        });
        JBLabel rightBracketLabel = new JBLabel();
        rightBracketLabel.setForeground(JBUI.CurrentTheme.ContextHelp.FOREGROUND);
        loadLabelText(rightBracketLabel, Bundle.get(\"settings.completion.completion_mode.hint_right_bracket\"));
        autoSuggestHintPanel.add(autoSuggestHintLabel);
        autoSuggestHintPanel.add(acceptSuggestKeyLink);
        autoSuggestHintPanel.add(rightBracketLabel);
        hintPanel.add(autoSuggestHintPanel, new GridConstraints(0, 0, 1, 1, ANCHOR_WEST, FILL_NONE, SIZEPOLICY_FIXED, SIZEPOLICY_FIXED, null, null, null));

        // 手动触发快捷键提示
        JPanel manualSuggestPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 0));
        manualSuggestPanel.setBorder(BorderFactory.createEmptyBorder(-5, 0, -5, 0));
        JBLabel manualSuggestShortcutLabel = new JBLabel();
        manualSuggestShortcutLabel.setForeground(JBUI.CurrentTheme.ContextHelp.FOREGROUND);
        loadLabelText(manualSuggestShortcutLabel, Bundle.get(\"settings.completion.completion_mode.hint_manual\"));
        ActionLink manualSuggestKeyLink = new ActionLink(Bundle.get(\"settings.completion.completion_mode.hint_goto_modify\"), e -> {
            jumpToActionConfig(\"comate.requestCompletions\");
        });
        JBLabel rightBracketLabel2 = new JBLabel();
        rightBracketLabel2.setForeground(JBUI.CurrentTheme.ContextHelp.FOREGROUND);
        loadLabelText(rightBracketLabel2, Bundle.get(\"settings.completion.completion_mode.hint_right_bracket\"));
        manualSuggestPanel.add(manualSuggestShortcutLabel);
        manualSuggestPanel.add(manualSuggestKeyLink);
        manualSuggestPanel.add(rightBracketLabel2);
        hintPanel.add(manualSuggestPanel, new GridConstraints(1, 0, 1, 1, ANCHOR_WEST, FILL_NONE, SIZEPOLICY_FIXED, SIZEPOLICY_FIXED, null, null, null));

        groupPanel.add(hintPanel, new GridConstraints(4, 0, 1, 3, ANCHOR_WEST, FILL_NONE, SIZEPOLICY_FIXED, SIZEPOLICY_FIXED, null, null, null));
    }
}
======== search_replace
<<< SEARCH <<<
        hintPanel.setLayout(new GridLayoutManager(2, 1, JBUI.insets(-2, 4, 3, 0), -1, -1, false, false));
==== REPLACED_BY ====
        hintPanel.setLayout(new GridLayoutManager(2, 1, JBUI.insets(0, 4, 3, 0), -1, 5, false, false));
>>> END >>>

<<< SEARCH <<<
JPanel autoSuggestHintPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 0));
        autoSuggestHintPanel.setBorder(BorderFactory.createEmptyBorder(-5, 0, -5, 0));
==== REPLACED_BY ====
JPanel autoSuggestHintPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 0));
        autoSuggestHintPanel.setBorder(BorderFactory.createEmptyBorder(0, 0, 0, 0));
>>> END >>>

<<< SEARCH <<<
JPanel manualSuggestPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 0));
        manualSuggestPanel.setBorder(BorderFactory.createEmptyBorder(-5, 0, -5, 0));
==== REPLACED_BY ====
JPanel manualSuggestPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 0));
        manualSuggestPanel.setBorder(BorderFactory.createEmptyBorder(0, 0, 0, 0));
>>> END >>>
======== applied
@NoArgsConstructor
public class CompletionSettingsPanel implements SettingsPanel {
    private void renderSuggestMode() {
        // 行间补全-自动推荐
        suggestModeComboBox.setSwingPopup(true); // 使得下拉框拥有Swing风格的弹出菜单
        groupPanel.add(new Spacer(), new GridConstraints(1, 1, 1, 1, 0, 1, 6, 1, null, new Dimension(-1, 10), null));

        // 补全模式下拉框
        JPanel comBoxPanel = new JPanel();
        JBLabel suggestModeLabel = new JBLabel();
        loadLabelText(suggestModeLabel, Bundle.get(\"settings.completion.completion_mode\"));
        comBoxPanel.add(suggestModeLabel);
        comBoxPanel.add(suggestModeComboBox);
        groupPanel.add(comBoxPanel, new GridConstraints(2, 0, 1, 3, ANCHOR_WEST, FILL_NONE, SIZEPOLICY_FIXED, SIZEPOLICY_FIXED, null, null, null));

        groupPanel.add(new Spacer(), new GridConstraints(3, 0, 1, 1, ANCHOR_WEST, FILL_NONE, SIZEPOLICY_FIXED, SIZEPOLICY_FIXED, null, null, null));

        JPanel hintPanel = new JPanel();
        hintPanel.setLayout(new GridLayoutManager(2, 1, JBUI.insets(0, 4, 3, 0), -1, 5, false, false));
        // 自动推荐快捷键提示
        JPanel autoSuggestHintPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 0));
        autoSuggestHintPanel.setBorder(BorderFactory.createEmptyBorder(0, 0, 0, 0));
        JBLabel autoSuggestHintLabel = new JBLabel();
        autoSuggestHintLabel.setForeground(JBUI.CurrentTheme.ContextHelp.FOREGROUND);
        loadLabelText(autoSuggestHintLabel, Bundle.get(\"settings.completion.completion_mode.hint_auto\"));
        ActionLink acceptSuggestKeyLink = new ActionLink(Bundle.get(\"settings.completion.completion_mode.hint_goto_modify\"), e -> {
            jumpToActionConfig(\"comate.applyInlays\");
        });
        JBLabel rightBracketLabel = new JBLabel();
        rightBracketLabel.setForeground(JBUI.CurrentTheme.ContextHelp.FOREGROUND);
        loadLabelText(rightBracketLabel, Bundle.get(\"settings.completion.completion_mode.hint_right_bracket\"));
        autoSuggestHintPanel.add(autoSuggestHintLabel);
        autoSuggestHintPanel.add(acceptSuggestKeyLink);
        autoSuggestHintPanel.add(rightBracketLabel);
        hintPanel.add(autoSuggestHintPanel, new GridConstraints(0, 0, 1, 1, ANCHOR_WEST, FILL_NONE, SIZEPOLICY_FIXED, SIZEPOLICY_FIXED, null, null, null));

        // 手动触发快捷键提示
        JPanel manualSuggestPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 0));
        manualSuggestPanel.setBorder(BorderFactory.createEmptyBorder(0, 0, 0, 0));
        JBLabel manualSuggestShortcutLabel = new JBLabel();
        manualSuggestShortcutLabel.setForeground(JBUI.CurrentTheme.ContextHelp.FOREGROUND);
        loadLabelText(manualSuggestShortcutLabel, Bundle.get(\"settings.completion.completion_mode.hint_manual\"));
        ActionLink manualSuggestKeyLink = new ActionLink(Bundle.get(\"settings.completion.completion_mode.hint_goto_modify\"), e -> {
            jumpToActionConfig(\"comate.requestCompletions\");
        });
        JBLabel rightBracketLabel2 = new JBLabel();
        rightBracketLabel2.setForeground(JBUI.CurrentTheme.ContextHelp.FOREGROUND);
        loadLabelText(rightBracketLabel2, Bundle.get(\"settings.completion.completion_mode.hint_right_bracket\"));
        manualSuggestPanel.add(manualSuggestShortcutLabel);
        manualSuggestPanel.add(manualSuggestKeyLink);
        manualSuggestPanel.add(rightBracketLabel2);
        hintPanel.add(manualSuggestPanel, new GridConstraints(1, 0, 1, 1, ANCHOR_WEST, FILL_NONE, SIZEPOLICY_FIXED, SIZEPOLICY_FIXED, null, null, null));

        groupPanel.add(hintPanel, new GridConstraints(4, 0, 1, 3, ANCHOR_WEST, FILL_NONE, SIZEPOLICY_FIXED, SIZEPOLICY_FIXED, null, null, null));
    }
}