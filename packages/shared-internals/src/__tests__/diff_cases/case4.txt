======== original
public class Sort
{
    // 快速排序的入口方法
    public void QuickSort(int[] arr)
    {
        if (arr == null || arr.Length <= 1)
            return;

        QuickSortHelper(arr, 0, arr.Length - 1);
    }

    // 快速排序的递归实现
    private void QuickSortHelper(int[] arr, int left, int right)
    {
        if (left < right)
        {
            int pivotIndex = Partition(arr, left, right);
            QuickSortHelper(arr, left, pivotIndex - 1);
            QuickSortHelper(arr, pivotIndex + 1, right);
        }
    }

    // 快速排序的分区方法
    private int Partition(int[] arr, int left, int right)
    {
        int pivot = arr[right];
        int i = left - 1;

        for (int j = left; j < right; j++)
        {
            if (arr[j] <= pivot)
            {
                i++;
                Swap(arr, i, j);
            }
        }

        Swap(arr, i + 1, right);
        return i + 1;
    }

    // 冒泡排序方法
    public void BubbleSort(int[] arr)
    {
        if (arr == null || arr.Length <= 1)
            return;

        bool swapped;
        for (int i = 0; i < arr.Length - 1; i++)
        {
            swapped = false;
            for (int j = 0; j < arr.Length - 1 - i; j++)
            {
                if (arr[j] > arr[j + 1])
                {
                    Swap(arr, j, j + 1);
                    swapped = true;
                }
            }

            // 如果没有发生交换，说明数组已经有序
            if (!swapped)
                break;
        }
    }

    // 交换数组中两个元素的辅助方法
    private void Swap(int[] arr, int i, int j)
    {
        int temp = arr[i];
        arr[i] = arr[j];
        arr[j] = temp;
    }
}
======== diff
@@ -1,15 +1,21 @@
+/// <summary>
+/// 提供多种排序算法实现的类
+/// </summary>
 public class Sort
 {
-    // 快速排序的入口方法
+    /// <summary>
+    /// 快速排序的公共入口方法
+    /// </summary>
+    /// <param name=\"arr\">待排序的整数数组</param>
     public void QuickSort(int[] arr)
     {
+        // 处理空数组或只有一个元素的数组
         if (arr == null || arr.Length <= 1)
             return;

         QuickSortHelper(arr, 0, arr.Length - 1);
     }

-    // 快速排序的递归实现
+    /// <summary>
+    /// 快速排序的递归实现部分
+    /// </summary>
+    /// <param name=\"arr\">待排序的数组</param>
+    /// <param name=\"left\">当前处理区间的左边界</param>
+    /// <param name=\"right\">当前处理区间的右边界</param>
     private void QuickSortHelper(int[] arr, int left, int right)
     {
         if (left < right)
@@ -20,12 +26,19 @@
         }
     }

-    // 快速排序的分区方法
+    /// <summary>
+    /// 快速排序的分区方法
+    /// </summary>
+    /// <param name=\"arr\">待分区的数组</param>
+    /// <param name=\"left\">分区的左边界</param>
+    /// <param name=\"right\">分区的右边界</param>
+    /// <returns>基准元素的最终位置</returns>
     private int Partition(int[] arr, int left, int right)
     {
+        // 选择最右边的元素作为基准值
         int pivot = arr[right];
         int i = left - 1;

+        // 将小于基准值的元素移到左边
         for (int j = left; j < right; j++)
         {
             if (arr[j] <= pivot)
@@ -35,18 +48,26 @@
             }
         }

+        // 将基准值放到最终位置
         Swap(arr, i + 1, right);
         return i + 1;
     }

-    // 冒泡排序方法
+    /// <summary>
+    /// 冒泡排序方法
+    /// </summary>
+    /// <param name=\"arr\">待排序的整数数组</param>
     public void BubbleSort(int[] arr)
     {
+        // 处理空数组或只有一个元素的数组
         if (arr == null || arr.Length <= 1)
             return;

         bool swapped;
+        // 外层循环控制排序轮数
         for (int i = 0; i < arr.Length - 1; i++)
         {
+            // 每一轮开始时重置交换标志
             swapped = false;
+            // 内层循环进行相邻元素比较和交换
             for (int j = 0; j < arr.Length - 1 - i; j++)
             {
                 if (arr[j] > arr[j + 1])
@@ -56,17 +77,23 @@
                 }
             }

-            // 如果没有发生交换，说明数组已经有序
+            // 如果本轮没有发生交换，说明数组已经有序，可以提前退出
             if (!swapped)
                 break;
         }
     }

-    // 交换数组中两个元素的辅助方法
+    /// <summary>
+    /// 交换数组中两个元素的辅助方法
+    /// </summary>
+    /// <param name=\"arr\">要进行元素交换的数组</param>
+    /// <param name=\"i\">第一个元素的索引</param>
+    /// <param name=\"j\">第二个元素的索引</param>
     private void Swap(int[] arr, int i, int j)
     {
         int temp = arr[i];
         arr[i] = arr[j];
         arr[j] = temp;
     }
-}
+}
======== applied
@@ -1,15 +0,0 @@
+/// <summary>
+/// 提供多种排序算法实现的类
+/// </summary>
 public class Sort
 {
-    // 快速排序的入口方法
+    /// <summary>
+    /// 快速排序的公共入口方法
+    /// </summary>
+    /// <param name=\"arr\">待排序的整数数组</param>
     public void QuickSort(int[] arr)
     {
+        // 处理空数组或只有一个元素的数组
         if (arr == null || arr.Length <= 1)
             return;

         QuickSortHelper(arr, 0, arr.Length - 1);
     }

-    // 快速排序的递归实现
+    /// <summary>
+    /// 快速排序的递归实现部分
+    /// </summary>
+    /// <param name=\"arr\">待排序的数组</param>
+    /// <param name=\"left\">当前处理区间的左边界</param>
+    /// <param name=\"right\">当前处理区间的右边界</param>
     private void QuickSortHelper(int[] arr, int left, int right)
     {
         if (left < right)
@@ -20,12 +0,0 @@
         }
     }

-    // 快速排序的分区方法
+    /// <summary>
+    /// 快速排序的分区方法
+    /// </summary>
+    /// <param name=\"arr\">待分区的数组</param>
+    /// <param name=\"left\">分区的左边界</param>
+    /// <param name=\"right\">分区的右边界</param>
+    /// <returns>基准元素的最终位置</returns>
     private int Partition(int[] arr, int left, int right)
     {
+        // 选择最右边的元素作为基准值
         int pivot = arr[right];
         int i = left - 1;

+        // 将小于基准值的元素移到左边
         for (int j = left; j < right; j++)
         {
             if (arr[j] <= pivot)
@@ -35,20 +0,0 @@
             }
         }

+        // 将基准值放到最终位置
         Swap(arr, i + 1, right);
         return i + 1;
     }

-    // 冒泡排序方法
+    /// <summary>
+    /// 冒泡排序方法
+    /// </summary>
+    /// <param name=\"arr\">待排序的整数数组</param>
     public void BubbleSort(int[] arr)
     {
+        // 处理空数组或只有一个元素的数组
         if (arr == null || arr.Length <= 1)
             return;

         bool swapped;
+        // 外层循环控制排序轮数
         for (int i = 0; i < arr.Length - 1; i++)
         {
+            // 每一轮开始时重置交换标志
             swapped = false;
+            // 内层循环进行相邻元素比较和交换
             for (int j = 0; j < arr.Length - 1 - i; j++)
             {
                 if (arr[j] > arr[j + 1])
@@ -58,17 +0,0 @@
                 }
             }

-            // 如果没有发生交换，说明数组已经有序
+            // 如果本轮没有发生交换，说明数组已经有序，可以提前退出
             if (!swapped)
                 break;
         }
     }

-    // 交换数组中两个元素的辅助方法
+    /// <summary>
+    /// 交换数组中两个元素的辅助方法
+    /// </summary>
+    /// <param name=\"arr\">要进行元素交换的数组</param>
+    /// <param name=\"i\">第一个元素的索引</param>
+    /// <param name=\"j\">第二个元素的索引</param>
     private void Swap(int[] arr, int i, int j)
     {
         int temp = arr[i];
         arr[i] = arr[j];
         arr[j] = temp;
     }
-}
+}