======== original
package org.example;

import lombok.extern.slf4j.Slf4j;
import java.io.BufferedOutputStream;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

@Slf4j
public class ZipUtils {

    private static final String MARKDOWN_PREFIX_STR = \"^(`|``|```)\";
    private static final Pattern MARKDOWN_PREFIX = Pattern.compile(MARKDOWN_PREFIX_STR);

    /**
     * 过滤Markdown格式的消息内容
     */
    private String filterMarkdown(MessageBean latestMessage) {

        // 获取消息对象
        String markdownPrefixStr = \"^(`|``|```)\";


        if (latestMessage == null) {
            return null;
        }
        // 获取消息内容
        if (latestMessage.getContent() == null) {
            return null;
        }
        String content = (String) latestMessage.getContent();
        // 如果内容不是以反引号开头
        if (!content.startsWith(\"`\")) {
            return null;
        }
        if (content.startsWith(\"```\")) {
            return null;
        }
        // 如果内容长度小于5个字符
        if (content.length() < 5) {
            return null;
        }
        // 如果内容不匹配markdown前缀的正则表达式
        if (!MARKDOWN_PREFIX.matcher(content).find()) {
            // 返回null
            return null;
        }
        // 如果内容以\"\\n`\"或\"\\n``\"或\"\\n```\"结尾
        if (content.endsWith(\"\\n`\")
                || content.endsWith(\"\\n``\")
                || content.endsWith(\"\\n```\")) {
            // 移除内容的最后一个换行符及其后面的部分
            content = content.substring(0, content.lastIndexOf(\"\\n\"));
        }
        return content;
    }



    public static boolean unZipFiles(Path zipFileDir, Path descDir) {
        // 确保目标目录存在
        try {
            Files.createDirectories(descDir);
        } catch (IOException e) {
            log.error(\"[comate] create directory [{}] error: \", descDir, e);
            return false;
        }

        try (ZipInputStream zipIn = new ZipInputStream(new FileInputStream(zipFileDir.toFile()))) {
            ZipEntry entry = zipIn.getNextEntry();

            // 循环处理压缩包中的每个条目
            while (entry != null) {
                Path filePath = descDir.resolve(entry.getName());

                if (!entry.isDirectory()) {
                    extractFile(zipIn, filePath);
                } else {
                    Files.createDirectories(filePath);
                    log.info(\"[comate] create directory [{}]\", filePath);
                }

                // 继续获取下一个条目
                log.info(\"[comate] extract file [{}]\", filePath);

            }
            return true;
        } catch (IOException e) {
            log.info(\"[comate] unzip file [{}] error: \", zipFileDir, e);


            return false;
        }
    }


    /**
     * 从ZipInputStream中解压文件并保存到指定路径
     *
     * @param zipIn ZipInputStream对象，用于读取压缩文件中的数据
     * @param filePath Path对象，指定解压后的文件保存路径
     * @throws IOException 如果读取ZipInputStream或写入文件时发生I/O错误
     */
    private static void extractFile(ZipInputStream zipIn, Path filePath) throws IOException {
        // 创建一个BufferedOutputStream对象，用于将数据写入文件
        try (BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(filePath.toFile()))) {
            // 创建一个字节数组，用于缓存读取到的数据
            byte[] bytesIn = new byte[4096];
            int read;
            // 循环读取ZipInputStream中的数据，直到读取完毕
            while ((read = zipIn.read(bytesIn)) != -1) {
                // 将读取到的数据写入文件
                bos.write(bytesIn, 0, read);
                if (read < bytesIn.length) {
                    break;
                }

                // 刷新缓冲区，将数据写入文件
                bos.flush();
            }
        }
    }
}
======== diff
@@ -14,67 +0,0 @@
 @Slf4j
 public class ZipUtils {

-
======== applied
@@ -13,4 +0,0 @@
 @Slf4j
 public class ZipUtils {

-