/* eslint-disable max-len */
import {describe, test, expect} from 'vitest';
import {parseShellCommandName} from '../common/command.js';

describe('parseShellCommandName', () => {
    test('单个命令', () => {
        expect(parseShellCommandName('ls -l')).toEqual(['ls']);
    });

    test('多个命令 && 分隔', () => {
        expect(parseShellCommandName('cd /tmp && ls -l')).toEqual(['cd', 'ls']);
    });

    test('多个命令 ; 分隔', () => {
        expect(parseShellCommandName('echo foo; ls')).toEqual(['echo', 'ls']);
    });

    test('多余空格', () => {
        expect(parseShellCommandName('   git   status   &&   npm   install   ')).toEqual(['git', 'npm']);
    });

    test('空输入', () => {
        expect(parseShellCommandName('')).toEqual([]);
    });

    test('管道命令', () => {
        expect(parseShellCommandName('cat file.txt | grep foo')).toEqual(['cat', 'grep']);
    });

    test('子shell命令', () => {
        expect(parseShellCommandName('echo $(ls -l)')).toEqual(['echo', 'ls']);
    });

    test('重定向命令', () => {
        expect(parseShellCommandName('ls > out.txt')).toEqual(['ls']);
    });

    test('包含关键字', () => {
        const commands = 'if grep foo bar.txt; then echo found; else echo not found; fi';
        expect(parseShellCommandName(commands)).toEqual(['grep', 'echo', 'echo']);
    });

    test('子命令和管道', () => {
        expect(parseShellCommandName('(cd /tmp && ls) | grep something')).toEqual(['cd', 'ls', 'grep']);
    });

    test('后台运行命令', () => {
        expect(parseShellCommandName('sleep 5 & echo done')).toEqual(['sleep', 'echo']);
    });

    test('子shell命令嵌套', () => {
        expect(parseShellCommandName('(mkdir test && cd test) && (touch file && ls)')).toEqual(['mkdir', 'cd', 'touch', 'ls']);
    });

    test('多重逻辑组合命令', () => {
        expect(parseShellCommandName('npm run build || echo "build failed" && npm run deploy')).toEqual(['npm', 'echo', 'npm']);
    });

    test('复杂控制流脚本', () => {
        const script = `
            if [ -f foo.txt ]; then
                cat foo.txt | grep bar;
            else
                echo "file not found";
            fi
        `;
        expect(parseShellCommandName(script)).toEqual(['[', 'cat', 'grep', 'echo']);
    });

    test('bash -c 子命令', () => {
        expect(parseShellCommandName('bash -c "cd /tmp && ls"')).toEqual(['bash']);
    });

    test('包含多个子shell', () => {
        const complex = 'echo $(grep foo $(cat files.txt))';
        expect(parseShellCommandName(complex)).toEqual(['echo', 'grep', 'cat']);
    });

    test('子命令带重定向', () => {
        expect(parseShellCommandName('(cd /tmp && ls > out.txt) | grep something')).toEqual(['cd', 'ls', 'grep']);
    });

    test('只包含shell关键字', () => {
        expect(parseShellCommandName('if then else fi')).toEqual([]);
    });

    test('不完整命令', () => {
        expect(parseShellCommandName('echo "hello')).toEqual(['echo']);
    });
});
