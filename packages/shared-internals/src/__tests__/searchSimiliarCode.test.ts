import path from 'node:path';
import {fileURLToPath} from 'node:url';
import {readFileSync, readdirSync} from 'node:fs';
import {describe, test, expect} from 'vitest';
import {searchSimiliarCode} from '../editor/searchSimiliarCode.js';

const filename = fileURLToPath(import.meta.url);
const dirname = path.dirname(filename);

const extractDiffsFromContent = (testcase: string) => {
    const lines = testcase.split('\n');
    const [original, search, matched] = lines.reduce<string[][]>(
        (result, line) => {
            if (line.startsWith('========')) {
                result.push([]);
            }
            else {
                result[result.length - 1].push(line);
            }
            return result;
        },
        []
    );
    return {original: original.join('\n'), search: search.join('\n'), matched: matched.join('\n')};
};

const submodules = readdirSync(path.join(dirname, 'similiar_code_cases'));
const cases = submodules.filter(name => name.startsWith('case')).map(submodule => {
    const testcase = readFileSync(path.join(dirname, 'similiar_code_cases', submodule), 'utf-8');
    return {name: submodule, ...extractDiffsFromContent(testcase)};
});

describe('editor.searchSimiliarCode', () => {
    for (const testcase of cases) {
        const {name, original, search, matched} = testcase;
        test(`searchSimiliarCode.${name}`, () => {
            const result = searchSimiliarCode(original, search);
            expect(result?.block).toEqual(matched);
        });
    }
});
