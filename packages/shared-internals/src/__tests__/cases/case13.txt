======== original
import {renderScratchStartRuleSection, renderRuleSection} from './rule';
import {renderFormatSection} from './format';
import {renderToolSection} from './tool';
import {renderStructureSection} from './structure';
import {renderObjectiveSection} from './objective';
import {InboxPromptView} from './interface';

export {InboxPromptView};

export async function renderInboxSystemPrompt(view: InboxPromptView) {
    const parts = [
        view.projectStructure ? renderRuleSection() : renderScratchStartRuleSection(),
        renderFormatSection(),
        renderToolSection(view),
        '# Context',
        'This section provides some already known information for user\'s request.',
        view.projectStructure ? renderStructureSection(view) : '',
        renderObjectiveSection(view),
    ];
    return parts.filter(v => !!v).join('\n\n');
}
======== diff
@@ -9,6 +9,12 @@ export {InboxPromptView};

 export async function renderInboxSystemPrompt(view: InboxPromptView) {
     const parts \u003d [
+        // If there are custom rules, prepend them
+        view.customRules \u0026\u0026 [
+            \u0027# Custom Rules\u0027,
+            \u0027\u0027,
+            view.customRules
+        ].join(\u0027\
\u0027),
         view.projectStructure ? renderRuleSection() : renderScratchStartRuleSection(),
         renderFormatSection(),
         renderToolSection(view),
======== applied
import {renderScratchStartRuleSection, renderRuleSection} from './rule';
import {renderFormatSection} from './format';
import {renderToolSection} from './tool';
import {renderStructureSection} from './structure';
import {renderObjectiveSection} from './objective';
import {InboxPromptView} from './interface';

export {InboxPromptView};

export async function renderInboxSystemPrompt(view: InboxPromptView) {
        // If there are custom rules, prepend them
        view.customRules \u0026\u0026 [
            \u0027# Custom Rules\u0027,
            \u0027\u0027,
            view.customRules
        ].join(\u0027\
    const parts = [
        view.projectStructure ? renderRuleSection() : renderScratchStartRuleSection(),
        renderFormatSection(),
        renderToolSection(view),
        '# Context',
        'This section provides some already known information for user\'s request.',
        view.projectStructure ? renderStructureSection(view) : '',
        renderObjectiveSection(view),
    ];
    return parts.filter(v => !!v).join('\n\n');
}