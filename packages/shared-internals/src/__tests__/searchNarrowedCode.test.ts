/* eslint-disable max-len */
import {describe, test, expect} from 'vitest';
import {searchNarrowedCode} from '../editor/searchNarrowedCode.js';

describe('editor.searchNarrowedCode', () => {
    test('中间内容不匹配，也能正常找到', async () => {
        const content = [
            '...',
            'start',
            '...code...',
            'end',
            '...',
        ]
            .join('\n');
        const target = [
            'start',
            '...ellipsis...',
            'end',
        ]
            .join('\n');
        const result = searchNarrowedCode(content, target);
        expect(result?.block).toBe(['start', '...code...', 'end'].join('\n'));
    });

    test('起始行不存在时，返回null', async () => {
        const content = [
            'start',
            '...code...',
            'end',
        ]
            .join('\n');
        const target = [
            'start1',
            '...ellipsis...',
            'end',
        ]
            .join('\n');
        const result = searchNarrowedCode(content, target);
        expect(result).toBeNull();
    });

    test('结尾行不存在时，返回null', async () => {
        const content = [
            'start',
            '...code...',
            'end',
        ]
            .join('\n');
        const target = [
            'start',
            '...ellipsis...',
            'end1',
        ]
            .join('\n');
        const result = searchNarrowedCode(content, target);
        expect(result).toBeNull();
    });
});
