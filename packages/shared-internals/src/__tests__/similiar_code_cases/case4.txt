======== original
<template>
  <div class="sidebar" :class="{ 'sidebar-collapsed': !isOpen }">
    <div class="sidebar-content">
      <!-- 侧边栏内容区域 -->
      <div class="sidebar-header">
        <h3>侧边栏</h3>
      </div>
      <div class="sidebar-body">
        <!-- 这里可以添加侧边栏的具体内容 -->

      </div>
    </div>
  </div>
  <div class="main-content" :style="{ width: isOpen ? '70%' : '100%' }">
    <slot></slot>
  </div>
</template>

<script>
import { ipcRenderer } from 'electron';

export default {
  name: 'SideBar',
  data() {
    return {
      isOpen: false
    };
  },
  created() {
    console.log('SideBar component created, setting up IPC listener');
    ipcRenderer.on('toggle-sidebar', () => {
      console.log('Received toggle-sidebar event');
      this.isOpen = !this.isOpen;
      console.log('isOpen state:', this.isOpen);
    });
  },
  methods: {
    toggle() {
      this.isOpen = !this.isOpen;
    }
  }
};
</script>

<style>
.main-content {
  position: fixed;
  top: 60px;
  left: 0;
  height: calc(100vh - 60px);
  transition: all 0.3s ease;
}

.sidebar {
  position: fixed;
  top: 60px;
  right: 0;
  width: 30%;
  height: calc(100vh - 60px);
  background: #fff;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  z-index: 100;
}

.sidebar-collapsed {
  transform: translateX(100%);
}

.sidebar-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.sidebar-body {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}
</style>
======== search
<template>
  <div class="sidebar" :class="{ 'sidebar-collapsed': !isOpen }">
    <div class="sidebar-content">
      <!-- 侧边栏内容区域 -->
      <div class="sidebar-header">
        <h3>侧边栏</h3>
      </div>
      <div class="sidebar-body">
        <!-- 这里可以添加侧边栏的具体内容 -->
      </div>
    </div>
  </div>
  <div class="main-content" :style="{ width: isOpen ? '70%' : '100%' }">
    <slot></slot>
  </div>
</template>
======== matched
<template>
  <div class="sidebar" :class="{ 'sidebar-collapsed': !isOpen }">
    <div class="sidebar-content">
      <!-- 侧边栏内容区域 -->
      <div class="sidebar-header">
        <h3>侧边栏</h3>
      </div>
      <div class="sidebar-body">
        <!-- 这里可以添加侧边栏的具体内容 -->

      </div>
    </div>
  </div>
  <div class="main-content" :style="{ width: isOpen ? '70%' : '100%' }">
    <slot></slot>
  </div>
</template>