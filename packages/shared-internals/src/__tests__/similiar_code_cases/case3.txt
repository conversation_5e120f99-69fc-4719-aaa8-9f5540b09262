======== original
import request from '@/utils/request'

// 创建订单
export function createOrder(data) {
  return request({
    url: '/orders',
    method: 'post',
    data
  })
}

// 批量创建订单
export function batchCreateOrder(data) {
  return request({
    url: '/orders/batch',
    method: 'post',
    data
  })
}

// 更新订单
export function updateOrder(id, data) {
  return request({
    url: `/orders/${id}`,
    method: 'put',
    data
  })
}

// 删除订单
export function deleteOrder(id) {
  return request({
    url: `/orders/${id}`,
    method: 'delete'
  })
}

// 复制订单
export function copyOrder(id) {
  return request({
    url: `/orders/${id}/copy`,
    method: 'post'
  })
}

// 获取订单信息
export function getOrder(id) {
  return request({
    url: `/orders/${id}`,
    method: 'get'
  })
}

// 分页查询订单
export function getOrderPage(params) {
  return request({
    url: '/orders/page',
    method: 'get',
    params
  })
}

// 根据合同号查询订单列表
export function getOrdersByContractNo(contractNo) {
  return request({
    url: `/orders/contract/${contractNo}`,
    method: 'get'
  })
}

// 根据图样ID查询订单列表
export function getOrdersBySampleId(sampleId) {
  return request({
    url: `/orders/sample/${sampleId}`,
    method: 'get'
  })
}
======== search
export function copyOrder(id) {
  return request({
    url: `/api/orders/${id}/copy`,
    method: 'post'
  })
}
======== matched
export function copyOrder(id) {
  return request({
    url: `/orders/${id}/copy`,
    method: 'post'
  })
}