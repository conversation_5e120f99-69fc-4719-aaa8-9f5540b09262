======== original
plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'dagger.hilt.android.plugin'
    id 'com.google.gms.google-services'
}

dependencies {
    // Firebase dependencies temporarily removed
    ext.kotlin_version = '1.8.22'
    def room_version = "2.6.0"
    def coroutines_version = "1.7.3"
    def lifecycle_version = "2.6.2"
    def work_version = "2.8.1"
    def hilt_version = "2.48"
}
======== search
    def room_version = "2.5.2"
    def coroutines_version = "1.7.1"
    def lifecycle_version = "2.6.2"
    def work_version = "2.8.1"
    def hilt_version = "2.44"
======== matched
    def room_version = "2.6.0"
    def coroutines_version = "1.7.3"
    def lifecycle_version = "2.6.2"
    def work_version = "2.8.1"
    def hilt_version = "2.48"