======== original
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-color: #f4f4f4;
    background-image: url(../images/bg.png);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
}

.top-left-image {
    position: absolute;
    top: 10px;
    left: 10px;
    max-width: 300px;
    min-width: 20px;
    width: 25vw;
    height: auto;
}

.login-container {
    background-color: white;
    width: 500px;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    text-align: center;
    z-index: 10;
}

.login-container h2 {
    margin-bottom: 20px;
}

/* 统一表单元素样式 */
.login-container input[type="text"],
.login-container input[type="password"] {
    width: calc(100% - 40px);
    padding: 10px;
    margin: 10px 0;
    border: 1px solid #ccc;
    border-radius: 4px;
    text-align: center;
}

.login-container input[type="submit"] {
    width: 100%;
    padding: 10px;
    border: none;
    background-color: #003366;
    color: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

.login-container input[type="submit"]:hover {
    background-color: #002244;
}

.message-box {
    margin-top: 20px;
    padding: 10px;
    border: 1px solid #008000;
    background-color: #e0ffe0;
    color: #006400;
    border-radius: 4px;
    display: none;
}

.options-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0px 0 10px;
    padding: 0 20px;
}

.remember-me {
    text-align: left;
}

.remember-me input {
    width: auto;
    margin-right: 20px;
}

.remember-me label {
    cursor: pointer;
    user-select: none;
}

.register-link {
    color: #003366;
    text-decoration: none;
    font-size: 14px;
}

.register-link:hover {
    text-decoration: underline;
}

/* 输入框图标样式 */
#userId,
#userName,
#role,
#password {
    padding: 10px;
    background-size: 22px;
    background-repeat: no-repeat;
    background-position: 10px 7px;
}

/* 输入框图标 */
#userId { background-image: url(../images/userId.png); }
#userName { background-image: url(../images/userName.png); }
#role { background-image: url(../images/role.png); }
#password { background-image: url(../images/password.png); }
======== search
.options-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 5px 0 15px;
    padding: 0 20px;
}

.remember-me {
    text-align: left;
}
======== matched
.options-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0px 0 10px;
    padding: 0 20px;
}

.remember-me {
    text-align: left;
}