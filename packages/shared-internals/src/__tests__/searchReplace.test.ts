// 可以单独执行 ./node_modules/.bin/vitest packages/vscode/src/services/Composer/__tests__/
import path from 'node:path';
import {fileURLToPath} from 'node:url';
import {readFileSync, readdirSync} from 'node:fs';
import {describe, expect, test} from 'vitest';
import {PatchFileDelimiterError, applySearchReplaceChunk} from '../bridge/searchReplace.js';

const filename = fileURLToPath(import.meta.url);
const dirname = path.dirname(filename);

const extractDiffsFromContent = (testcase: string) => {
    const lines = testcase.split('\n');
    const [original, searchReplace, newContent] = lines.reduce<string[][]>(
        (result, line) => {
            if (line.startsWith('========')) {
                result.push([]);
            }
            else {
                result[result.length - 1].push(line);
            }
            return result;
        },
        []
    );
    return {
        original: original.join('\n'),
        searchReplace: searchReplace.join('\n'),
        newContent: newContent.join('\n'),
    };
};

const caseDir = 'search_replace_cases';
const submodules = readdirSync(path.join(dirname, caseDir));
const cases = submodules.filter(name => name.startsWith('case')).map(submodule => {
    const testcase = readFileSync(path.join(dirname, caseDir, submodule), 'utf-8');
    return {name: submodule, ...extractDiffsFromContent(testcase)};
});

describe('Composer', () => {
    for (const testcase of cases) {
        const {name, original, searchReplace, newContent} = testcase;
        test(`searchReplace.${name}`, () => {
            const result = applySearchReplaceChunk(original, searchReplace);
            expect(result.patchedContent).toEqual(newContent);
        });
    }

    test('流式输出时，可以正确输出截断的行号', () => {
        const original = [
            '第一行',
            '第二行',
            '第三行',
            '第四行',
            '第五行',
        ]
            .join('\n');
        const patchStr = [
            '<<<SEARCH<<<',
            '第二行',
            '====REPLACED_BY===',
            '替换',
            '>>>END>>>',
        ]
            .join('\n');
        const {patchedContent} = applySearchReplaceChunk(original, patchStr, {stream: true});
        expect(patchedContent).toEqual([
            '第一行',
            '替换',
        ]
            .join('\n'));
    });

    test('格式不正确时，可以返回错误，SEARCH 跟在 REPLACED 后面', () => {
        const patchStr = [
            '====REPLACED_BY===',
            '内容',
            '<<<SEARCH<<<',
            '第二行',
            '====REPLACED_BY===',
            '替换',
            '>>>END>>>',
        ]
            .join('\n');
        const {error} = applySearchReplaceChunk('第一行', patchStr);
        expect(error).toBeInstanceOf(PatchFileDelimiterError);
    });

    test('格式不正确时，可以返回错误，SEARCH 跟在 SEARCH 后面', () => {
        const patchStr = [
            '<<<SEARCH<<<',
            '内容',
            '<<<SEARCH<<<',
            '第二行',
            '====REPLACED_BY===',
            '替换',
            '>>>END>>>',
        ]
            .join('\n');
        const {error} = applySearchReplaceChunk('第一行', patchStr);
        expect(error).toBeInstanceOf(PatchFileDelimiterError);
    });

    test('格式不正确时，可以返回错误，REPLACE 没有跟在 SEARCH 后面', () => {
        const patchStr = [
            '>>>END>>>',
            '第二行',
            '====REPLACED_BY===',
            '替换',
            '>>>END>>>',
        ]
            .join('\n');
        const {error} = applySearchReplaceChunk('第一行', patchStr);
        expect(error).toBeInstanceOf(PatchFileDelimiterError);
    });

    test('格式不正确时，可以返回错误，END 没有跟在 REPLACE 的后面', () => {
        const patchStr = [
            '<<<SEARCH<<<',
            '替换',
            '>>>END>>>',
        ]
            .join('\n');
        const {error} = applySearchReplaceChunk('第一行', patchStr);
        expect(error).toBeInstanceOf(PatchFileDelimiterError);
    });
});
