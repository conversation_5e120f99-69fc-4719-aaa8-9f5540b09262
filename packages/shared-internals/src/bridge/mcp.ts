export const MCP_SETTINGS_FILE_PATH = '.comate/mcp.json';

export interface MCPTextContent {
    type: 'text';
    text: string;
}

export interface MCPImageContent {
    type: 'image';
    data: string;
    mimeType: string;
}

export interface MCPAudioContent {
    type: 'audio';
    data: string;
    mimeType: string;
}

export interface MCPResourceContent {
    type: 'resource';
    resource: {
        uri: string;
        mimeType?: string;
        text?: string;
        blob?: string;
    };
}

export interface MCPServerForWebview {
    name: string;
    disabled?: boolean;
    status: 'connected' | 'connecting' | 'disconnected';
    error?: string;
    tools?: MCPToolForWebview[];
    logPath: string;
}

export interface MCPToolForWebview {
    name: string;
    description?: string;
    inputSchema?: object;
    autoApprove?: boolean;
}

export interface MCPServerConfigForWebview {
    command?: string;
    args?: string[];
    env?: Record<string, string>;
    cwd?: string;

    url?: string;
    disabled?: boolean;
}
