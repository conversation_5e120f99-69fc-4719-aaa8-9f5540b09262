import {ActivationContext} from '../schema.js';
import {Information} from './information.js';
import {SystemInfoParsed} from './shared.js';

export const ACTION_CHAT_QUERY = 'CHAT_QUERY';

// TODO 目前只有知识集，还需要文件以及文件夹

export interface ChatQueryParsed {
    messageId: string;
    pluginName: string;
    capability?: string | undefined;
    query: string;
    data?: any;
    informationList: Information[];
    enableSmartApply?: boolean;
}

export interface ChatQueryPayload {
    pluginName: string;
    input: ChatQueryParsed;
    context: ActivationContext;
    systemInfo: SystemInfoParsed;
    isRegenerated?: boolean;
    sessionUuid?: string;
}

export const ACTION_CHAT_TASK_PROGRESS = 'CHAT_TASK_PROGRESS';
