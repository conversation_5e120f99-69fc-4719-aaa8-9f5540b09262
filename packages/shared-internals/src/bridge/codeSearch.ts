// 为三方插件提供代码检索能力，包括embedding和keyword检索
export const ACTION_CODE_SEARCH = 'CODE_SEARCH';

export interface CodeSearchPayload {
    /** 检索query */
    queries: string[];
    /** 检索范围 */
    path?: string[];
    /** 是否需要关键字搜索 */
    needKeywordSearch?: boolean;
    /** 是否需要重建索引后再进行检索（重建索引会有一定耗时） */
    needRecreateIndex?: boolean;
    /** 重建索引最大等待耗时，单位为秒 */
    maxDuration?: number;
}

export interface CodeSearchPluginPayload {
    pluginName: string;
    capabilityName?: string;
    codeSearchPayload: CodeSearchPayload;
}


export interface Position {
    line: number;
    column: number;
}

export type CodeChunkType ='embedding' | 'keyword';

export interface CodeChunk {
    repo: string;
    type: CodeChunkType;
    path: string; // 当 type 为 embedding 时，path 为绝对路径，其余情况为工作区相对路径
    content: string; // 上下文信息
    commentContent?: string; // 代码解释
    contentStart: Position;
    contentEnd: Position;
    distance?: number;
    symbolName?: string;
    symbolType?: string;
}
