// 思考过程中的任务的状态
export enum TaskStatus {
    INIT = 'INIT',
    PROCESSING = 'PROCESSING',
    SUCCEED = 'SUCCEED',
    FAIL = 'FAIL',
}

interface SubTask {
    /** 任务描述 */
    desc: string;
    status: TaskStatus;
    /** 任务详情 */
    detail?: string[];
    /** type 决定当前思考过程的 tag 的显示 */
    taskType: 'SEARCH' | 'PLAN' | 'REASONING' | 'THOUGHT' | 'CODE_GENERATE' | 'ANSWER' | 'ANALYZE';
}
// 思考过程中的任务
export interface Task extends SubTask {
    /** 二级子任务 */
    subTasks?: SubTask[];
}

interface BaseDynamicSection {
    label: string;
    done: boolean;
    collapsible: boolean;
}

// 对应多项任务进度展示区域的数据
export interface DynamicActionItems extends BaseDynamicSection {
    type: 'actionItems';
    tasks: Task[];
    relevantFiles?: string[];
}

// 代码块
interface CodeChunk {
    filePath: string;
    code: string;
    language: string;
    startLine: number;
    endLine: number;
}

interface KnowledgeChunk {
    id: string;
    content: string; // 文档内容
    url: string; // 原文地址
    title: string; // 原文标题
}

export interface WebChunk {
    id: number; // 网页id，顺序id用于展示角标
    title: string; // 网页标题
    url: string;
}

// 对应代码块展示区域的数据
export interface DynamicCodeChunks extends BaseDynamicSection {
    type: 'codeChunks';
    codeChunks?: CodeChunk[];
    knowledgeChunks?: KnowledgeChunk[];
    webChunks?: WebChunk[];
}

// 对应关联文件/目录展示区域
export interface DynamicRelativeFiles extends BaseDynamicSection {
    type: 'relativeFiles';
    files: string[];
}

export type OperateType = 'ADD' | 'CHANGE' | 'DELETE';

export interface Snippet {
    original: string;
    updated: string;
}

export interface File {
    filepath: string;
    operateType: OperateType;
    steps: string[]; // 文件对应的执行步骤，不需要展示
    snippetsDetail?: Snippet[];
}

export interface Step {
    id: number;
    desc: string;
    status: TaskStatus;
    tasks: Task[];
}

export interface DynamicCodeGenSteps extends BaseDynamicSection {
    type: 'codeGenSteps';
    steps: Step[];
}

export interface DynamicReminder extends BaseDynamicSection {
    type: 'reminder';
    reminder: string;
}

export interface Notification {
    notificationType: 'info' | 'fail' | 'success';
    title: string;
    path?: string;
}

export interface DynamicNotification extends BaseDynamicSection {
    type: 'notification';
    notifications: Notification[];
}

// DynamicSection 是 Message 中会动态更新的一个区域
export type DynamicSection =
    | DynamicActionItems
    | DynamicCodeChunks
    | DynamicCodeGenSteps
    | DynamicRelativeFiles
    | DynamicReminder
    | DynamicNotification;
