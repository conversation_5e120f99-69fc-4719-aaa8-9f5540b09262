// import {Diagnostic, DiagnosticRelatedInformation, DiagnosticTag} from 'vscode-languageserver-types';
import {ActivationContext, SystemInfoParsed} from '../schema.js';

// IDE 发起的获取 DIAGNOSTIC
export const ACTION_COMATE_PLUS_DIAGNOSTIC_SCAN = 'COMATE_PLUS_DIAGNOSTIC_SCAN';

// 诊断扫描任务进展上报
export const ACTION_DIAGNOSTIC_SCAN_TASK_PROGRESS = 'DIAGNOSTIC_SCAN_TASK_PROGRESS';

// 向plugin发起诊断扫描
export const ACTION_DIAGNOSTIC_SCAN = 'DIAGNOSTIC_SCAN';

// 获取诊断扫描问题数量
export const ACTION_DIAGNOSTIC_SCAN_TASK_COUNT = 'DIAGNOSTIC_SCAN_TASK_COUNT';

export interface DiagnosticScanPayload {
    pluginName: string;
    capabilityName: string;
    systemInfo: SystemInfoParsed;
}

export interface DiagnosticScanInvokePayload {
    pluginName: string;
    capabilityName: string;
    diagnosticScanId: string;
    scanType: DiagnosticScanTypes;
    systemInfo: SystemInfoParsed;
    changedFiles?: DiagnosticScanChangedFiles;
    context?: ActivationContext;
}

export interface DiagnosticScanChangedFiles {
    added: string[];
    deleted: string[];
    modified: string[];
}
export type DiagnosticScanTypes = 'init' | 'change' | 'open' | 'count';

// 诊断信息，触发侧边栏后对话的依据，data针对插件变化
export interface RepairData {
    pluginName?: string; // 负责处理的插件
    capability?: string; // 负责处理的能力
    query: string;
    data: any;
}

interface Range {
    startLine: number;
    startCharacter: number;
    endLine: number;
    endCharacter: number;
}

export interface DiagnosticCacheValue {
    hash?: string;
    diagnostics: DiagnosticInfo[];
}
export interface DiagnosticInfo {
    // 错误的位置，Range {startLine: number, startCharacter: number, endLine: number, endCharacter: number}
    range: Range;
    // 诊断信息的具体内容，描述，可以写多一点,for vscode
    textMessage: string;
    // html版，诊断信息的具体内容，描述，可以写多一点,for jetbrains
    htmlMessage?: string;
    // vscode：vscode.DiagnosticSeverity.Error、vscode.DiagnosticSeverity.Warning
    // IDEA支持：ERROR、WARNING
    severity: 'ERROR' | 'WARNING';
    // 用于触发侧边栏对话
    repairData: RepairData;
    // 错误的源码，必须，否则无法定位错误
    sourceCode: string;
    // 上下文，辅助定位错误位置
    contextCode: string[];

    /**
     * A code or identifier for this diagnostic.
     * Should be used for later processing, e.g. when providing {@link CodeActionContext code actions}.
     */
    // 可选的诊断代码，包含规则标识符和相关文档链接。for vscode
    code?: {value: string | number, target: string};
    // source: 诊断信息的来源（通常是插件的名称）。for vscode
    source?: string;
}

export interface DiagnosticScanTaskProgressChunk {
    filename: string;
    hash?: string;
    diagnostics: DiagnosticInfo[];
}

export interface DiagnosticScanTaskProgressPayload {
    chunk: DiagnosticScanTaskProgressChunk;
    capabilityName?: string;
}

export interface DiagnosticScanCountPayload {
    chunk: number;
    capabilityName?: string;
}
