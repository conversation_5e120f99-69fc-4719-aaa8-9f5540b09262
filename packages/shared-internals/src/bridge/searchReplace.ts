/* eslint-disable max-len */
import {stringSimilarity} from 'string-similarity-js';
import flow from 'lodash/flow.js';
import {searchNarrowedCode} from '../editor/searchNarrowedCode.js';
import {searchSimiliarCode} from '../editor/searchSimiliarCode.js';

export interface ParsedPatch {
    search: string[];
    replace: string[];
}

interface ParseState {
    context: 'none' | 'search' | 'replace';
    current: ParsedPatch | null;
}

export class PatchFileDelimiterError extends Error {
    message = 'Delimiter error for patch format！Delimiters should be one of the following:'
        + '"\n<<< SEARCH <<<\n", "\n==== REPLACED_BY ====\n", "\n>>> END >>>\n"';
}

// eslint-disable-next-line complexity
function parsePatchString(input: string): ParsedPatch[] {
    // A patch block looks like this:
    //
    // ```
    // <<<SEARCH<<<
    // content to search
    // ====REPLACED_BY====
    // content to replace
    // >>>END>>>
    // ```
    //
    // For more stable parsing, we allow `<<<<`, `====` and `>>>>` to have different repetive length,
    // but it requires at least 4 in order not to be confused with the `<<`, `>>` or `===` operators
    const results: ParsedPatch[] = [];
    const state: ParseState = {context: 'none', current: null};

    for (const line of input.split('\n')) {
        if (/^[<=]+\s*SEARCH\s*[<=#]+$/.test(line)) {
            if (state.context === 'replace' || state.context === 'search') {
                throw new PatchFileDelimiterError();
            }

            if (state.current && state.context === 'none') {
                results.push(state.current);
            }

            state.context = 'search';
            state.current = {search: [], replace: []};
        }
        else if (/^[<=>]+\s*REPLACED?_BY\s*[<=>]+$/.test(line)) {
            if (state.context !== 'search') {
                throw new PatchFileDelimiterError();
            }
            state.context = 'replace';
        }
        else if (/^[>=]+\s*END\s*[>=]+$/.test(line)) {
            if (state.context !== 'replace') {
                throw new PatchFileDelimiterError();
            }
            state.context = 'none';
        }
        else if (state.context === 'search' && state.current) {
            state.current.search.push(line);
        }
        else if (state.context === 'replace' && state.current) {
            state.current.replace.push(line);
        }
        else if (line) {
            throw new PatchFileDelimiterError();
        }
    }

    if (state.current) {
        results.push(state.current);
    }

    return results;
}

interface FlowInput {
    error?: SearchReplacePatchError;
    patchedContent: string;
}

interface Opts {
    /** 是否流式，流式过程中只会做简单的搜索替换，结束或会做更详细的相似度匹配 */
    stream?: boolean;
}

export class SearchReplacePatchError extends Error {
    constructor(readonly patchIndex: number, readonly patches: ParsedPatch[], readonly content: string) {
        super();
    }

    get result() {
        const currentPatch = this.patches[this.patchIndex];
        const matcher = currentPatch.search.join('\n');
        if (matcher.length > 20) {
            return {
                similiarBlock: '',
                failedPatchIndex: this.patchIndex,
                patches: this.patches,
            };
        }

        const result = searchSimiliarCode(this.content, matcher, {threshold: 0, maxLineGap: 0});
        // 扩写为上下两行
        const similiarBlock = result
            ? this.content.slice(
                Math.max(0, result.start - 2),
                result.end + 2
            )
            : '';
        return {
            /** 失败的补丁序号，从0开始，如果全成功了就是-1 */
            failedPatchIndex: this.patchIndex,
            /** 解析出来的所有补丁 */
            patches: this.patches,
            /** 当前补丁没匹配上，与其最相关的代码块 */
            similiarBlock,
        };
    }
}

const trim = (input: string) => input.replace(/^(\r?\n)+/, '').replace(/^(\s|\t)+/, '');
const detectLineEnding = (text: string) => {
    const rn = text.indexOf('\r\n');
    const n = text.indexOf('\n');
    const r = text.indexOf('\r');

    if (rn !== -1) {
        return '\r\n'; // Windows风格
    }
    if (n !== -1) {
        return '\n'; // Unix/Linux/macOS风格
    }
    if (r !== -1) {
        return '\r'; // 旧的Mac风格(罕见)
    }
    return '\n'; // 默认
};

/**
 * 1. 去除检索时，模型多生成行首换行
 * 2. 增加模型生成时，行不完整的匹配
 * 3. 增加通过匹配首尾行收窄范围的匹配
 * 4. 增加相似度 n+4 行的相似度匹配
 * 5. 以上策略均为匹配时，返回修改失败，并提供最相似代码上下2行代码，进行二次修改
 */
export const applySearchReplaceChunk = (originalContent: string, searchReplaceChunk: string, opts?: Opts) => {
    try {
        const patches = parsePatchString(searchReplaceChunk);
        const eol = detectLineEnding(originalContent);

        if (!patches.length && !opts?.stream) {
            return {
                error: new PatchFileDelimiterError(),
                patchedContent: originalContent,
                patches: [],
            };
        }

        const {streamLineCount, patchedContent, error} = flow(
            patches.map(
                // eslint-disable-next-line max-statements, complexity
                (patch, i) => (input: FlowInput): FlowInput & {streamLineCount?: number} => {
                    if (input.error) {
                        return input;
                    }

                    const normalizedSearchChunk = trim(patch.search.join(eol));
                    const normalizedSearchChunkLines = normalizedSearchChunk.split(/\r?\n/);
                    const replaceChunk = patch.replace.join(eol);
                    const patchedContent = input.patchedContent;
                    if (opts?.stream) {
                        const chunkIndex = patchedContent.indexOf(normalizedSearchChunk);
                        const replaceStart = patchedContent.slice(0, chunkIndex).split(/\r?\n/).length - 1;
                        const end = replaceStart + patch.replace.length;
                        return {
                            streamLineCount: end,
                            patchedContent: patchedContent.replace(normalizedSearchChunk, trim(replaceChunk)),
                        };
                    }
                    else {
                        const chunkIndex = patchedContent.indexOf(normalizedSearchChunk);
                        if (chunkIndex !== -1) {
                            if (patch.search.length === 1) {
                                return {
                                    patchedContent: patchedContent.replace(normalizedSearchChunk, trim(replaceChunk)),
                                };
                            }
                            else {
                                const patchedContentLines = patchedContent.split(/\r?\n/);
                                const replaceStart = patchedContent.slice(0, chunkIndex).split(/\r?\n/).length - 1;
                                const replaceChunkLines = replaceChunk.split(/\r?\n/);
                                const replaceEnd = replaceStart + normalizedSearchChunkLines.length;
                                // eslint-disable-next-line max-depth
                                if (!replaceChunk) {
                                    return {
                                        patchedContent: [
                                            ...patchedContentLines.slice(0, replaceStart),
                                            ...patchedContentLines.slice(replaceEnd),
                                        ]
                                            .join(eol),
                                    };
                                }

                                // 这个是为了处理模型返回缩进不对的问题，详情见单测的case2
                                replaceChunkLines[0] = patchedContentLines[replaceStart].replace(
                                    normalizedSearchChunkLines[0],
                                    trim(replaceChunkLines[0])
                                );
                                return {
                                    patchedContent: [
                                        ...patchedContentLines.slice(0, replaceStart),
                                        replaceChunkLines.join(eol),
                                        ...patchedContentLines.slice(replaceEnd),
                                    ]
                                        .join(eol),
                                };
                            }
                        }
                        const codeSearched = searchNarrowedCode(patchedContent, normalizedSearchChunk);
                        if (codeSearched) {
                            if (stringSimilarity(normalizedSearchChunk, codeSearched.block) > 0.67) {
                                return {
                                    patchedContent: patchedContent.replace(normalizedSearchChunk, codeSearched.block),
                                };
                            }
                        }

                        if (patchedContent.length <= 1000 && normalizedSearchChunk.length <= 20) {
                            const similiarCode = searchSimiliarCode(patchedContent, normalizedSearchChunk, {
                                maxLineGap: 4,
                                threshold: 0.86,
                            });
                            if (similiarCode) {
                                return {
                                    patchedContent: patchedContent.replace(normalizedSearchChunk, similiarCode.block),
                                };
                            }
                        }

                        return {
                            error: new SearchReplacePatchError(i, patches, originalContent),
                            patchedContent: input.patchedContent,
                        };
                    }
                }
            )
        )({patchedContent: originalContent, streamLineCount: 0});

        return {
            error,
            patchedContent: opts?.stream
                ? patchedContent.split(/\r?\n/).slice(0, streamLineCount).join(eol)
                : patchedContent,
            patches,
        };
    }
    catch (ex) {
        return {
            error: ex,
            patchedContent: originalContent,
            patches: [],
        };
    }
};
