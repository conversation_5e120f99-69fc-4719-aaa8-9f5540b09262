import {DrawElement} from './element.js';
import {DynamicSection} from './section.js';
import {ActionSet} from './action.js';

export type ChunkContent = string | string[] | DrawElement | DrawElement[];
export type SectionContent = DynamicSection | DynamicSection[];
/**
 * 更新用户看到的消息内容
 */
export interface DrawChunk {
    /** 指定更新用户的回复信息 */
    command: 'draw';

    /** 重绘后的内容 */
    content: ChunkContent;
}

/**
 * 更新用户看到的动态section
 */
export interface SectionChunk {
    /** 指定更新用户的section */
    command: 'section';

    /** 重绘后的section */
    sections: {dynamicSections: DynamicSection[], dynamicFooterSections: DynamicSection[]};
    /** 重绘后的content */
    content: string;
    /** 重绘后的action及配置  */
    /** NOTE: 仅用于更新整个消息的action，不包括代码块内的action */
    actionSet?: ActionSet;
}

/**
 * 告知用户执行失败
 */
export interface FailChunk {
    /** 指定告知用户任务失败 */
    command: 'fail';

    /** 失败的提示信息 */
    content: ChunkContent;
}

/**
 * 任务处理过程的反馈片段
 */
export type TaskProgressChunk = DrawChunk | FailChunk | SectionChunk | ChunkContent;

export interface TaskProgressPayload {
    messageId?: number;
    taskId: string;
    chunk: TaskProgressChunk;
    capabilityName?: string;
    enableSmartApply?: boolean;
}
