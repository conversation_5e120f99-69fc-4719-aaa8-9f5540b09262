export interface WorkspaceEditEntryMetadata {
    /**  A flag which indicates that user confirmation is needed. */
    needsConfirmation: boolean;
    /** A human-readable string which is rendered prominent. */
    label: string;
    /** A human-readable string which is rendered less prominent on the same line. */
    description?: string;
}

export interface Position {
    line: number;
    character: number;
}

interface AcceptByReplace {
    type: 'replace';
    path: string;
    range: {start: Position, end: Position};
    newText: string;
    oldText?: string;
    metadata?: WorkspaceEditEntryMetadata;
}

interface AcceptByInsert {
    type: 'insert';
    path: string;
    position: Position;
    newText: string;
    metadata?: WorkspaceEditEntryMetadata;
}

type AcceptByType = AcceptByReplace | AcceptByInsert;
export type Accept = AcceptByType[];

// 为三方插件提供目录批量采纳并上报的能力
export const ACTION_BATCH_ACCEPT = 'ACCEPT_DIR';

export interface BatchAcceptPayload {
    /** 批量采纳文件列表 */
    paths: string[];
}

export interface BatchAcceptPluginPayload {
    pluginName: string;
    capabilityName?: string;
    batchAcceptPayload: BatchAcceptPayload;
}
