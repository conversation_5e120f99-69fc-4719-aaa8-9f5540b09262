/* eslint-disable max-statements */
/* eslint-disable complexity */
import {EOL} from 'os';

/** 修复开头奇数的空格的脏数据 */
const trimSpace = (line: string) => {
    const match = /^[ ]+/.exec(line);
    const spaces = match?.[0].length || 0;
    if (spaces % 2 === 1) {
        return line.replace(/^\s/, '');
    }
    return line;
};

const prettierLine = (line: string) => {
    // Go语言Tab缩进会生成带转义的\t
    // jsx语言的"会双转义
    return line
        .replace(/^\\t/g, '\t')
        .replace(/\\"/g, '"')
        .replace(/\\"/g, '"')
        .replace(/\\\\n/g, '\\n');
};

export const safeSplitEol = (text: string) => {
    return text.split(/\r?\n/);
};

export const twoStepSearchLineFromList = (lines: string[], line: string) => {
    // 注释的情况，空格会是奇数个，找两次，如果trim的没有，再找次原文
    const lineSeqRemovedFindByTrimSpace = lines.indexOf(trimSpace(line));
    return lineSeqRemovedFindByTrimSpace === -1
        ? lines.indexOf(line)
        : lineSeqRemovedFindByTrimSpace;
};

const isSlicedLine = (text: string | undefined) => (text ? text.startsWith('-') : undefined);
const isAppendLine = (text: string | undefined) => (text ? text.startsWith('+') : undefined);
const isDiffHeaddingLine = (text: string | undefined) => (text ? text.startsWith('@@') : undefined);
const removeDiffLineMark = (text: string) => text.replace(/^-/, '').replace(/^\+/, '');

const trimStart = (lines: string[]) => lines.map(line => line.trimStart());

const customSplitDiffLine = (diff: string) => {
    if (!diff) {
        return [];
    }

    const lines = safeSplitEol(diff);
    // 大模型会偶现，比如第三行这类case，把源码行挤到标注位去了，直接丢弃后面的部分
    // --- gin_not_found_package.go
    // +++ gin_not_found_package.go
    // @@ -2,6 +2,7 @@ package main·

    //  import (
    //  \t\"net/http\"
    // +\t\"time\"

    //  \t\"github.com/gin-gonic/gin\"
    //  \t\"github.com/gin-gonic/gin/binding\"
    // @@ -9,6 +10,13 @@ import (
    //  \t\"icode.baidu.com/baidu/bcloud/auto-debug-test/src/usr\"
    //  )
    return lines.reduce<string[]>(
        (result, line) => {
            if (line.startsWith('@@')) {
                const matched = /@@(.*)@@\s(.*)$/.exec(line);
                if (matched) {
                    result.push('@@ ... @@');
                    return result;
                }
            }
            result.push(line);
            return result;
        },
        []
    );
};

const hasNoDiffPrefixes = (lines: string[]) => {
    return !lines.some(line => {
        return isSlicedLine(line) || isAppendLine(line) || isDiffHeaddingLine(line);
    });
};
interface Option {
    eol: string;
    /** 设置为`true`时，只要diff行遍历结束，就立刻返回内容，不生成全部的代码 */
    returnContentWhenDiffLineLoopFinish?: boolean;
    /** 设置为`true`时，匹配行时忽略缩进 */
    skipIndentWhenMatchDiffLine?: boolean;
    throwErrorWhenLineCanNotMathced?: boolean;
}
/**
 * 将 diff 应用到原始内容上, 生成新的内容，具体可看测试用例
 * @param originalContent 原始内容
 * @param diffWithoutLineSeq diff 内容，但是没有行序号，起止的标识 `@@ ... @@`
 */
export function applyDiff(originalContent: string, diffWithoutLineSeq: string, opts: Option) {
    const lines = safeSplitEol(originalContent);
    const diffLines = customSplitDiffLine(diffWithoutLineSeq.replace(/\n$/, ''));
    let currentLine = 0;

    // 如果diff字符串中没有任何+-号标记，认为是模型的脏输出，直接把diff作为原文返回
    if (hasNoDiffPrefixes(diffLines) && diffLines.length !== lines.length) {
        return diffWithoutLineSeq;
    }

    for (const [i, diffLine] of diffLines.entries()) {
        if (diffLine.startsWith('---') || diffLine.startsWith('+++') || diffLine.startsWith('@@')) {
            continue;
        }

        if (isSlicedLine(diffLine)) {
            // 连续减号，只要第一个序号匹配上，就按行数删除
            if (isSlicedLine(diffLines[i + 1])) {
                continue;
            }

            const slicedChunk = [prettierLine(removeDiffLineMark(diffLine))];
            let startIndex = i;
            while (isSlicedLine(diffLines[startIndex - 1])) {
                const slicedLine = prettierLine(removeDiffLineMark(diffLines[startIndex - 1]));
                slicedChunk.unshift(slicedLine);
                startIndex = startIndex - 1;
            }

            let lineSeqRemoved = -1;
            let searchIndex = 0;
            while (lineSeqRemoved === -1 && searchIndex < slicedChunk.length) {
                const lineRemoved = slicedChunk[0];
                const allLines = lines.slice(currentLine, lines.length);
                lineSeqRemoved = twoStepSearchLineFromList(trimStart(allLines), lineRemoved.trimStart());
                searchIndex = searchIndex + 1;
            }

            if (lineSeqRemoved !== -1) {
                currentLine = currentLine + lineSeqRemoved - (searchIndex - 1);
                lines.splice(currentLine, slicedChunk.length);
            }

            if (opts.throwErrorWhenLineCanNotMathced) {
                throw new Error('line not found');
            }
        }
        else if (isAppendLine(diffLine)) {
            if (isAppendLine(diffLines[i + 1])) {
                continue;
            }
            // 连续加号批量插入
            const appendChunk = [prettierLine(removeDiffLineMark(diffLine))];
            let startIndex = i;
            while (isAppendLine(diffLines[startIndex - 1])) {
                const appendLine = prettierLine(removeDiffLineMark(diffLines[startIndex - 1]));
                appendChunk.unshift(appendLine);
                startIndex = startIndex - 1;
            }

            lines.splice(currentLine, 0, ...appendChunk);
            currentLine = currentLine + appendChunk.length;
        }
        else {
            const originalLine = prettierLine(diffLine);
            const allLines = lines.slice(currentLine, lines.length);
            const lineSequence = twoStepSearchLineFromList(trimStart(allLines), originalLine.trimStart());
            const matchedLineSeq = currentLine + lineSequence;
            if (lineSequence !== -1) {
                currentLine = matchedLineSeq + 1;
            }

            if (opts.throwErrorWhenLineCanNotMathced) {
                throw new Error('line not found');
            }
        }
    }

    if (opts?.returnContentWhenDiffLineLoopFinish) {
        return lines.slice(0, currentLine).join(EOL);
    }

    return lines.join(opts.eol);
}
