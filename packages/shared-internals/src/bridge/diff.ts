const findStringIndexes = (str: string, list: string[]) => {
    return list.reduce<number[]>(
        (result, compared, i) => {
            if (str.trimStart() === compared.trimStart()) {
                result.push(i);
            }
            return result;
        },
        []
    );
};

/**
 * 找到代码块在原内容中的起始行号
 * @param codeChunk 代码块
 * @param originalContent 原代码块
 * @returns
 */
export const findCodeChunkStartLineIndex = (codeChunk: string, originalContent: string) => {
    const i = originalContent.indexOf(codeChunk);
    if (i !== -1) {
        return Math.max(0, originalContent.slice(0, i).split(/\r?\n/).length - 1);
    }

    const lines = originalContent.split(/\r?\n/);
    const newLines = codeChunk.split(/\r?\n/);
    for (const [i, newLine] of newLines.entries()) {
        const searchedIndexes = findStringIndexes(newLine, lines);
        if (searchedIndexes.length > 1) {
            if (i === newLines.length - 1) {
                return searchedIndexes[0] - i;
            }
            else {
                continue;
            }
        }

        if (searchedIndexes.length === 1) {
            return searchedIndexes[0] - i;
        }

        return -1;
    }
    return -1;
};

interface Hunk {
    oldStart: number; // hunk 在旧文件中的起始行号
    newStart: number; // hunk 在新文件中的起始行号
    oldCount: number; // 旧文件中该 hunk 包含的行数（上下文 + 删除）
    newCount: number; // 新文件中该 hunk 包含的行数（上下文 + 添加）
    lines: string[]; // hunk 中所有的 diff 内容行，每行末尾包含换行符
}

const isMatchLine = (line: string) => {
    if (typeof line !== 'string') {
        return false;
    }
    return line.startsWith('-') || line.startsWith(' ') || line === '';
};
// eslint-disable-next-line
export const fixUdiffLineMarker = (diff: string, originalContent: string) => {
    const fixedLines: string[] = [];
    const diffLines = diff.split(/\r?\n/);
    let currentHunk: Hunk | null = null;
    const globalOldStartLineIndex = 0;
    for (let i = 0; i < diffLines.length; i++) {
        const diffLine = diffLines[i];
        if (diffLine.startsWith('@@')) {
            if (currentHunk) {
                const {
                    oldStart: os,
                    oldCount: oc,
                    newStart: ns,
                    newCount: nc,
                } = currentHunk;
                const header = `@@ -${os},${oc} +${ns},${nc} @@`;
                fixedLines.push(header, ...currentHunk.lines);
            }
            currentHunk = {
                oldStart: globalOldStartLineIndex,
                newStart: 0,
                oldCount: 0,
                newCount: 0,
                lines: [],
            };
        }
        else if (currentHunk) {
            if (diffLine.startsWith('---') || diffLine.startsWith('+++')) {
                continue;
            }
            else if (diffLine.startsWith('+')) {
                currentHunk.lines.push(diffLine);
            }
            else if (isMatchLine(diffLine) && currentHunk) {
                let range = 1;
                let codeChunk = diffLine.replace(/^(-|\s)/, '');
                currentHunk.lines.push(diffLine);
                // eslint-disable-next-line
                while (typeof diffLines[i + range] === 'string' && !diffLines[i + range].startsWith('@@')) {
                    // eslint-disable-next-line
                    if (isMatchLine(diffLines[i + range])) {
                        codeChunk += `\n${diffLines[i + range].replace(/^(-|\s)/, '')}`;
                    }
                    currentHunk.lines.push(diffLines[i + range]);
                    range = range + 1;
                }

                const startIndex = findCodeChunkStartLineIndex(codeChunk, originalContent);
                // eslint-disable-next-line
                if (startIndex === -1) {
                    currentHunk = null;
                }
                else {
                    currentHunk.oldStart = startIndex + 1;
                    currentHunk.oldCount = codeChunk.split(/\r?\n/).length;
                    i = i + range - 1;
                }
            }
        }
    }

    if (currentHunk) {
        const {
            oldStart: os,
            oldCount: oc,
            newStart: ns,
            newCount: nc,
        } = currentHunk;
        const header = `@@ -${os},${oc} +${ns},${nc} @@`;
        fixedLines.push(header, ...currentHunk.lines);
    }

    return fixedLines.join('\n');
};
