import {ActivationContext} from '../schema.js';
import {Range, SystemInfoParsed} from './shared.js';
import {DiagnosticInfo} from './diagnostic.js';

/** IDE侧发起的完整的扫描任务 */
export const ACTION_SCAN_QUERY = 'SCAN_QUERY';

export const ACTION_WILL_SCAN = 'WILL_SCAN';

export const ACTION_SCAN_TASK = 'SCAN_TASK';

export const ACTION_RELEASE_SCAN_TASK = 'RELEASE_SCAN_TASK';

export const ACTION_SCAN_TASK_PROGRESS = 'SCAN_TASK_PROGRESS';

/** 获取是否存在存量扫描结果 */
export const ACTION_SCAN_CACHE_COUNT = 'SCAN_CACHE_COUNT';

/** 初始化拉起SA扫描server */
export const ACTION_COMATE_PLUS_SA_SCAN_INIT = 'COMATE_PLUS_SA_SCAN_INIT';

/** IDE发起扫描任务获取SA扫描结果 */
export const ACTION_COMATE_PLUS_SA_SCAN_DIAGNOSTIC = 'COMATE_PLUS_SA_SCAN_DIAGNOSTIC';
/** SA扫描结果 */
export const ACTION_SA_SCAN_DIAGNOSTIC = 'SA_SCAN_DIAGNOSTIC';
/** 获取三方插件返回的SA扫描结果 */
export const ACTION_SA_SCAN_DIAGNOSTIC_RESULT = 'SA_SCAN_DIAGNOSTIC_RESULT';

export interface ScanQueryPayload {
    scanId: string;
    context: ActivationContext;
    systemInfo: SystemInfoParsed;
}

export interface WillScanPayload extends ScanQueryPayload {
    pluginName: string;
    capabilityName: string;
}

export interface ReleaseScanTaskPayload {
    scanId: string;
    pluginName: string;
    capabilityName: string;
}

export interface ScanTaskPayload {
    scanId: string;
    pluginName: string;
    capabilityName: string;
    description: string;
    ranges: Range[];
}

/** 表达参与扫描任务的目标 */
export interface ScanHandleGoal {
    /** 说明本次处理的目标 */
    description: string;
    /** 是否独占处理，如果独占并被选中，则其它插件能力都不会处理本次扫描 */
    exclusive?: boolean;
    /** 计划处理和修改的代码块 */
    ranges: Range[];
}

/** 扫描意愿结果 */
export interface ReportWillScanPayload {
    scanId: string;
    pluginName: string;
    capabilityName: string;
    goal: ScanHandleGoal | false;
}

export interface SAScanDiagnosticResult {
    absolutePath: string;
    diagnostics: DiagnosticInfo[];
}

export interface SADiagnosticScanResultPayload {
    chunk: SAScanDiagnosticResult | boolean;
    capabilityName?: string;
}

export type ScanTypes = 'init' | 'getResult' | 'destroy';

export interface SADiagnosticScanInvokePayload {
    pluginName: string;
    capabilityName: string;
    systemInfo: SystemInfoParsed;
    scanType: ScanTypes;
    absolutePath?: string;
    context?: ActivationContext;
}
