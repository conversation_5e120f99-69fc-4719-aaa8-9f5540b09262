import {ACTION_LOG} from './log.js';
import {
    ACTION_SESSION_FINISH,
    ChannelImplement,
    Session,
    SessionOptions,
    SessionInit,
    Execution,
    SessionMessage,
} from './session.js';
import {TaskManager} from './taskManager.js';

export interface ChannelImplementMaybe {
    on: ChannelImplement['on'];
    send?: ChannelImplement['send'];
}

function isChannelConnected(channel: ChannelImplementMaybe): channel is ChannelImplement {
    return typeof channel.send === 'function';
}

function isSessionMessage(value: any): value is SessionMessage {
    return !!value?.sessionId;
}

export abstract class Channel<S extends Session> {
    protected readonly implement: ChannelImplement;

    protected readonly sessions = new Map<string, S>();

    private readonly tasks = new TaskManager<string>();

    constructor(implement: ChannelImplementMaybe) {
        if (!isChannelConnected(implement)) {
            throw new Error('Cannot send data over channel');
        }

        this.implement = implement;
    }

    start() {
        this.implement.on(
            'message',
            async (message: unknown) => {
                if (!isSessionMessage(message)) {
                    return;
                }

                const {data: {action, payload}, sessionId, execution} = message;

                if (typeof action !== 'string') {
                    return;
                }
                // 对于接收方，接收第一条消息时是不存在对应的会话的，所以需要实时创建出来
                const session = this.sessions.get(sessionId) ?? this.createSession({association: sessionId});
                this.sessions.set(sessionId, session);
                await session.handleMessage(action, payload, execution);

                if (action === ACTION_SESSION_FINISH) {
                    this.sessions.delete(sessionId);
                    this.tasks.resolve(sessionId);
                }
            }
        );
    }

    async startSession(association: Session | string, data: any, options?: SessionOptions): Promise<void> {
        const session = this.createSession({association, options});
        this.sessions.set(session.sessionId, session);
        try {
            session.start(data);
            // 延迟1秒后resolve，防止session未完全初始化完毕
            setTimeout(() => this.tasks.resolve(session.sessionId), 1000);
            return this.tasks.start(session.sessionId);
        }
        catch (error) {
            this.sessions.delete(session.sessionId);
            this.tasks.remove(session.sessionId);
            throw error;
        }
    }

    send(sessionId: string, message: any, execution?: Execution) {
        return this.sessions.get(sessionId)?.send(message, execution);
    }

    log(source: string, action: string, detail: Record<string, any> = {}) {
        const message = {
            sessionId: null,
            data: {
                action: ACTION_LOG,
                payload: {level: 'system', source, action, detail},
            },
        };
        this.implement.send(message);
    }

    protected abstract createSession(init: SessionInit): S;
}
