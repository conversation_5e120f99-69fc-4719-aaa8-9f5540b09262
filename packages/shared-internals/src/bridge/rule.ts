
export const DEFAULT_WORKSPACE_CONFIG_FOLDER = '.comate';

export const DEFAULT_RULE_CONFIG_FOLDER = 'rules';

export const DEFAULT_WORKSPACE_RULE_FILE = 'default.mdr';

export interface RuleMetadata {
    description?: string;
    globs?: string;
    alwaysApply?: boolean;
}

export interface RuleItem {
    name: string;
    /** 相对路径 */
    path: string;
    metadata: RuleMetadata;
    firstLine: string;
}

export interface RulePanelActionParamsMap {
    getRuleList: Record<string, never>;
    createRule: {name: string, metadata?: RuleMetadata};
    deleteRule: {path: string};
    updateRule: {path: string, name: string, metadata: RuleMetadata};
    openRuleFile: {path: string};
    isRuleNameExist: {name: string};
    checkFilePatternMatch: {globs: string};
}

export type RulePanelAction = keyof RulePanelActionParamsMap;

export function extractMentionFilesFromRuleConfig(content: string) {
    const regex = /[@#]([\w/.\-\u4e00-\u9fa5]+)/g;
    const matches = [...content.matchAll(regex)];
    const validMatches = matches.filter(match => {
        const index = match.index ?? 0;
        const charBefore = content[index - 1] ?? '';
        const chartAfter = content[index + match[0].length] ?? '';
        return charBefore.trim() === '' && chartAfter.trim() === '';
    });
    const mentions = validMatches.map(match => match[1].trim());
    const files = [...new Set(mentions)];
    return {
        files,
        matches: validMatches,
    };
}
