import {ActivationContext, SystemInfoParsed} from '../schema.js';

interface BugDetail {
    rule_name: string;
    line_number: number;
    description: string;
    type: string;
    bug_es_id: string;
}

interface CodestyleBugs {
    [filePath: string]: BugDetail[]; // 文件路径作为键，值为 BugDetail 数组
}

interface SubIscanResult {
    bugs: CodestyleBugs;
    high: number;
    middle: number;
    low: number;
    report: string;
    bug_num: number;
    message: string;
    file_num: number;
    status: number;
}
export interface IScanResult {
    status: string;
    bugbye: SubIscanResult | null;
    security: SubIscanResult | null;
    codestyle: SubIscanResult | null;
    searchIscanErrorMsg: string;
}

// 向engine发起代码扫描并获取安全扫描结果
export const ACTION_START_ISCAN_AND_GET_SEC_RESULT = 'START_ISCAN_AND_GET_SEC_RESULT';

// 向plugin发起代码扫描
export const ACTION_START_ISCAN = 'START_ISCAN';

// 获取代码扫描结果
export const ACTION_ISCAN_RESULT = 'ISCAN_RESULT';

// 获取代码扫描buildId
export const ACTION_ISCAN_JOB_BUILD_ID = 'ISCAN_JOB_BUILD_ID';

// 获取代码扫描结果
export const ACTION_SCAN_NOTIFICATION = 'SCAN_NOTIFICATION';

export type IcScanTypes = 'init' | 'getResult';

export interface IScanInvokePayload {
    pluginName: string;
    capabilityName: string;
    scanType: IcScanTypes;
    systemInfo: SystemInfoParsed;
    jobBuildId?: string;
    context?: ActivationContext;
}

export interface IScanResultPayload {
    chunk: IScanResult;
    capabilityName?: string;
}

export interface IScanBuildResult {
    jobBuildId: string;
    errorMsg: string;
    status: 'SUCCESS' | 'ERROR';
}

export interface IScanJobBuildPayload {
    chunk: IScanBuildResult;
    capabilityName?: string;
}
