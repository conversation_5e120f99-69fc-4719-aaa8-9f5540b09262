{"name": "@comate/plugin-shared-internals", "version": "0.9.2", "type": "module", "engines": {"node": ">=20.10.0"}, "main": "dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js"}, "./schema": {"import": "./dist/schema.js", "require": "./dist/schema.js"}, "./logger": {"import": "./dist/common/logger.js", "require": "./dist/common/logger.js"}}, "files": ["dist"], "contributors": ["zhanglili01 <<EMAIL>>"], "license": "MIT", "publishConfig": {"access": "public"}, "scripts": {"clean": "rm -rf dist", "test": "vitest", "build": "tsc", "lint": "eslint --max-warnings=0 src --fix", "bundle": "tsc && rollup -c rollup.config.js"}, "devDependencies": {"@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-json": "^6.1.0", "@types/json-schema": "^7.0.15", "@types/node": "18.15.0", "@types/shell-quote": "^1.7.5", "@types/vscode": "1.69.0", "eslint": "^8.54.0", "typescript": "^5.3.2", "vscode-languageserver-types": "^3.17.5"}, "dependencies": {"@types/fs-extra": "^11.0.4", "@types/lodash": "^4.14.202", "@types/ospath": "^1.2.3", "anymatch": "^3.1.3", "axios": "^1.5.0", "compare-versions": "^6.1.1", "dayjs": "^1.11.13", "fs-extra": "^11.2.0", "globby": "13.2.2", "lodash": "^4.17.21", "marked": "^11.2.0", "ospath": "^1.2.2", "shell-quote": "^1.8.2", "string-similarity-js": "^2.1.4", "vite": "^5.1.6", "vitest": "^1.2.2", "vscode-languageserver": "8.0.2", "vscode-uri": "^3.0.8"}}