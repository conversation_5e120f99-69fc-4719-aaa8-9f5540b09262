{"private": true, "name": "@comate/kernel", "version": "0.9.2", "author": "wuweiqi <<EMAIL>>", "type": "module", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js"}}, "files": ["bundle", "dist"], "publishConfig": {"registry": "http://registry.npm.baidu-int.com"}, "repository": {"type": "git", "url": "ssh://<EMAIL>:8235/baidu/ide/comate-plugin-host"}, "scripts": {"build": "rm -rf dist && tsc -p ./ && rollup -c rollup.config.js", "watch": "tsc -p ./ --watch", "bundle": "rm -rf bundle && pnpm run build && cp package.json bundle"}, "dependencies": {"@baidu/weirwood-comate-sdk": "1.0.7-beta.4", "@comate/kernel-shared": "workspace:^", "@comate/plugin-shared-internals": "workspace:^", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-terser": "^0.4.4", "ajv": "^8.17.1", "axios": "^1.7.7", "compare-versions": "^6.1.1", "dayjs": "^1.11.13", "fs-extra": "^11.2.0", "globby": "13.2.2", "inversify": "^6.0.1", "isomorphic-git": "^1.27.1", "lodash": "^4.17.21", "p-memoize": "^7.1.1", "reflect-metadata": "^0.1.13", "vscode-languageserver": "8.0.2", "vscode-languageserver-textdocument": "^1.0.11", "vscode-uri": "^3.0.8"}, "devDependencies": {"@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-json": "^6.1.0", "@types/fs-extra": "^11.0.4", "@types/lodash": "^4.14.202", "@types/vscode": "1.69.0", "pkg": "^5.8.1", "vscode-languageserver-types": "3.17.5"}}