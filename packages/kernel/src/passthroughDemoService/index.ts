import {injectable} from 'inversify';
import {Disposable} from 'vscode-languageserver';
import {
    kernel,
    PT_WEBVIEW_DEMO_REQUEST,
    // PT_KERNEL_DEMO_REQUEST,
    PT_WEBVIEW_STREAM_DEMO_REQUEST,
} from '@comate/kernel-shared';

@injectable()
export class PassthroughDemoService implements Disposable {
    private readonly disposables: (() => void)[] = [];
    private intervalId: NodeJS.Timer | null = null;
    constructor() {
        this.disposables.push(
            kernel.connect.onWebviewMessage(PT_WEBVIEW_DEMO_REQUEST, this.handleMessage),
            kernel.connect.onWebviewStreamMessage(PT_WEBVIEW_STREAM_DEMO_REQUEST, this.handleStreamMessage)
        );
    }

    start() {
        // this.intervalId = setInterval(async () => {
        //     kernel.connect.sendWebviewMessage(PT_KERNEL_DEMO_REQUEST, {
        //         time: new Date(),
        //     });
        // }, 10000);
    }

    handleMessage = (data: string) => {
        return '收到来自 webview 请求：' + data;
    };

    handleStreamMessage = async function* (data: string) {
        for (let i = 1; i <= 5; i++) {
            await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
            yield i;
        }
    };

    dispose(): void {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }

        for (const dispose of this.disposables) {
            dispose();
        }
        this.disposables.length = 0;
    }
}
