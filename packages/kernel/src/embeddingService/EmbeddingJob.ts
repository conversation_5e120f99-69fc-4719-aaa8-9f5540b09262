/* eslint-disable max-statements */
/* eslint-disable complexity */
import {inject, injectable} from 'inversify';
import {join} from 'node:path';
import EventEmitter from 'node:events';
import axios, {CancelTokenSource} from 'axios';
import {CancellationTokenSource} from 'vscode-languageserver';
import {kernel} from '@comate/kernel-shared';
import {globbyStream} from 'globby';
import {
    ApiCodeSearchService,
    FileMeta,
    IndexerConfig,
} from '../api/codeSearch.js';
import {isFileExist} from '../utils/fs.js';
import {sleep} from '../utils/common.js';
import {EmbeddingsIndexProgressStore} from './InprogressStore.js';
import {EmbeddingsWorkspaceMetadata, createFileIndexPatchAction, getMetadata} from './EmbedingUtil.js';

export interface Description {
    createdTime: number;
}

export abstract class Job<T extends Description> {
    abstract get description(): T;
    abstract get progress(): number;
    abstract get running(): boolean;
    abstract execute(): void;
    abstract cancel(): void;
    abstract addListener(listener: (ev: T) => void): this;
}

enum JobStatus {
    READY,
    RUNNING,
    CANCELLED,
    DONE,
}
export interface EmbeddingJobDescription extends Description {
    repoId: string;
    workspacePath: string;
    running: boolean;
    progress: number;
    scene: 'common' | 'firstBuild' | 'reBuild';
    version: 'v1' | 'v2';
}

export class EmbeddingFileExceedError extends Error {
    threshold: number;
    constructor(message: string, threshold: number) {
        super(message);
        this.threshold = threshold;
    }
}

@injectable()
export class EmbeddingJob implements Job<EmbeddingJobDescription> {
    private readonly createdTime: number = Date.now();
    private readonly repoId: string;
    private readonly workspacePath: string;
    private readonly cancelToken: CancellationTokenSource;
    private readonly axiosCancelTokenSource: CancelTokenSource;
    private readonly eventEmitter = new EventEmitter();
    private internalProgress: number = 0;
    private status: JobStatus = JobStatus.READY;

    private readonly opts: Pick<EmbeddingJobDescription, 'scene' | 'version'>;
    constructor(
        // @inject(ConfigurationService) protected readonly configProvider: ConfigurationService,
        @inject(ApiCodeSearchService) protected readonly api: ApiCodeSearchService,
        @inject(EmbeddingsIndexProgressStore) protected readonly progressStore: EmbeddingsIndexProgressStore,
        workspacePath: string,
        repoId: string,
        opts: Pick<EmbeddingJobDescription, 'scene' | 'version'>
    ) {
        this.workspacePath = workspacePath;
        this.repoId = repoId;
        this.opts = opts;
        this.axiosCancelTokenSource = axios.CancelToken.source();
        this.cancelToken = new CancellationTokenSource();
    }

    // static async getExistJob(repoId: string) {
    //     const store = globalContext.get(GlobalStorage);
    //     const jobs = store.get<Record<string, EmbeddingJobDescription>>('embeddingJobs', {});
    //     return jobs?.[repoId];
    // }

    async setEmbeddingJobDescriptionToGlobal() {
        // const store = globalContext.get(GlobalStorage);
        // const jobs = store.get<Record<string, EmbeddingJobDescription>>('embeddingJobs', {});
        // store.update('embeddingJobs', {...jobs, [this.repoId]: this.description});
    }

    get running(): boolean {
        return this.status === JobStatus.RUNNING;
    }

    get progress(): number {
        return Math.min(this.internalProgress, 100);
    }

    get description(): EmbeddingJobDescription {
        return {
            createdTime: this.createdTime,
            progress: this.progress,
            workspacePath: this.workspacePath,
            running: this.running,
            repoId: this.repoId,
            scene: this.opts.scene,
            version: this.opts.version,
        };
    }

    /**
     * 每个Job有两个定时器
     * 一个定时器是任务开始执行后，定时向外广播progress
     * 一个定时器是任务执行过程中，轮训服务端进度，往job里写progress
     */
    private readonly intervals: NodeJS.Timer[] = [];

    get emitProgressInterval() {
        return this.intervals[0];
    }

    set emitProgressInterval(interval: NodeJS.Timer) {
        this.intervals[0] = interval;
    }

    addListener(listener: (ev: EmbeddingJobDescription) => void) {
        this.eventEmitter.addListener('progress', listener);
        return this;
    }

    dispose() {
        // Clear all intervals
        this.intervals.forEach(interval => {
            if (interval) {
                clearInterval(interval);
            }
        });
        this.intervals.length = 0;

        // Remove all event listeners
        this.eventEmitter.removeAllListeners();

        // Cancel any pending requests
        this.axiosCancelTokenSource.cancel();
        this.cancelToken.cancel();
    }

    private async buildIndex(files: string[], metadata: EmbeddingsWorkspaceMetadata, config: IndexerConfig) {
        const user = kernel.config.username!;
        const repoInfo = {
            user,
            // eslint-disable-next-line camelcase
            repo_id: this.repoId,
            repo: metadata.project!,
            branch: metadata.branch,
            device: metadata.device!,
            cwd: this.description.workspacePath,
            cancelToken: this.axiosCancelTokenSource,
            version: this.description.version,
            scene: this.description.scene,
            limit: config.config.concurrent_file_count || 50,
        };
        await this.api.postRemoteIndexMetadata(repoInfo, this.description.version);
        const indexInfo = await this.api.getRemoteIndexInfo(this.repoId, this.description.version);
        const previousIndexData = indexInfo.data ?? [];
        const indexMap = new Map(previousIndexData.map(item => [item.filepath, item.sha1]));
        // 启动后就设置上传时间，IDE重启后不支持续传，可通过系统命令，重建索引
        // 若后续要支持续传，增加description里的`uploadedFiles/files`两个字段，启动时可读取上一次的description队列进行续传
        this.progressStore.updateUploadTime(this.description.repoId);

        const fileInfos = [];
        for await (const relativePath of files) {
            if (this.cancelToken.token.isCancellationRequested) {
                return;
            }
            const oldFileHash = indexMap.get(relativePath);
            const absolutePath = join(this.description.workspacePath, relativePath);
            const action = await createFileIndexPatchAction(absolutePath, oldFileHash, config.config.max_file_size_kb);
            if (action) {
                fileInfos.push({path: relativePath, sha1: action.sha1, action: action.action});
            }
        }

        const batchUpdate = this.api.apiPostFileForIndex({...repoInfo, files: fileInfos});
        for await (const progress of batchUpdate) {
            // @进度计算规则1：前50%由上传文件决定，已上传数/总文件数
            this.internalProgress = 50 * (progress.uploaded / fileInfos.length);
        }

        const removedFiles: FileMeta[] = [];
        for await (const {filepath, sha1} of previousIndexData) {
            const existed = await isFileExist(join(this.description.workspacePath, filepath));
            if (!existed) {
                removedFiles.push({sha1, path: filepath, action: 'delete'});
            }
        }

        // @进度计算规则3：删除部分不考虑进度, 但是finish需要等待删除处理完
        this.api.apiPostDeletedFileAndMarkedJobAsFinish({...repoInfo, files: removedFiles});

        while (
            !this.cancelToken.token.isCancellationRequested
            && fileInfos.length > 0
            && this.internalProgress < 95
        ) {
            const {progress} = await this.api.getRemoteIndexInfo(this.repoId, this.description.version);
            this.internalProgress = 50 + progress * 0.5;
            this.setEmbeddingJobDescriptionToGlobal();
            await sleep(1000);
        }
        this.internalProgress = 100;
        kernel.logger.info('embedding(info): job finished, repoId=', this.repoId);
        this.status = JobStatus.DONE;
        // 保证定时器把最后一帧消息发出去
        await sleep(1000);
        clearInterval(this.emitProgressInterval);
        this.setEmbeddingJobDescriptionToGlobal();
        this.dispose();
    }

    async execute() {
        try {
            // 防止重复执行，在 JobManager.createJob 里有可能会返回 existJob
            if (this.status !== JobStatus.READY) {
                return;
            }

            this.status = JobStatus.RUNNING;
            this.emitProgressInterval = setInterval(
                () => {
                    this.setEmbeddingJobDescriptionToGlobal();
                    this.eventEmitter.emit('progress', this.description);
                    if (this.description.progress === 100) {
                        // 保证只发送一次100的进度事件，防止界面上从100重置到0后又变成100
                        clearInterval(this.emitProgressInterval);
                    }
                },
                1000
            );

            kernel.logger.info('EmbeddingJob execute', this.description);

            const config = await this.api.getIndexerConfig();
            const metadata = await getMetadata(this.description.workspacePath);
            if (!metadata.project || !metadata.device) {
                throw new Error('Unable to update: missing project or device');
            }

            if (this.cancelToken.token.isCancellationRequested) {
                this.status = JobStatus.CANCELLED;
                return;
            }

            const stream = await globbyStream(config.white_pattern, {
                gitignore: true,
                cwd: this.description.workspacePath,
                ignore: config.black_pattern,
            });

            const files: string[] = [];
            for await (const file of stream) {
                if (this.cancelToken.token.isCancellationRequested) {
                    return;
                }

                files.push(file.toString());
            }
            kernel.logger.info(`embeddings(info): finish index ${files.length} files`);
            const maxFiles = config.config.max_file_count;
            if (files.length > maxFiles) {
                throw new EmbeddingFileExceedError(
                    `embeddings(error): exceed max file count, limit is ${maxFiles}`,
                    maxFiles
                );
            }

            kernel.logger.info('embedding(info): start buildIndex, files count:', files.length);
            void this.buildIndex(files, metadata, config).catch(e => {
                kernel.logger.info('Error while building index: ', e.message || '');
                this.status = JobStatus.CANCELLED;
            });
        }
        catch (ex) {
            kernel.logger.error(`embeddings(error): failed to index files, error=${(ex as Error).message}`);
            this.cancel();
            throw ex;
        }
    }

    cancel(): void {
        clearInterval(this.emitProgressInterval);
        this.internalProgress = 100;
        this.eventEmitter.emit('progress', this.description);
        this.status = JobStatus.CANCELLED;
        this.cancelToken.cancel();
        this.axiosCancelTokenSource.cancel();
        this.setEmbeddingJobDescriptionToGlobal();
        this.dispose();
    }
}

/** 首次构建的任务，上线后默认是v2版本 */
@injectable()
export class FirstBuildEmbeddingJob extends EmbeddingJob {
    constructor(
        @inject(ApiCodeSearchService) protected readonly api: ApiCodeSearchService,
        @inject(EmbeddingsIndexProgressStore) protected readonly progressStore: EmbeddingsIndexProgressStore,
        rootPath: string,
        repoId: string
    ) {
        super(api, progressStore, rootPath, repoId, {scene: 'firstBuild', version: 'v2'});
    }
}

/** 24h定时任务 */
@injectable()
export class Internval24hEmbeddingJob extends EmbeddingJob {
    constructor(
        @inject(ApiCodeSearchService) protected readonly api: ApiCodeSearchService,
        @inject(EmbeddingsIndexProgressStore) protected readonly progressStore: EmbeddingsIndexProgressStore,
        rootPath: string,
        repoId: string,
        version: 'v1' | 'v2'
    ) {
        super(api, progressStore, rootPath, repoId, {scene: 'common', version});
    }
}

/** 手动触发的重建任务，默认是v2版本 */
@injectable()
export class RebuildEmbeddingJob extends EmbeddingJob {
    constructor(
        @inject(ApiCodeSearchService) protected readonly api: ApiCodeSearchService,
        @inject(EmbeddingsIndexProgressStore) protected readonly progressStore: EmbeddingsIndexProgressStore,
        rootPath: string,
        repoId: string
    ) {
        super(api, progressStore, rootPath, repoId, {scene: 'reBuild', version: 'v2'});
    }

    // @TODO 暂无用到地方，后续有需求再补充
    // static async getRebuildJobProgress(repoId: string) {
    //     const jobs = this.store.get<Record<string, EmbeddingJobDescription>>('embeddingJobs', {});
    //     if (jobs?.[repoId].running) {
    //         return jobs[repoId].progress;
    //     }

    //     return 100;
    // }
}
