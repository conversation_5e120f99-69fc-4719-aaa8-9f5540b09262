import {join} from 'node:path';
import {injectable} from 'inversify';
import {kernel} from '@comate/kernel-shared';
import {AxiosInstance, AxiosRequestConfig, CancelTokenSource} from 'axios';
import * as _ from 'lodash';
import {PLATFORM} from '@comate/plugin-shared-internals';
import {readFileSync} from 'node:fs';

const EB_BASE_URL_MAPPING: Record<PLATFORM, string> = {
    internal: 'https://cs.baidu-int.com',
    saas: 'https://comate.baidu.com',
    poc: 'https://comate.baidu.com',
};

const CS_MAPPING: Record<PLATFORM, string> = {
    internal: 'nlcodesearch',
    saas: 'retrieval',
    poc: 'retrieval',
};

const ES_MAPPING: Record<PLATFORM, string> = {
    internal: 'embeddingindex',
    saas: 'index',
    poc: 'index',
};

export type Version = 'v1' | 'v2';

export interface FileMeta {
    sha1: string;
    path: string;
    action: PatchFileIndex['action'];
}

function getVersionPrefix(version?: Version): string {
    return version === 'v2' ? '/v2' : '';
}

export interface IndexerConfig {
    black_pattern: string[];
    white_pattern: string[];
    config: {
        concurrent_file_count?: number;
        max_file_size_kb: number;
        max_file_count: number;
        index_interval_minute: number; // 索引更新最小间隔
        file_interval_milliseconds: number; // 文件更新最小间隔
    };
}
interface IndexerConfigResponse {
    data: IndexerConfig;
    status: 'ok';
    message: string;
}

interface RemoteIndexInfoResponse {
    data: FileIndex[] | null;
    success: boolean;
    error: null;
    progress: number;
}

export interface FileIndex {
    repo_id: string;
    filepath: string;
    sha1: string;
    status: 'pending' | 'running' | 'finished' | 'failed';
}

interface CreateFileIndex extends Omit<FileIndex, 'status'> {
    action: 'create';
    content: string;
}

interface UpdateFileIndex extends Omit<CreateFileIndex, 'action'> {
    action: 'update';
}

interface DeleteFileIndex extends Omit<FileIndex, 'sha1' | 'status'> {
    action: 'delete';
}

interface RecreateFileIndex extends Omit<FileIndex, 'sha1' | 'status'> {
    action: 'recreate';
}

export type PatchFileIndex = CreateFileIndex | UpdateFileIndex | DeleteFileIndex | RecreateFileIndex;

export interface IndexVersionResponse {
    success: boolean;
    error: string;
    version: 'v1' | 'v2';
}

export interface RemoteIndexMetadata {
    repo_id: string;
    repo: string;
    branch?: string;
    device: string;
    user: string;
    action?: 'finish';
}

function removeSuffix(str: string, suffix: string) {
    if (str.endsWith(suffix)) {
        return str.slice(0, -suffix.length);
    }
    return str;
}

@injectable()
export class ApiCodeSearchService {
    private readonly autoworkPrefix: string;
    private readonly cs: string;
    private readonly es: string;
    private readonly axiosInstance: AxiosInstance;
    private readonly axiosConfig: AxiosRequestConfig;

    constructor() {
        this.axiosInstance = kernel.http.axiosInstance; // this.apiBase.axiosInstance;
        const platform = kernel.env.platform;
        const isSaaS = kernel.env.isSaaS;

        this.cs = CS_MAPPING[platform];
        this.es = ES_MAPPING[platform];

        this.autoworkPrefix = isSaaS ? '/api/autowork' : '';

        this.axiosConfig = {
            baseURL: this.getBaseUrl(),
            headers: {
                'X-Client-Type': kernel.env.ideName,
                'X-Client-Version': kernel.env.ideVersion,
            },
        };
        // TODO 别忘了监听config更新
        if (kernel.config.username) {
            this.axiosConfig.headers!['X-Comate-User'] = encodeURIComponent(kernel.config.username);
        }
        if (kernel.config.license) {
            this.axiosConfig.headers!['X-Comate-License'] = kernel.config.license;
        }
    }

    private getBaseUrl() {
        const {platform, httpHost} = kernel.env;
        const isSaaS = kernel.env.isSaaS;

        // // customizedUrl 的优先级比 serverEndpointPrefix 高，即使设置了serverEndpointPrefix，customizedUrl也可以覆盖
        if (isSaaS && httpHost) {
            // 为了兼容路由规则不一致的问题，autowork的私有化baseurl不需要/api后缀
            return removeSuffix(httpHost, '/api') || EB_BASE_URL_MAPPING[platform];
        }
        // else if (isSaaS && customized) {
        //     return customizedUrl || EB_BASE_URL_MAPPING[platform];
        // }
        // else {
        return EB_BASE_URL_MAPPING[platform];
        // }
    }

    async checkFeatureAccess(username: string, feature: 'comateCodeGraph') {
        if (!kernel.env.isInternal) {
            return false;
        }

        const res = await this.axiosInstance.get<{status: boolean}>(
            `${this.autoworkPrefix}/${this.cs}/hasAccess?user=${username}&type=${feature}`,
            this.axiosConfig
        );
        return !!res.data?.status;
    }

    public async getIndexerConfig() {
        const res = await this.axiosInstance.get<IndexerConfigResponse>(
            `${this.autoworkPrefix}/${this.es}/v2/remote-index-config`,
            this.axiosConfig
        );
        if (res.data.status === 'ok') {
            return res.data.data;
        }
        kernel.logger.error('Failed to GET /remote-index-config', JSON.stringify(res.data));
        throw new Error(res.data.message);
    }

    public async getRemoteIndexInfo(repoId: string, version?: Version) {
        const versionPrefix = getVersionPrefix(version);
        const res = await this.axiosInstance.get<RemoteIndexInfoResponse>(
            `${this.autoworkPrefix}/${this.es}${versionPrefix}/remote-index-info?repo_id=${repoId}`,
            this.axiosConfig
        );
        if (res.data.success) {
            return {
                data: res.data.data,
                progress: res.data.progress,
            };
        }
        kernel.logger.error(`Failed to GET /remote-index-info?repo_id=${repoId}`, JSON.stringify(res.data));
        throw new Error(`Failed to GET /remote-index-info?repo_id=${repoId}`);
    }

    /**
     * 获取索引构建服务版本
     */
    public async getRemoteIndexVersion(repoId: string) {
        const res = await this.axiosInstance.get<IndexVersionResponse>(
            `${this.autoworkPrefix}/${this.es}/remote-server-version?repo_id=` + repoId,
            this.axiosConfig
        );
        return res.data.version;
    }

    public async postRemoteIndexMetadata(data: RemoteIndexMetadata, version?: Version) {
        const versionPrefix = getVersionPrefix(version);
        const res = await this.axiosInstance.post(
            `${this.autoworkPrefix}/${this.es}${versionPrefix}/remote-index-repo-meta`,
            data,
            this.axiosConfig
        );
        return res;
    }

    public async patchRemoteIndexFile(data: PatchFileIndex, version?: Version) {
        const versionPrefix = getVersionPrefix(version);
        const res = await this.axiosInstance.post(
            `${this.autoworkPrefix}/${this.es}${versionPrefix}/remote-index-file`,
            data,
            this.axiosConfig
        );
        return res;
    }

    public async *apiPostFileForIndex({repo_id, device, repo, branch, files, limit, cwd, version, scene, cancelToken}: {
        repo_id: string;
        device: string;
        branch?: string;
        repo?: string;
        limit: number;
        cwd: string;
        version: 'v1' | 'v2';
        scene: 'firstBuild' | 'reBuild' | 'common';
        cancelToken: CancelTokenSource;
        files: FileMeta[];
    }) {
        const chunks = _.chunk(files, limit);
        let finished = 0;
        try {
            for await (const chunk of chunks) {
                kernel.logger.info('embedding(info): start upload', JSON.stringify(chunk));
                const files = chunk.map(file => ({
                    path: file.path,
                    sha1: file.sha1,
                    action: file.action,
                    content: file.action === 'delete' ? '' : readFileSync(join(cwd, file.path), 'utf-8'),
                }));
                const meta = {device, repo_id, repo, branch, scene};
                try {
                    const versionPrefix = version === 'v1' ? '' : '/v2';
                    await this.axiosInstance.post(
                        `${this.autoworkPrefix}/${this.es}${versionPrefix}/batch-remote-index-file/json`,
                        {...meta, files},
                        {...this.axiosConfig, timeout: 5 * 1000, cancelToken: cancelToken.token}
                    );
                }
                catch (ex) {
                    kernel.logger.error(
                        `embeddings(error): apiPostFileForIndex upload chunk message: ${(ex as Error).message}`
                    );
                }
                finally {
                    finished = finished + chunk.length;
                    kernel.logger.info(`embedding(info): uploaded ${finished} files, total ${files.length} files`);
                    yield {uploaded: chunks.indexOf(chunk) * limit + chunk.length};
                }
            }
        }
        catch (ex) {
            kernel.logger.error(`embeddings(error): apiPostFileForIndex message: ${(ex as Error).message}`);
        }
    }

    public async apiPostDeletedFileAndMarkedJobAsFinish(
        params: Parameters<typeof this.postRemoteIndexMetadata>[0] & Parameters<typeof this.apiPostFileForIndex>[0]
    ) {
        const repoInfo = {
            // eslint-disable-next-line camelcase
            repo_id: params.repo_id,
            repo: params.repo,
            branch: params.branch,
            device: params.device,
        };
        kernel.logger.info(`embedding(debug): start delete ${params.files.length} files, repoId=${repoInfo.repo_id}`);
        const deleteRemoteIndexFileTask = this.apiPostFileForIndex({
            ...repoInfo,
            cwd: params.cwd,
            files: params.files,
            version: params.version,
            cancelToken: params.cancelToken,
            scene: params.scene,
            limit: params.limit,
        });
        // eslint-disable-next-line no-constant-condition
        while (true) {
            const {done} = await deleteRemoteIndexFileTask.next();
            if (done) {
                break;
            }
        }
        kernel.logger.info(
            `embedding(debug): finish delete ${params.files.length} files, files=${params.files.length}`
        );
        this.postRemoteIndexMetadata({
            user: params.user,
            ...repoInfo,
            action: 'finish',
        }, params.version);
    }
}
