import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import terser from '@rollup/plugin-terser';
import json from '@rollup/plugin-json';
import alias from '@rollup/plugin-alias';
import path from 'path';
import {fileURLToPath} from 'url';
import {dts} from 'rollup-plugin-dts';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const config = [{
    context: 'this',
    input: 'dist/index.js', // 这里替换为你程序的入口文件
    output: {
        file: 'bundle/index.js',
        format: 'es',
        sourcemap: process.env.PLATFORM === 'internal',
    },
    plugins: [
        alias({
            entries: [
                { find: '@kernel', replacement: path.resolve(__dirname, 'dist/foundation') }
            ]
        }),
        commonjs({
            dynamicRequireTargets: [
                'node_modules/inversify/es/syntax/binding_{on,when}_syntax.js',
            ],
        }),
        json(),
        resolve({
            preferBuiltins: true, // 告诉 Rollup 使用 Node 的内置模块
        }),
        // terser(),
    ],
}, {
    input: 'dist/index.d.ts',
    output: {
        file: 'bundle/index.d.ts',
        format: 'es'
    },
    plugins: [dts()]
}];

export default config;
