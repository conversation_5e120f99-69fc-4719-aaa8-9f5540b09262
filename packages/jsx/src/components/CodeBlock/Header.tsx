/* eslint-disable max-len */
import {useCallback} from 'react';
import {MESSAGE_OPEN_FILE, OpenFileMessage} from '../../dicts/message.js';
import {useSendMessage} from '../ArticleContextProvider.js';

interface Props {
    filePath?: string;
    startLine?: number;
    endLine?: number;
}

export default function Header({filePath = '', startLine = 0, endLine = 0}: Props) {
    const title = filePath.split('/').pop();
    const sendMessage = useSendMessage();

    const click = useCallback(
        () => {
            const message: OpenFileMessage = {
                type: MESSAGE_OPEN_FILE,
                payload: {to: filePath, line: startLine},
            };
            sendMessage(message);
        },
        [filePath, startLine, sendMessage]
    );

    return (
        <div
            onClick={click}
            className="comate-code-header group hover:cursor-pointer flex justify-between text-xs bg-[var(--comate-editor-background)] items-center px-3 py-1.5 rounded-t-[4px] border-b border-[#888888]/20"
            style={{
                position: 'sticky',
                top: 0,
                backgroundColor: 'rgba(0, 0, 0, 0.4)',
            }}
        >
            <div
                className="group-hover:opacity-80 whitespace-nowrap text-ellipsis overflow-hidden text-[var(--comate-descriptionForeground)]"
            >
                {title ?? null}
            </div>
            <div className="group-hover:opacity-80 min-w-fit">
                <span className="min-w-fit text-[var(--comate-descriptionForeground)]">
                    Lines {startLine + 1}-{endLine + 1}
                </span>
            </div>
        </div>
    );
}
