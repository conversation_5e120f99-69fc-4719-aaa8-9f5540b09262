import {createElement} from 'react';
import {DrawElement, allowedNativeElement, isNativeElement} from '@comate/plugin-shared-internals/schema';
import Markdown from './Markdown.js';
import CodeBlock from './CodeBlock/index.js';
import CodeBlockHeader from './CodeBlock/Header.js';

import FileLink from './FileLink.js';
import Suggestion from './Suggestion.js';
import CommandButton from './CommandButton.js';
import ButtonGroup from './ButtonGroup.js';
import {useArticleContext} from './ArticleContextProvider.js';
import {IdeCodeBlockProps, JsxMarkdownProps} from './Article.js';
import AlertTag from './AlertTag.js';
import Loading from './Loading.js';
import {ExternalLink} from './ExternalLink.js';
import Flex from './Flex.js';
import Card from './Card.js';
import CollapsePanel from './CollapsePanel/index.js';


// const fixedElementAttributes: Record<string, Record<string, string>> = {};

function ensureArray(value: any): any[] {
    return Array.isArray(value) ? value : [value];
}

function ensureString(value: any) {
    return Array.isArray(value) ? value.join('') : value;
}

function renderContextElement(
    IdeCodeBlock: React.ComponentType<IdeCodeBlockProps> | undefined,
    IdeMarkdown: React.ComponentType<JsxMarkdownProps> | undefined,
    actions?: Record<string, () => void>,
    role?: 'user' | 'system' | 'assistant' | undefined
) {
    /* eslint-disable complexity, @typescript-eslint/no-use-before-define */
    return function renderElement(element: string | number | DrawElement, index: number) {
        if (typeof element === 'string' || typeof element === 'number') {
            return element;
        }

        if (isNativeElement(element)) {
            const props: Record<string, any> = {};
            const allowedAttributeNames = allowedNativeElement[element.type];
            for (const key of allowedAttributeNames) {
                const value = element[key];

                if (value !== undefined) {
                    props[key] = key === 'children'
                        ? <Display elements={ensureArray(value)} actions={actions} role={role} />
                        : value;
                }
            }
            // Object.assign(props, fixedElementAttributes[element.type] || {});
            if (element.type === 'a') {
                return createElement(ExternalLink, {
                    ...props,
                    href: props.href,
                });
            }
            return createElement(element.type, props);
        }

        const {type, children, ...rest} = element;
        switch (type) {
            case 'markdown':
                return (IdeMarkdown
                    ? <IdeMarkdown key={index} actions={actions} role={role} content={ensureString(children)} />
                    : <Markdown key={index}>{ensureString(children)}</Markdown>);
            case 'alert-tag':
                return (
                    <AlertTag key={index} level={element.level}>
                        {children && ensureString(children)}
                    </AlertTag>
                );
            case 'code-block':
                return (
                    IdeCodeBlock
                        ? (
                            <IdeCodeBlock
                                hidden={element.hidden}
                                key={index}
                                inline={false}
                                className={`language-${element.language}`}
                                language={element.language}
                                actions={actions}
                                showActions={role === 'assistant'}
                                visibleLines={element.visibleLines}
                                showHeader
                                header={
                                    element.readonly
                                        ? (
                                            <CodeBlockHeader
                                                filePath={element.readonlyFileData?.filePath}
                                                startLine={element.readonlyFileData?.startLine}
                                                endLine={element.readonlyFileData?.endLine}
                                            />
                                        )
                                        : undefined
                                }
                                jsxControl={{
                                    actions: element.actions,
                                    replaceToFileData: element.replaceToFileData,
                                    insertToFileData: element.insertToFileData,
                                    smartApplyData: element.smartApplyData,
                                }}
                            >
                                {ensureString(children)}
                            </IdeCodeBlock>
                        )
                        : (
                            <CodeBlock.Content
                                key={index}
                                language={element.language}
                                code={ensureString(element.children)}
                                acceptMethods={element.acceptMethods}
                                closed={element.closed}
                            />
                        )
                );
            case 'file-link':
                return (
                    <FileLink key={index} to={element.to} line={element.line} showIcon={element.showIcon}>
                        {children && ensureString(children)}
                    </FileLink>
                );
            case 'suggestion':
                return (
                    <Suggestion
                        key={index}
                        acceptMethods={element.acceptMethods}
                        title={element.title}
                        content={element.content}
                    >
                        <Display elements={ensureArray(children)} actions={actions} />
                    </Suggestion>
                );
            case 'command-button':
                return (
                    <CommandButton
                        variant={element.variant}
                        commandName={element.commandName}
                        data={element.data}
                        replyText={element.replyText}
                        actions={actions}
                        action={element.action}
                        propagation={element.propagation}
                        tooltipText={element.tooltipText}
                        actionData={element.actionData}
                    >
                        {children}
                    </CommandButton>
                );
            case 'button-group':
                return (
                    <ButtonGroup>
                        {children}
                    </ButtonGroup>
                );
            case 'loading':
                return <Loading content={children} />;
            case 'flex':
                return (
                    <Flex {...rest}>
                        <Display elements={ensureArray(children)} actions={actions} />
                    </Flex>
                );
            case 'card':
                return (
                    <Card color={element.color}>
                        <Display elements={ensureArray(children)} actions={actions} />
                    </Card>
                );
            case 'collapse-panel':
                return (
                    <CollapsePanel
                        {...element}
                        extra={<Display elements={ensureArray(element.extra)} actions={actions} />}
                    >
                        <Display elements={ensureArray(children)} actions={actions} />
                    </CollapsePanel>
                );
            default:
                // eslint-disable-next-line no-console
                console.log('not supported element', element);
                return null;
        }
    };
    /* eslint-enable complexity, @typescript-eslint/no-use-before-define */
}

interface Props {
    elements: Array<string | number | DrawElement>;
    actions?: Record<string, () => void>;
    role?: 'user' | 'system' | 'assistant';
}

export default function Display({elements, actions, role}: Props) {
    const {codeBlock, markdown} = useArticleContext();
    // eslint-disable-next-line no-console
    // console.log('Display Props', actions, role, elements);
    return (
        <>
            {elements.map(renderContextElement(codeBlock, markdown, actions, role))}
        </>
    );
}
