import {useRenderConfig} from './ConfigProvider.js';
import LoadingIcon from './icons/LoadingIcon.js';
import WarningIcon from './icons/WarningIcon.js';

interface Props {
    content?: string;
}

export default function Loading({content}: Props) {
    const {status} = useRenderConfig();

    if (status === 'canceled') {
        return (
            <div
                className={'text-red-500 mr-1 flex-shrink-0 comate-loading-canceled'}
            >
                <WarningIcon />
                已停止生成
            </div>
        );
    }

    return (
        <div className="comate-loading">
            <LoadingIcon />
            {content && <p>{content}</p>}
        </div>
    );
}
