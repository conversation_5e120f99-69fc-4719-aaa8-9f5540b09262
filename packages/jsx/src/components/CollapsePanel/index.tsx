/* bca-disable */
/* eslint-disable max-len */
import {ReactNode, useCallback} from 'preact/compat';
import RightIcon from '../icons/RightIcon.js';
import Loading from '../Loading.js';
import {useDerivedState} from './useDerivedState.js';

interface Props {
    collapsible: boolean;
    title: string;
    children: ReactNode;
    loading?: boolean;
    loadingText?: string;
    extra?: ReactNode;
    onChange?: (collapsible: boolean) => void;
}

export default function CollapsePanel({
    collapsible,
    title,
    children,
    loading,
    loadingText,
    extra,
    onChange,
}: Props) {
    const [isCollapsed, setIsCollapsed] = useDerivedState(collapsible);
    const toggleCollapse = useCallback(
        () => {
            setIsCollapsed(!isCollapsed);
            onChange && onChange(!isCollapsed);
        },
        [isCollapsed, setIsCollapsed, onChange]
    );

    return (
        /* eslint-disable max-len */
        <div className="comate-panel-content flex flex-col gap-1">
            <div className="flex items-center justify-between cursor-pointer gap-1" onClick={toggleCollapse}>
                <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-1 font-bold">
                        {title}
                    </div>
                    <>
                        {extra}
                    </>
                </div>
                <div className="flex items-center gap-2">
                    {isCollapsed && (
                        <div className="flex items-center gap-1">
                            {loading && <Loading />}
                            {loading && loadingText && (
                                <span className="text-[var(--comate-link-color)]">{loadingText}</span>
                            )}
                        </div>
                    )}
                    <div className={`${isCollapsed ? 'rotate-90' : '-rotate-90'}`}>
                        {/* {isCollapsed
                            ? <div className="w-3.5 h-3.5 rotate-90">{<RightIcon />}</div>
                            : <div className="w-3.5 h-3.5 -rotate-90">{<RightIcon />} </div>} */}
                        <RightIcon />
                    </div>
                </div>
            </div>
            {!isCollapsed && <>{children}</>}
        </div>
    );
}
