import {CSSProperties, HTMLAttributes} from 'react';
import {ChunkContent, DrawElement} from '@comate/plugin-shared-internals/schema';
import classNames from 'classnames';
import Display from './Display.js';
import Markdown from './Markdown.js';
import ArticleContextProvider from './ArticleContextProvider.js';

function isElement(value: string | DrawElement): value is DrawElement {
    return typeof value === 'object';
}

function normalizeChunks(content: ChunkContent): string | DrawElement[] {
    const chunks = Array.isArray(content) ? content : [content];

    if (!chunks.length) {
        return [];
    }

    return typeof chunks[0] === 'string' ? chunks.join('') : chunks.filter(isElement);
}

interface Props {
    status?: any;
    style?: CSSProperties;
    className?: string;
    pluginName: string;
    taskId: string;
    interactive: boolean;
    content: ChunkContent;
    actions?: Record<string, () => void>;
    role?: 'system' | 'user' | 'assistant';
}

export default function Article({style, className, pluginName, taskId, interactive, content}: Props) {
    const elements = normalizeChunks(content);

    return (
        <ArticleContextProvider pluginName={pluginName} taskId={taskId} interactive={interactive}>
            <div className={classNames('comate-article', className)} style={style}>
                {typeof elements === 'string'
                    ? <Markdown>{elements}</Markdown>
                    : <Display elements={elements} />}
            </div>
        </ArticleContextProvider>
    );
}

export interface JsxMarkdownProps {
    content: string;
    role?: 'system' | 'user' | 'assistant';
    actions?: Record<string, () => void>;
    visibleLines?: number;
}

// TODO webview 和 jsx循环依赖问题 类型重复声明
type CodeBlockActions =
    | 'replaceToFile'
    | 'insertToFile'
    | 'copy'
    | 'accept'
    | 'diff'
    | 'showFileInsertDiff'
    | 'showFileReplaceDiff'
    | 'newFile'
    | 'insertIntoTerminal'
    | 'viewFile'
    | 'smartApply';


export interface IdeCodeBlockProps {
    inline: boolean;
    children: React.ReactNode;
    hidden?: boolean;
    className?: string;
    language?: string;
    actions?: Record<string, (content?: string, language?: string) => void>;
    visibleLines?: number;
    showHeader?: boolean;
    showActions?: boolean;
    startLine?: number;
    title?: string | React.ReactNode;
    diffLines?: {
        add: number[];
        remove: number[];
    };
    header?: React.ReactNode;
    noMargin?: boolean;
    attributes?: HTMLAttributes<HTMLDivElement>;
    jsxControl?: {
        actions?: CodeBlockActions[];
        replaceToFileData?: {
            filePath: string;
            from: string;
            to: string;
        };
        insertToFileData?: {
            filePath: string;
            position: {line: number, character: number};
            newText: string;
            metadata?: {needsConfirmation: boolean};
        };
        smartApplyData?: {
            filePath: string;
        };
    };
}

export function withMarkdownArticle(
    IdeMarkdown: React.FC<JsxMarkdownProps>,
    IdeCodeBlock?: React.FC<IdeCodeBlockProps>
): React.FC<Props> {
    return ({style, className, pluginName, taskId, content, actions, role, interactive}: Props) => {
        const elements = normalizeChunks(content);
        return (
            <ArticleContextProvider
                pluginName={pluginName}
                taskId={taskId}
                interactive={interactive}
                codeBlock={IdeCodeBlock}
                markdown={IdeMarkdown}
            >
                <div className={classNames('comate-article', className)} style={style}>
                    {typeof elements === 'string'
                        ? <IdeMarkdown content={elements} actions={actions} role={role} />
                        : <Display elements={elements} actions={actions} role={role} />}
                </div>
            </ArticleContextProvider>
        );
    };
}
