/* bca-disable */
import {useEffect, useState, useCallback, useMemo} from 'react';
import CopyIcon from './icons/CopyIcon.js';
import CheckIcon from './icons/CheckIcon.js';
import TerminalIcon from './icons/TerminalIcon.js';
import DetailIcon from './icons/DetailIcon.js';
import ExecuteIcon from './icons/ExecuteIcon.js';
import {useArticleContext} from './ArticleContextProvider.js';
import Tooltip from './Tooltip/index.js';
import Button from './Button.js';
import AcceptIcon from './icons/AcceptIcon.js';

const renderIcon = (action: string) => {
    switch (action) {
        case 'copy':
            return <CopyIcon />;
        case 'insertIntoTerminal':
            return <TerminalIcon />;
        case 'detail':
            return <DetailIcon />;
        case 'execute':
            return <ExecuteIcon />;
        case 'acceptDir':
            return <AcceptIcon />;
        default:
            return null;
    }
};

interface ActionData {
    [key: string]: any;
    includeHiddenStatsCode?: boolean;
    newText?: string;
}

interface Props {
    actions?: Record<string, (content?: string | ActionData) => void>;
    action: string;
    propagation?: boolean;
    displayName: string;
    showIcon?: boolean;
    onClick: () => void;
    tooltipText?: string;
    actionData?: string | ActionData;
    variant?: 'text' | 'primary' | 'default';
}

function ActionButton(props: Props) {
    const {
        actions,
        action,
        propagation = false,
        displayName,
        showIcon = true,
        onClick,
        tooltipText,
        actionData,
        variant = 'action',
    } = props;
    const [showSuccessLabel, setSuccessLabelVisibility] = useState(false);
    const [showTooltip, setShowTooltip] = useState(false);
    const {codeBlock: IdeCodeBlock} = useArticleContext();

    const handleMouseEnter = () => {
        setShowTooltip(true);
    };

    const handleMouseLeave = () => {
        setShowTooltip(false);
    };

    const handleActionClick = useCallback(
        async () => {
            if (showSuccessLabel) {
                return;
            }
            try {
                if (actions && actions[action]) {
                    const callback = actions[action];
                    callback && callback(actionData);
                    // propagation 属性为 true 时，执行完 action 后继续执行按钮的click事件（被插件中的handleCommand承接）
                    if (propagation) {
                        await onClick();
                    }
                }
                else {
                    await onClick();
                }
                setSuccessLabelVisibility(true);
            }
            catch (ex) {
                //
            }
        },
        [showSuccessLabel, actions, action, actionData, propagation, onClick]
    );
    // 默认执行，activateAtOnce 为 true 时，在加载时直接执行一次command
    useEffect(
        () => {
            if (actions && actions[action] && typeof actionData === 'object' && actionData?.activateAtOnce === true) {
                const callback = actions[action];
                callback && callback({
                    ...actionData,
                    ifAutoActivate: true,
                });
                // propagation 属性为 true 时，执行完 action 后继续执行按钮的click事件（被插件中的handleCommand承接）
                if (propagation) {
                    onClick();
                }
            }
        },
        []
    );

    useEffect(
        () => {
            if (showSuccessLabel) {
                const id = setTimeout(
                    () => {
                        setSuccessLabelVisibility(false);
                        clearTimeout(id);
                    },
                    5 * 1000
                );
            }
        },
        [showSuccessLabel]
    );

    const hiddenCodeBlock = useMemo(
        () => {
            if (!IdeCodeBlock) {
                return null;
            }
            // 输出内容不包含采纳内容时，需要构造隐藏的代码块，保证采纳上报数据有效
            if (typeof actionData === 'object' && actionData.includeHiddenStatsCode) {
                return <IdeCodeBlock inline={false} hidden>{actionData.newText}</IdeCodeBlock>;
            }
            return null;
        },
        [IdeCodeBlock, actionData]
    );

    if (variant === 'action') {
        return (
            <>
                {hiddenCodeBlock}
                <Tooltip overlay={!showSuccessLabel && showTooltip ? tooltipText : null}>
                    <div
                        key={action}
                        className="comate-action-button"
                        onClick={handleActionClick}
                        onMouseEnter={handleMouseEnter}
                        onMouseLeave={handleMouseLeave}
                    >
                        {showSuccessLabel
                            ? (
                                <>
                                    <CheckIcon />
                                    <span>{displayName}</span>
                                </>
                            )
                            : (
                                <>
                                    {showIcon && renderIcon(action)}
                                    <span>{displayName}</span>
                                </>
                            )}
                    </div>
                </Tooltip>
            </>
        );
    }
    return (
        <>
            {hiddenCodeBlock}
            <Button variant={variant} onClick={handleActionClick}>
                {displayName}
            </Button>
        </>
    );
}

export default ActionButton;
