import resolve from '@rollup/plugin-node-resolve';
import dts from 'rollup-plugin-dts';
import commonjs from '@rollup/plugin-commonjs';
import replace from '@rollup/plugin-replace';
import json from '@rollup/plugin-json';
import terser from '@rollup/plugin-terser';

// 厂内不用压缩
const useTerser = process.env.PLATFORM !== 'internal' && process.env.TERSER !== 'NONE';

const getPlugins = (useBuiltins = true) => {
    const plugins = [
        commonjs(),
        resolve({
            preferBuiltins: useBuiltins,
        }),
    ];

    if (useTerser) {
        plugins.push(terser());
    }

    return plugins;
};

const config = [{
    input: 'dist/server.js',
    output: {
        file: 'bundle/server.js',
        format: 'es',
        sourcemap: process.env.PLATFORM === 'internal',
    },
    external: ['fsevents'],
    plugins: [
        ...getPlugins(),
        replace({
            'process.env.PLUGIN_PATH': JSON.stringify('./plugins'),
        }),
        json(),
    ],
}, {
    input: 'dist/server.d.ts',
    output: [{ file: 'bundle/server.d.ts', format: 'es' }],
    plugins: [dts()],
}, {
    input: 'dist/fallbackServer.js',
    output: {
        file: 'bundle/fallbackServer.js',
        format: 'es',
        sourcemap: process.env.PLATFORM === 'internal',
    },
    plugins: getPlugins(),
}];

export default config;
