import iconv from 'iconv-lite';

// 判断是否为 UTF-8 编码
export function isUtf8(buffer: Buffer): boolean {
    let i = 0;
    while (i < buffer.length) {
        if ((buffer[i] & 0x80) === 0) {
            // 0xxxxxxx → 单字节字符 (ASCII)
            i++;
        }
        else if (
            (buffer[i] & 0xe0) === 0xc0 && (i + 1 < buffer.length)
            && (buffer[i + 1] & 0xc0) === 0x80
        ) {
            // 110xxxxx 10xxxxxx → 两字节字符
            i += 2;
        }
        else if (
            (buffer[i] & 0xf0) === 0xe0 && (i + 2 < buffer.length)
            && (buffer[i + 1] & 0xc0) === 0x80 && (buffer[i + 2] & 0xc0) === 0x80
        ) {
            // 1110xxxx 10xxxxxx 10xxxxxx → 三字节字符
            i += 3;
        }
        else {
            // 不符合 UTF-8 格式
            return false;
        }
    }
    return true;
}

// 自动检测编码并解码
export function decodeBuffer(data: Buffer): string {
    // 判断是否为 UTF-8 编码
    if (isUtf8(data)) {
        return data.toString('utf8');
    }
    else {
        return iconv.decode(data, 'gbk'); // 默认 GBK 解码
    }
}
