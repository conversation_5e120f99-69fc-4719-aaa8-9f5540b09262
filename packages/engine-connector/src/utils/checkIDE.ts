enum IDE {
    JETBRAINS = 'jetbrains',
    VSCODE = 'vscode',
    XCODE = 'xcode',
    COMATE = 'comate',
    WEB = 'web', // 官网对话体验上用的
}

export const isJetbrains = process.env.IDE === IDE.JETBRAINS;
export const isXcode = process.env.IDE === IDE.XCODE;
// TODO comate内核和vscode一样，vscode需要的处理逻辑comate也需要，所以这里暂时用这个判断
export const isVscode = process.env.IDE === IDE.VSCODE || process.env.IDE === IDE.COMATE;
export const isWeb = process.env.IDE === IDE.WEB;

// TODO 抽取到全局utils里
export const isDevMod = process.env.NODE_ENV === 'development';
