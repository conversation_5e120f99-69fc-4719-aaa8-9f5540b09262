import crypto from 'crypto';
import {parseRemoteUri} from './track.js';

export interface EmbeddingsWorkspaceMetadata {
    device?: string;
    project?: string;
    branch?: string;
    repoId?: string;
    uploadBasePath: string; // 文件上传给服务端的相对路径，相对于此路径
}

export function createHash(content: string | Buffer) {
    const hash = crypto
        .createHash('sha1')
        .update(content)
        .digest('hex');
    return hash;
}


export function computeRepoIdFromGitInfo(device: string, remoteUrl: string | null, branch?: string) {
    if (!remoteUrl) {
        return '';
    }
    const {host, repository} = parseRemoteUri(remoteUrl);
    const project = host === 'localhost' ? '' : `${host}/${repository}`;
    if (!device || !project) {
        // 缺少 device 和 project 时无法计算 repoId
        return '';
    }
    const composition = [device, project, branch].filter(item => !!item).join('+');
    return createHash(composition);
}
