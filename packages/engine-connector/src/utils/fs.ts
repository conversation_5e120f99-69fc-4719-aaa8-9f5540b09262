import fs from 'node:fs/promises';
/**
 * 判断文件是否存在
 */
export async function isFileExist(path: string): Promise<boolean> {
    try {
        await fs.access(path);
        return true;
    }
    catch (e) {
        return false;
    }
}

export function detectLineEnding(text: string) {
    const rn = text.indexOf('\r\n');
    const n = text.indexOf('\n');
    const r = text.indexOf('\r');

    if (rn !== -1) {
        return '\r\n'; // Windows风格
    }
    if (n !== -1) {
        return '\n'; // Unix/Linux/macOS风格
    }
    if (r !== -1) {
        return '\r'; // 旧的Mac风格(罕见)
    }
    return '\n'; // 默认
}
