import os, {homedir} from 'os';
import {sep} from 'path';
import {execa} from 'execa';

async function filterCommandFromPathOutput(output: string, commandName: string[]) {
    const supportedCommands = output
        .split(/\r?\n/)
        .map(line => line.split(sep))
        .filter(parts => parts.length > 1)
        .map(parts => parts.pop());

    return commandName.filter(command => (
        supportedCommands.find(v => v === command || v === `${command}.exe`)
    ));
}

async function filterSupportedCommandWindows(commandName: string[], defaultShell?: string) {
    const {stdout = '', stderr = ''} = await execa(
        'where',
        commandName,
        {cwd: homedir(), shell: defaultShell, reject: false}
    );
    return filterCommandFromPathOutput(`${stdout}\n${stderr}`, commandName);
}

async function filterSupportedCommandUnix(commandName: string[], defaultShell?: string) {
    const {stdout = '', stderr = ''} = await execa(
        'command',
        ['-v', ...commandName],
        {cwd: homedir(), shell: defaultShell, reject: false}
    );
    return filterCommandFromPathOutput(`${stdout}\n${stderr}`, commandName);
}

export async function filterSupportedCommand(commandName: string[], defaultShell?: string) {
    try {
        const isWin = os.platform() === 'win32';
        const installedCommands = isWin
            ? await filterSupportedCommandWindows(commandName, defaultShell)
            : await filterSupportedCommandUnix(commandName, defaultShell);
        return {
            notInstalledCommands: commandName.filter(command => !installedCommands.includes(command)),
            installedCommands,
        };
    }
    catch (e) {
        return {
            installedCommands: [],
            notInstalledCommands: [],
        };
    }
}
