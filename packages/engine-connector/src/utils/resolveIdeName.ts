import {IDE_NAME} from "@comate/kernel-shared";

// IDE 系列名称映射表参考
// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/sPLKuLl5IX/dc4c5ecc0ed248
export default function resolveIdeName(ideName: IDE_NAME, ideSeries: string) {
    switch (ideName) {
        case IDE_NAME.VSCode:
            // vscode 的返回 等价于 getIdeName 的调用结果
            return ideSeries;
        case IDE_NAME.JetBrains:
            // jetbrains 系列此处返回形如 JetBrains-Local-IntellijIdeaCommunity 的版本
            return ideSeries;
        case IDE_NAME.VS:
            // 20250324 vs 当前固定配置返回给kernel {"ideSeries: "Professional", "ideVersion": "vs2022"}
            // vs 获取真实版本后需再适配
            return 'visual-studio';
        // 占位符，以下 ide 暂未适配 kernel
        case IDE_NAME.Xcode:
            return 'xcode';
        case IDE_NAME.Eclipse:
            return 'eclipse';
        default:
            return ideName;
    }
}
