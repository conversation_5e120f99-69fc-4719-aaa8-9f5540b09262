const THROTTLE_DELAY = 500;

const sleep = (ms: number) => new Promise(r => setTimeout(r, ms));

export async function* throttleAsyncIterator<T>(
    chatStreamResponse: AsyncGenerator<T, void, unknown>,
    delay = THROTTLE_DELAY
) {
    let lastYieldTime = 0;
    let cacheChunks: T[] = [];

    let nextChunk: IteratorResult<T, void> = await chatStreamResponse.next();

    while (!nextChunk.done) {
        cacheChunks.push(nextChunk.value);
        const now = Date.now();
        if (now - lastYieldTime >= delay) {
            yield cacheChunks;
            cacheChunks = [];
            lastYieldTime = now;
        }

        const nextChunkPromise = chatStreamResponse.next();
        const raceResult = await Promise.race([
            nextChunkPromise,
            sleep(delay),
        ]);
        // raceResult 为空的话，是 sleep 先结束了，证明在下一个 chunk 到来之前，过了 THROTTLE_DELAY
        // 强制清空下缓存，避免两个 chunk 之间如果间隔时间太长，前一个 chunk 又在缓存里，没有及时处理
        if (!raceResult && cacheChunks.length) {
            yield cacheChunks;
            cacheChunks = [];
            lastYieldTime = Date.now();
        }
        nextChunk = await nextChunkPromise;
    }

    if (cacheChunks.length) {
        yield cacheChunks;
    }
}
