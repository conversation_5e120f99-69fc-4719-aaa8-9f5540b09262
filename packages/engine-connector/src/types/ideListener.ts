import {ActivationContext, KnowledgeSetBase, SystemInfoParsed} from '@comate/plugin-shared-internals';

export interface ComatePlusChatQueryPayload {
    /** 插件名 或者 PromptTemplate(自定义Prompt场景) */
    pluginName: string;
    input: {
        messageId: number;
        /** 插件的命令 或者 自定义Prompt对应的命令 */
        capability: string;
        /** InputBox 输入的内容 */
        query: string;
        /** 待补充 */
        data: any;
        /** 知识列表 */
        informationList: KnowledgeSetBase[];
        /** 是否可用智能采纳 */
        enableSmartApply?: boolean;
    };
    context: Omit<ActivationContext, 'data' | 'query'> & {
        /** context内部的query是处理过的，用于三方二轮交互的默认query置空 */
        query: string;
    };
    systemInfo: SystemInfoParsed;
    isRegenerated?: boolean;
    sessionUuid?: string;
}

export interface PromptTemplateList {
    /** 若自定义Prompt文件配置了name字段，则取其值，否则取文件名为name值 */
    name: string;
    /** 以文件名为uuid */
    uuid: string;
    description?: string;
    /** 模板创建时间, 厂内版有该字段 */
    ts?: number;
    /** prompt 内容 */
    prompt?: string;
}

export interface PromptTemplateCreatePayload {
    query?: string;
}
