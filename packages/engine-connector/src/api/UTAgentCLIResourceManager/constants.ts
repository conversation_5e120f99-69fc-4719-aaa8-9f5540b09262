export interface VersionConfig {
    id: string;
    version: string;
    path: string;
}

const pathMap = {
    UTAgent: {
        default: {
            production: 'https://baidu-ide.bj.bcebos.com/comate/bin/UTAgent/',
            test: 'https://baidu-coverage.bj.bcebos.com/comate/bin/UTAgent/'
        },
        poc: {
            production: `${process.env.HTTP_HOST}/storage/plugin-resource/UTAgent/`,
            test: `${process.env.HTTP_HOST}/storage/plugin-resource/UTAgent/`,
        }
    }
}

export const RESOURCES_MAPPING: Record<string, VersionConfig> = {
    UTAgent: {
        id: 'UTAgent',
        version: '^2.0.0',
        path: pathMap.UTAgent[
            process.env.PLATFORM === 'poc' ? 'poc' : 'default'
        ][
            process.env.ENVIROMENT === 'test' ? 'test' : 'production'
        ],
    },
};
