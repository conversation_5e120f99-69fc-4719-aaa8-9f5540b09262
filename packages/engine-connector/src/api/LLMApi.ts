import path from 'node:path';
import FormData from 'form-data';
import {
    FunctionDefinition,
    formatPrompt,
    axiosInstance,
    TextModel,
    ModelOptions,
    SSEProcessor,
    createAxiosInstance,
    getApiHost,
    SAAS_API_HOST,
    INTERNAL_API_HOST,
} from '@comate/plugin-shared-internals';
import {ChatMessage, ConversationBufferMemory} from '@comate/plugin-engine';
import {LoggerRoleInstance} from '../service/Logger.js';
import {Mediator} from '../service/Mediator.js';
import {UserDetailWithId} from '../service/User.js';
import {isSaaS} from '../utils/isSaaS.js';
import resolveIdeName from '../utils/resolveIdeName.js';

export enum EB35FunctionType {
    CODE_TO_COMMENT = 'CODE_TO_COMMENT',
    COMMENT_TO_CODE = 'COMMENT_TO_CODE',
    CODE_EXPLAIN = 'CODE_EXPLAIN',
    CODE_INLINE_COMMENT = 'CODE_INLINE_COMMENT',
    CODE_SPLIT = 'CODE_SPLIT',
    CODE_TEMPLATE = 'CODE_TEMPLATE',
}

interface FunctionCall {
    name: string;
    arguments: string;
    thoughts?: string;
}

interface ErnieBotResponse {
    result: string;
    function_call?: FunctionCall;
}

interface ErnieBotStreamingResponse {
    result: string;
    end: boolean;
}

interface ErnieBotParams {
    messages: ChatMessage[];
    pluginName: string;
    functions?: FunctionDefinition[];
    functionType?: EB35FunctionType;
    lang?: string;
    isInternalInvoke?: boolean;
    model?: TextModel;
    modelOptions?: ModelOptions;
    isStreaming?: boolean;
    device?: string;
}

interface AskForTextParams {
    promptTemplate: string;
    args?: Record<string, unknown>;
    pluginName: string;
    capabilityName?: string;
    model?: TextModel;
    modelOptions?: ModelOptions;
    device?: string;
}

interface AskForFunctionParams {
    functions: FunctionDefinition[];
    promptTemplate: string;
    pluginName: string;
    args?: Record<string, unknown>;
    model?: TextModel;
}

// 匹配不上的情况服务默认3.5兜底
const modelMap = {
    [TextModel.Default]: 'EB',
    [TextModel.ErnieBot]: 'EB',
    [TextModel.ErnieBot4]: 'EB_4',
    [TextModel.ErnieBot128]: 'EB_128k',
    [TextModel.ErnieBot4Turbo]: 'EB_4_turbo',
    [TextModel.ErnieBot4Turbo128]: 'EB_4_turbo_128k',
};

// 匹配不上的情况服务默认3.5兜底
const modelMapMaxOutput = {
    [TextModel.Default]: 2048,
    [TextModel.ErnieBot]: 2048,
    [TextModel.ErnieBot4]: 2048,
    [TextModel.ErnieBot128]: 4096,
    [TextModel.ErnieBot4Turbo]: 2048,
    [TextModel.ErnieBot4Turbo128]: 4096,
};

export const modelMapMaxInputString = {
    [TextModel.ErnieBot4]: 20000,
    [TextModel.ErnieBot4Turbo128]: 516096,
};

const vpcAxiosInstance = createAxiosInstance();
export class LLMApi {
    mediator: Mediator;
    logger: LoggerRoleInstance;
    user!: UserDetailWithId;

    constructor(mediator: Mediator, logger: LoggerRoleInstance) {
        this.mediator = mediator;
        this.logger = logger;
    }

    async init(user: UserDetailWithId) {
        this.user = user;
    }

    private formatErnieBotParams(ernieBotParams: ErnieBotParams) {
        const {
            messages,
            functions,
            functionType,
            lang,
            pluginName,
            isInternalInvoke,
            model,
            modelOptions,
        } = ernieBotParams;
        const body: Record<string, any> = {
            messages,
            username: this.user.name,
            // 如果是内部调用，需要加上前缀
            pluginName: isInternalInvoke ? `comate-plus-platform@${pluginName}` : pluginName,
        };
        if (modelOptions) {
            if (modelOptions.openMaxOutput) {
                body['max_output_tokens'] = modelMapMaxOutput[model || TextModel.Default];
            }
            body.temperature = modelOptions.temperature;
            body['top_p'] = modelOptions.topP;
            body['penalty_score'] = modelOptions.penaltyScore;
        }
        if (functions) {
            body.functions = functions;
        }
        if (functionType) {
            body.function = functionType;
            body.lang = lang;
        }
        if (isSaaS()) {
            body.key = this.user.license;
        }
        if (model) {
            body.model = modelMap[model];
        }
        return body;
    }

    private formatVpcParams(ernieBotParams: ErnieBotParams) {
        const formData = new FormData();
        formData.append('username', this.user.name);
        formData.append('ide', resolveIdeName(this.user.ideName, this.user.ideSeries));
        formData.append('ideVersion', this.user.ideVersion);
        formData.append('pluginVersion', this.user.version);
        formData.append('model', 'ERNIE_BOT');
        formData.append('function', 'CUSTOMER_QUERY');
        formData.append('userInput', ernieBotParams.messages[0].content);
        formData.append('key', this.user.license);
        formData.append('device', ernieBotParams.device);
        // 这两个参数没用但是一定要带着(┯_┯)
        formData.append('row', '1');
        formData.append('col', '1');
        return formData;
    }

    async callErnieBot(ernieBotParams: ErnieBotParams): Promise<ErnieBotResponse> {
        const apiHost = getApiHost();

        try {
            // vpc和poc调用
            if (apiHost !== SAAS_API_HOST && apiHost !== INTERNAL_API_HOST) {
                const formData = this.formatVpcParams(ernieBotParams);
                const response = await vpcAxiosInstance(`${apiHost}/api/generate`, {
                    method: 'POST',
                    data: formData,
                })
                    .then(res => res.data);

                if (response.status === 'OK') {
                    return {result: response.data.content};
                }
                else {
                    this.logger.error('vpc callErnieBot error', response);
                    return {} as any;
                }
            }
            else {
                const body = this.formatErnieBotParams(ernieBotParams);
                const response = await axiosInstance('/api/v2/api/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    data: JSON.stringify(body),
                })
                    .then(res => res.data);
                if (response.status === 'OK') {
                    this.logger.info('callErnieBot response.data', response.data);
                    return response.data;
                }
                else {
                    this.logger.error('callErnieBot error', response);
                    return {} as any;
                }
            }
        }
        catch (error) {
            this.logger.error('callErnieBot error', (error instanceof Error) ? error.toString() : error);
            return {} as any;
        }
    }

    async callErnieBotStreaming(ernieBotParams: ErnieBotParams): Promise<AsyncGenerator<ErnieBotStreamingResponse>> {
        const body = this.formatErnieBotParams(ernieBotParams);
        try {
            const response = await axiosInstance('/api/v2/api/generate/stream', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                responseType: 'stream',
                data: JSON.stringify(body),
            });
            const processor = new SSEProcessor<ErnieBotStreamingResponse>(response.data);
            return processor.processSSE();
        }
        catch (error) {
            this.logger.error('callErnieBotStreaming error', error);
            return {} as any;
        }
    }

    // 续写接口 https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/mcV1ZM02Cz/PU5CIeen_Fh2il
    async ec2GenerateCode(content: string, user: string, path: string) {
        const data = new URLSearchParams({
            ide: 'comate-engine',
            username: user,
            repo: '',
            path,
            content,
            row: '1',
            col: '1',
            pluginVersion: '1.0.0',
            model: 'ERNIE_CODE',
            function: 'GENERATE_CODE',
        });
        return axiosInstance('/api/generate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            data: data.toString(),
        })
            .then(res => res.data);
    }

    async askForText(
        {promptTemplate, pluginName, args, model, modelOptions, device}: AskForTextParams
    ): Promise<string> {
        const prompt = formatPrompt(promptTemplate, args);
        const messages: ChatMessage[] = [
            {role: 'user', content: prompt},
        ];
        const response = await this.callErnieBot({messages, pluginName, model, modelOptions, device});
        return response.result ?? '';
    }

    askForTextStreaming(
        {promptTemplate, pluginName, args, model, modelOptions, capabilityName}: AskForTextParams
    ): Promise<AsyncGenerator<ErnieBotStreamingResponse>> {
        const prompt = formatPrompt(promptTemplate, args);
        const message = {role: 'user', content: prompt} as const;
        let messages: ChatMessage[] = [message];
        if (modelOptions?.enableMultiturnDialogue) {
            // const conversationMessages = this.mediator.conversationBufferMemory.get(conversationId);
            // messages.unshift(...conversationMessages);
            const conversationId = this.mediator.conversationBufferMemory.getConversationId(
                modelOptions.enableMultiturnDialogue,
                pluginName,
                capabilityName!
            );
            this.mediator.conversationBufferMemory.putUser(
                modelOptions.enableMultiturnDialogue,
                conversationId,
                message
            );
            const trimedConversation = this.mediator.conversationBufferMemory.trimConversation(conversationId);
            messages = trimedConversation || messages;
        }
        const response = this.callErnieBotStreaming({messages, pluginName, model, modelOptions, isStreaming: true});
        return response;
    }

    async askForFunction({functions, promptTemplate, pluginName, args, model}: AskForFunctionParams) {
        let isInternalInvoke = false;
        if (args && args.$$temperature && args.$$temperature === '0.7') {
            delete args.$$temperature;
            isInternalInvoke = true;
        }
        const messages: ChatMessage[] = [
            {role: 'user', content: formatPrompt(promptTemplate, args)},
        ];
        const response = await this.callErnieBot({messages, functions, pluginName, isInternalInvoke, model});
        return response.function_call ?? null;
    }

    async askForCode(promptTemplate: string, args?: Record<string, unknown>): Promise<string> {
        const prompt = formatPrompt(promptTemplate, args);
        return this.ec2GenerateCode(prompt, this.user.name, '').then(res => res.data.result);
    }

    async explainCode(code: string, filePath: string, pluginName: string): Promise<string> {
        const messages: ChatMessage[] = [
            {role: 'user', content: code},
        ];
        const response = await this.callErnieBot({
            messages,
            pluginName,
            functionType: EB35FunctionType.CODE_EXPLAIN,
            lang: path.extname(filePath),
        });
        return response.result;
    }

    async getDocCommentForCode(code: string, filePath: string, pluginName: string): Promise<string> {
        const messages: ChatMessage[] = [
            {role: 'user', content: code},
        ];
        const response = await this.callErnieBot({
            messages,
            pluginName,
            functionType: EB35FunctionType.CODE_TO_COMMENT,
            lang: path.extname(filePath),
        });
        return response.result;
    }

    async addCommentForCode(code: string, filePath: string, pluginName: string): Promise<string> {
        const messages: ChatMessage[] = [
            {role: 'user', content: code},
        ];
        const response = await this.callErnieBot({
            messages,
            pluginName,
            functionType: EB35FunctionType.COMMENT_TO_CODE,
            lang: path.extname(filePath),
        });
        return response.result;
    }
}
