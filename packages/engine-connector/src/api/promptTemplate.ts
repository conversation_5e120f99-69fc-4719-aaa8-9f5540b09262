import {kernel} from '@comate/kernel-shared';
import {axiosInstance} from '@comate/plugin-shared-internals';

export async function apiGetPromptTemplateList() {
    try {
        const res = await axiosInstance<{
            status: string;
            message: string;
            data: {
                content: Array<{
                    promptId: string;
                    promptName: string;
                    promptDesc: string;
                    prompt: string;
                }>;
            };
        }>('/api/openapi/prompt/list', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Username': kernel.env.isSaaS ? kernel.config.license : kernel.config.username,
            },
            data: JSON.stringify({
                page: 0,
                pageSize: 500,
                // 默认0：更新时间倒序，1：更新时间正序
                sortByTime: 0,
                // 默认0：只有启用的指令，1：启用+禁用的指令，2：只有禁用的指令
                needUnused: 0,
                key: kernel.config.license,
            }),
        });
        if (res.data?.status !== 'OK') {
            throw new Error(res.data.message);
        }
        return res.data?.data?.content.map(item => ({
            uuid: item.promptId,
            name: item.promptName,
            description: item.promptDesc,
            prompt: item.prompt,
        })) ?? [];
    }
    catch (e) {
        throw e;
    }
}
