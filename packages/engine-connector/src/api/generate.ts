import qs from 'qs';
import {axiosInstance} from '@comate/plugin-shared-internals';
import {MultiAcceptData} from '../service/Track/api.js';
import {kernel} from '@comate/kernel-shared';

export type Model = 'COMPLETION_AGENT' | 'UNIT_TEST_AGENT' | 'DEBUG_AGENT' | 'SECURITY_AGENT' | 'FULL_STACK_AGENT' | 'F2C_AGENT';

export interface CreateUUIDParams {
    repo: string;
    branch?: string;
    path: string;
    uuid?: string;
    content: string;
    function?: string;
    model?: Model;
    /** 用于多文件，支持采纳放弃的场景（智能体） */
    multiSuggestions?: {fileContent: MultiAcceptData[]};
}

interface CreateUUIDResponse {
    status: string;
    message?: string;
    data: {
        uuid: string;
    };
}

// 通过推荐内容接口获取 uuid
export function generate({
    repo,
    branch,
    path,
    content,
    function: modelFunction,
    model,
    uuid,
    multiSuggestions,
}: CreateUUIDParams) {
    const data = {
        ide: kernel.env.ideName,
        ideVersion: kernel.env.ideVersion,
        username: kernel.config.username,
        repo,
        branch,
        path,
        content,
        generatedContent: content,
        row: '1',
        col: '1',
        pluginVersion: kernel.env.extensionVersion,
        model,
        function: modelFunction,
        key: kernel.config.key,
        device: kernel.env.deviceId,
        uuid,
        multiSuggestions,
    } as any;
    if (data.multiSuggestions) {
        data.multiSuggestions = JSON.stringify(data.multiSuggestions);
    }
    const query = qs.stringify(data);
    return axiosInstance<CreateUUIDResponse>('/api/generate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        data: query,
    })
        .then(res => res.data.data?.uuid)
        .catch(() => {
            throw new Error('generate uuid error');
        });
}
