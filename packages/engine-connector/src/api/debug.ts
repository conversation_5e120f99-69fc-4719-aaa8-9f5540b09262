import {IncomingMessage} from 'http';
import {kernel} from '@comate/kernel-shared';
import {axiosInstance} from '@comate/plugin-shared-internals';
import {WorkflowResult} from '../service/Roundtrip/Workflow/WorkflowBase.js';
import {ToolCallEvent} from '../service/Roundtrip/types.js';

interface UserDetail {
    username: string;
    version: string;
}

function headerWithUsername(userDetail: UserDetail) {
    const name = userDetail.username || '';
    return {
        'X-Source': 'COMATE',
        'X-Client-Version': kernel.env.ideVersion,
        'Content-Type': 'application/json',
        'login-name': encodeURIComponent(name),
        'Uuap-login-name': encodeURIComponent(name),
    };
}

interface HistoryMessageItem {
    role: 'user' | 'assistant';
    content: string;
}

interface AnalyzeParams {
    userDetail: UserDetail;
    query: string;
    contexts?: string;
    platform?: string;
    repo?: string;
    conversationId: string;
    history?: HistoryMessageItem[];
    cwd?: string;
}


export interface ClosestFunctionContext {
    filePath: string;
    lineNum: number;
    maxNum?: number;
    forwardRangeNum: number;
    backRangeNum: number;
}

export interface FunctionDefinitionContext {
    filePath: string;
    lineNum: number;
    symbolName: string;
    symbolType: string;
}

export interface FileContext {
    filePath: string;
    lineNum: number;
    retrieval: boolean;
    forwardRangeNum: number;
    backRangeNum: 10, // 指定行号下面的行数
}

export type AnalyzeContext = Record<string, ClosestFunctionContext | FunctionDefinitionContext | FileContext>;

export interface AnalyzeData {
    recordId: string;
    rewriteQuery: string;
    queryType?: string;
    errorReason: string;
    context: AnalyzeContext;
}

export interface AnalyzeResponse {
    requestId: string;
    code: number;
    message?: string;
    data?: AnalyzeData;
}

export async function agenticAnalyze({userDetail, ...params}: AnalyzeParams & {taskId: string}) {
    const result = await axiosInstance.post<AnalyzeResponse>(
        '/api/autodebug/api/v1/errorfix/agenticAnalysis',
        params,
        {
            headers: headerWithUsername(userDetail),
        }
    );

    if (result.data.code !== 200) {
        throw new Error(result.data.message);
    }

    return result.data.data;
}

export async function analyze({userDetail, ...params}: AnalyzeParams) {
    const result = await axiosInstance.post<AnalyzeResponse>(
        '/api/autodebug/api/v1/errorfix/agentAnalysis',
        params,
        {
            headers: headerWithUsername(userDetail),
        }
    );

    if (result.data.code !== 200) {
        throw new Error(result.data.message);
    }

    return result.data.data;
}

export interface CodeSnippetItem {
    filePath: string;
    startLineNum: number;
    endLineNum: number;
    content: string;
    symbolName?: string;
    type?: string;
}

export interface SysInfo {
    os: string;
    defaultShell: string;
    homeDir: string;
    workspaceFolder: string;
    workspaceFolderTree: string[];
}

export type CodeContext = Record<string, CodeSnippetItem[]>;

interface AutoDebugFixParams {
    userDetail: UserDetail;
    recordId?: string;
    device: string;
    conversationId: string;
    query: string;
    context?: CodeContext;
    queryType?: string;
    history?: HistoryMessageItem[];
}

export interface AutoDebugFixResult {
    isEnd: boolean;
    result: string;
    content?: string;
    toolUse: ToolCallEvent[];
    uuid?: string;
}

export interface AutoDebugFixResponse {
    requestId: string;
    code: number;
    message?: string;
    data?: AutoDebugFixResult
}

export interface AgenticFixParams extends AutoDebugFixParams {
    taskId: string;
    toolUseResults: WorkflowResult[];
    type: 'NORMAL' | 'FAILRETRY' | 'REGENERATE';
}

export async function agenticAutoDebugFix({userDetail, ...params}: AgenticFixParams) {
    const result = await axiosInstance.post<IncomingMessage>(
        '/api/autodebug/api/v1/errorfix/agenticSolution',
        params,
        {
            headers: headerWithUsername(userDetail),
            responseType: 'stream',
        }
    );
    return result.data;
}

export async function autoDebugFix({userDetail, ...params}: AutoDebugFixParams) {
    const result = await axiosInstance.post<IncomingMessage>(
        '/api/autodebug/api/v1/errorfix/agentSolution',
        params,
        {
            headers: headerWithUsername(userDetail),
            responseType: 'stream',
        }
    );
    return result.data;
}

interface ResponseBase<T> {
    requestId: string;
    code: number;
    message?: string;
    data: T;
}

interface CodeAdoptParams {
    userDetail: UserDetail;
    conversationId: string;
    adoptedCode: string;
}

export async function codeAdopt({userDetail, ...params}: CodeAdoptParams) {
    const result = await axiosInstance.post<ResponseBase<any>>(
        '/api/autodebug/api/v1/errorfix/agenticCodeAdopt',
        {...params, type: 'AGENT', platform: 'IDE'},
        {
            headers: headerWithUsername(userDetail),
        }
    );
    return result.data;
}

interface CodeGenerateParams {
    userDetail: UserDetail;
    uuid: string;
    conversationId: string;
    generatedCode: string;
}

export async function codeGenerate({userDetail, ...params}: CodeGenerateParams) {
    const result = await axiosInstance.post<ResponseBase<any>>(
        '/api/autodebug/api/v1/errorfix/agenticCodeGenerate',
        {...params, type: 'AGENT', platform: 'IDE'},
        {
            headers: headerWithUsername(userDetail),
        }
    );
    return result.data;
}

export async function runStatus(
    {userDetail, ...params}: {taskId: string; status: string, userDetail: UserDetail}
) {
    const result = await axiosInstance.post<ResponseBase<any>>(
        '/api/autodebug/api/v1/errorfix/agenticRunStatus',
        params,
        {
            headers: headerWithUsername(userDetail),
        }
    );
    return result.data;
}

export async function taskStatus(
    {userDetail, ...params}: {conversationId: string; status: string, userDetail: UserDetail}
) {
    const result = await axiosInstance.post<ResponseBase<any>>(
        '/api/autodebug/api/v1/errorfix/agenticConversationStatus',
        params,
        {
            headers: headerWithUsername(userDetail),
        }
    );
    return result.data;
}

