// import fs from 'node:fs/promises';
// import path from 'path';
import crypto from 'crypto';
import {LoggerRoleInstance} from './Logger.js';
import {Mediator} from './Mediator.js';
import {DiagnosticCache} from '@comate/plugin-engine';
import {
    ACTION_DIAGNOSTIC_SCAN,
    DiagnosticScanChangedFiles,
    DiagnosticScanPayload,
    DiagnosticScanInvokePayload,
    ActivationContext,
    SystemInfoParsed,
} from '@comate/plugin-shared-internals';

export class EngineDiagnosticCache extends DiagnosticCache {
    private readonly logger: LoggerRoleInstance;
    private readonly mediator: Mediator;
    private pluginName: string = '';
    private capabilityName: string = '';

    constructor(mediator: Mediator, logger: LoggerRoleInstance) {
        super();
        this.logger = logger;
        this.mediator = mediator;
    }

    async getGray() {
        try {
            const userDetail = await this.mediator.userDetail();
            const response: any = await fetch('https://comate-sec.baidu-int.com/api/v2/analysis/settings', {
                method: 'GET',
                headers: {
                    'Comate-Username': userDetail.username,
                },
            });
            const result = await response.json();
            const scanEnabled = result.data.scanConfiguration.autoScanEnabled;
            return scanEnabled;
        }
        catch (ex) {
            return false;
        }
    }

    async handleChangedFiles(payload: DiagnosticScanPayload, changedFiles: DiagnosticScanChangedFiles) {
        try {
            const sessionId = crypto.randomUUID();
            await this.mediator.launchSendToPlugin(
                sessionId,
                {
                    action: ACTION_DIAGNOSTIC_SCAN,
                    payload: {
                        ...payload,
                        changedFiles,
                        scanType: 'change',
                        diagnosticScanId: Date.now().toString(),
                    } as DiagnosticScanInvokePayload,
                }
            );
        }
        catch (ex: any) {
            this.logger.error('处理诊断变更异常', ex?.message);
        }
    }

    async init(payload: DiagnosticScanPayload) {
        this.pluginName = payload.pluginName;
        this.capabilityName = payload.capabilityName;

        const sessionId = crypto.randomUUID();
        await this.mediator.launchSendToPlugin(
            sessionId,
            {
                action: ACTION_DIAGNOSTIC_SCAN,
                payload: {
                    ...payload,
                    scanType: 'init',
                    diagnosticScanId: Date.now().toString(),
                } as DiagnosticScanInvokePayload,
            }
        );
    }

    async beforeGet(payload: DiagnosticScanPayload & {context: ActivationContext}) {
        const sessionId = crypto.randomUUID();

        await this.mediator.launchSendToPlugin(
            sessionId,
            {
                action: ACTION_DIAGNOSTIC_SCAN,
                payload: {
                    ...payload,
                    pluginName: this.pluginName,
                    capabilityName: this.capabilityName,
                    scanType: 'open',
                    diagnosticScanId: Date.now().toString(),
                } as DiagnosticScanInvokePayload,
            }
        );
    }

    async getCount(systemInfo: SystemInfoParsed) {
        try {
            const sessionId = crypto.randomUUID();
            for await (
                const message of this.mediator.sendToPlugin(
                    sessionId,
                    {
                        action: ACTION_DIAGNOSTIC_SCAN,
                        payload: {
                            pluginName: this.pluginName,
                            capabilityName: this.capabilityName,
                            systemInfo,
                            scanType: 'count',
                            diagnosticScanId: Date.now().toString(),
                        } as DiagnosticScanInvokePayload,
                    }
                )
            ) {
                // 只需要处理返回的第一个message
                return message?.data.payload.chunk as unknown as number;
            }
        }
        catch (error) {
            this.logger.error('处理诊断变更数量异常', (error as Error).message);
            return;
        }
    }
}
