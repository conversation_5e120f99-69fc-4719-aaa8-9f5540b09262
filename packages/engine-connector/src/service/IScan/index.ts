import fs from 'node:fs/promises';
import {
    ACTION_SCAN_NOTIFICATION,
    ACTION_START_ISCAN,
    IScanBuildResult,
    IScanInvokePayload,
    IScanResult,
} from '@comate/plugin-shared-internals';
import * as git from 'isomorphic-git';
import {LoggerRoleInstance} from '../Logger.js';
import {Mediator} from '../Mediator.js';

export const sleep = (ms: number) => new Promise(r => setTimeout(r, ms));

export class IScan {
    private readonly logger: LoggerRoleInstance;
    private readonly mediator: Mediator;
    private readonly pluginName: string = 'aiscan';
    private readonly capabilityName: string = 'codeStyleCheck';
    private latestJobBuildId: string | null = null;

    constructor(mediator: Mediator, logger: LoggerRoleInstance) {
        this.logger = logger;
        this.mediator = mediator;
    }

    async startScan(systemInfo: IScanInvokePayload['systemInfo']): Promise<boolean> {
        try {
            const sessionId = crypto.randomUUID();

            for await (
                const message of this.mediator.sendToPlugin(
                    sessionId,
                    {
                        action: ACTION_START_ISCAN,
                        payload: {
                            systemInfo,
                            pluginName: this.pluginName,
                            capabilityName: this.capabilityName,
                            scanType: 'init',
                        } as IScanInvokePayload,
                    }
                )
            ) {
                const jobBuildResult = message?.data.payload.chunk as unknown as IScanBuildResult;
                const {jobBuildId, errorMsg, status} = jobBuildResult;
                this.logger.info('jobBuildResult', jobBuildId, errorMsg, status);
                if (jobBuildId) {
                    this.latestJobBuildId = jobBuildId;
                    this.reportScanResult(systemInfo, jobBuildId);
                    return true;
                }
                else {
                    this.logger.error('iScan job build error', errorMsg);
                    return false;
                }
            }
            return false;
        }
        catch (error) {
            this.logger.error('startScan failed', (error instanceof Error) ? error.toString() : error);
            return false;
        }
    }

    async reportScanResult(systemInfo: IScanInvokePayload['systemInfo'], jobBuildId: string, maxDuration = 300000) {
        const startTime = Date.now();
        let isSuccess = false;
        while (Date.now() - startTime < maxDuration && !isSuccess) {
            // 如果当前jobBuildId不是最新的，则直接返回
            if (this.latestJobBuildId !== jobBuildId) {
                this.logger.info('Scan job cancelled', jobBuildId);
                return;
            }

            try {
                const sessionId = crypto.randomUUID();
                for await (
                    const message of this.mediator.sendToPlugin(
                        sessionId,
                        {
                            action: ACTION_START_ISCAN,
                            payload: {
                                systemInfo,
                                pluginName: this.pluginName,
                                capabilityName: this.capabilityName,
                                scanType: 'getResult',
                                jobBuildId,
                            } as IScanInvokePayload,
                        }
                    )
                ) {
                    // 再次检查是否是最新任务
                    if (this.latestJobBuildId !== jobBuildId) {
                        this.logger.info('Scan job cancelled during processing', jobBuildId);
                        return;
                    }

                    const result = message?.data.payload.chunk as unknown as IScanResult;
                    this.logger.info('reportScanResult', jobBuildId, result);
                    if (result.status === "SUCC") {
                        isSuccess = true;
                        const codestyleNum = (result.codestyle?.high || 0) + (result.codestyle?.middle || 0) + (result.codestyle?.low || 0);
                        const bugbyeNum = (result.bugbye?.high || 0) + (result.bugbye?.middle || 0) + (result.bugbye?.low || 0);
                        const notification = (codestyleNum === 0 && bugbyeNum === 0)
                            ? 'Comate代码扫描已完成，未发现任何问题！您可以放心继续下一步操作。'
                            : `Comate代码扫描已完成，发现 ${codestyleNum === 0 ? '' : `${codestyleNum} 个代码规范问题，`} ${bugbyeNum === 0 ? '' : `${bugbyeNum} 个代码缺陷问题,`} 建议您立即修复，避免合入被卡位。`;
                        const branch = await git.currentBranch({fs, dir: systemInfo.cwd});
                        const pushCommand = branch
                        ? `git push origin HEAD:refs/for/${branch}`
                        : 'git push origin master';
                        this.mediator.sendToIde(ACTION_SCAN_NOTIFICATION, {
                            notification,
                            jobBuildId,
                            buttonType: (codestyleNum !== 0 || bugbyeNum !== 0) ? 'detailButton' : 'pushButton',
                            pushCommand,
                        });
                    }
                    break;
                }
                await sleep(10000);
            }
            catch (error) {
                this.logger.error('get result failed', (error instanceof Error) ? error.toString() : error);
                await sleep(10000);
            }
        }
        if (!isSuccess) {
            this.logger.error('get result timed out');
        }
    }
}
