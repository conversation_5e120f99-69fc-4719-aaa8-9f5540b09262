export {WorkflowStatus} from '@comate/plugin-shared-internals';

export type ComposerFileAction =
    | 'create'
    | 'edit'
    | 'rewrite'
    | 'delete'
    | 'explain'
    | 'preview'
    | 'replaceFrom'
    | 'searchReplace';

export enum AcceptState {
    UNTOUCHED = 0,
    ACCEPT = 1,
    REJECT = 2,
}

export type IllegalFileType = 'image' | 'video' | 'audio';
export interface IllegalTextExtDefinition {
    type: IllegalFileType;
    extensions: string[];
}
