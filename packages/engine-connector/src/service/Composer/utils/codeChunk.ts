import {DocumentPosition} from '@comate/plugin-shared-internals';
import {readFileSync} from 'node:fs';
import {join} from 'node:path';
import {detectLineEnding} from '../../../utils/fs.js';

export const readFileContent = (fileContent: string, contentStart: DocumentPosition, contentEnd: DocumentPosition) => {
    try {
        const eol = detectLineEnding(fileContent);
        const lines = fileContent.split(/\r?\n/).slice(contentStart.line, contentEnd.line + 1);

        if (contentStart.line === contentEnd.line) {
            lines[0] = lines[0].slice(contentStart.column, contentEnd.column + 1);
        }
        else {
            lines[0] = lines[0].slice(contentStart.column);
            lines[lines.length - 1] = lines[lines.length - 1].slice(0, contentEnd.column + 1);
        }

        return lines.join(eol);
    }
    catch (ex) {
        return '';
    }
};

export const createEmbeddingCodeChunk = (
    repo: string,
    path: string,
    contentStart: DocumentPosition,
    contentEnd: DocumentPosition
) => {
    const absolutePath = join(repo, path);
    const fileContent = readFileSync(absolutePath, 'utf-8');
    const content = readFileContent(fileContent, contentStart, contentEnd);
    return {
        repo,
        type: 'embeddingSearchResult',
        path,
        content,
        contentStart,
        contentEnd,
    };
};
