import crypto from 'node:crypto';
import {applySearchReplaceChunk} from '@comate/plugin-shared-internals';
import {WorkflowStatus} from '../types.js';
import {sleep} from '../utils/index.js';
import {EditComposerTask} from './EditComposerTask.js';
import {Task} from './Task.js';

function findStartDifferentLine(originalContent?: string | null, newContent?: string): number {
    if (!originalContent || !newContent) {
        return 0;
    }
    const originalContentLines = originalContent.split(/\r?\n/);
    const newContentLines = newContent.split(/\r?\n/);

    const startLine = originalContentLines.findIndex((line, i) => line !== newContentLines[i]);
    // 如果-1要么就是完全相同，要么就是原内容行数小于
    return startLine === -1 ? Math.max(0, originalContentLines.length - 1) : startLine;
}

// 与 edit 任务的区别在于，这里的替换是在两个 Block 中描述的，比如：
// ```replaceFrom:/file/path
// text to replace
// ```
// ```replaceTo:/file/path
// replacement text
// ```
// replaceFrom 是要替换的文本，replaceTo 是替换后的文本
// 基于 edit 任务，将 replaceFrom 的每一行加上 `- ` 前缀，replaceTo 的每一行加上 `+ ` 前缀
export class ReplaceComposerTask extends EditComposerTask {
    private from = new Map<string, string>();
    private to = new Map<string, string>();
    readonly id = crypto.randomUUID();
    skipIndentWhenMatchDiffLine = true;
    // 记录当前正在流式更新内容的任务 id，因为相同文件的多个 replace 任务都合成了一个，现在做法是前一个结束后状态什么的都是 DONE 了
    // 碰到后面的要合并时从新 restart 这个任务，restart 的这个任务不能被前面已经处理过的 replaceTo 给结束了
    currentActiveId: string = this.id;
    // 如果多个 ReplaceTask 修改相同的文件，将他们都合并成一个任务，进行统一处理，避免修改冲突和文件引用问题
    // 后面新建的任务可以通过这里的 combineTask 拿到第一个新建的任务，所以更新都转到这个任务上
    private combineTask?: ReplaceComposerTask;

    static create(virtualEditor: any, repo: any, action: string, filePath: string, subTasks: Task[], source: string) {
        const replaceTask = new ReplaceComposerTask(virtualEditor, repo, action, filePath, source);
        const previousReplaceTaskWithSamePath = subTasks.find(task => (
            task.action === 'replaceFrom' && task.absolutePath === replaceTask.absolutePath
        ));
        if (previousReplaceTaskWithSamePath) {
            replaceTask.setCombineTask(previousReplaceTaskWithSamePath as ReplaceComposerTask);
        }
        return replaceTask;
    }

    get searchReplacePatchContent() {
        const patches = [];
        for (const [id, fromText] of this.from) {
            const toText = this.to.get(id) ?? '';
            const patchContent = `<<< SEARCH <<<\n${fromText ?? ''}\n`
                + `=== REPLACED_BY ===\n${toText ?? ''}\n`
                + `>>> END >>>`;
            patches.push(patchContent);
        }
        return patches.join('\n\n');
    }

    async update(_content: string, closed: boolean = false) {
        if (this.status === WorkflowStatus.CANCELLED) {
            this.applyStreamFinished = true;
            return;
        }

        const {patchedContent} = applySearchReplaceChunk(
            this.originalContent || '',
            this.searchReplacePatchContent,
            {stream: !closed}
        );

        this.generatedContentLines.lines = patchedContent.split(/\r?\n/);
        this.applyStreamFinished = closed;
        if (closed) {
            this.content = patchedContent;
            if (closed) {
                // eslint-disable-next-line max-depth
                while (!this.finish) {
                    await sleep(100);
                }
                this.computeDiffLine();
            }
        }
        else {
            this.computeDiffLineThrottled();
        }
    }

    protected async autoApproveWriteTaskInVSCodeAgentMode(_content: string) {
        // 不要自动采纳
    }

    /** 如果存在 combineTask 则这个实例本身不做任务处理，在界面上是不可见的 */
    get visible() {
        return !this.combineTask;
    }

    setCombineTask(combineTask: ReplaceComposerTask) {
        this.combineTask = combineTask;
    }

    /** 解析 replaceTo 内容时调用， 这里的 content 会被当成要替换的最终文本 */
    replaceTo(content: string, closed: boolean = false) {
        if (this.combineTask) {
            return this.combineTask.replaceToWithId(this.id, content, closed);
        }
        return this.replaceToWithId(this.id, content, closed);
    }

    /** 解析 replaceFrom 内容时调用， 这里的 content 会被当成要替换的原始内容，可以多次调用，效果是流式地删除内容 */
    replaceFrom(content: string, forcePreview?: boolean, forceClosed = false) {
        if (this.combineTask) {
            return this.combineTask.replaceFromWithId(this.id, content, forcePreview, forceClosed);
        }
        return this.replaceFromWithId(this.id, content, forcePreview, forceClosed);
    }

    /** 创建任务时如果 block 已经闭合时调用 */
    end(content: string, forcePreview?: boolean) {
        if (this.combineTask) {
            return this.combineTask.endWithId(this.id, content, forcePreview);
        }
        return this.endWithId(this.id, content, forcePreview);
    }

    async replaceToWithId(id: string, content: string, closed: boolean = false) {
        if (this.to.get(id) === content && (this.currentActiveId !== id || !closed)) {
            return;
        }
        if (this.status === WorkflowStatus.RUNNING) {
            this.to.set(id, content);
            await this.update('', this.currentActiveId === id ? closed : false);
        }
    }

    async replaceFromWithId(id: string, content: string, forcePreview?: boolean, forceClosed = false) {
        const previousFromText = this.from.get(id);
        // 减少重复
        if (this.from.get(id) === content && !forceClosed) {
            return;
        }
        this.from.set(id, content);
        if (this.status === WorkflowStatus.UNREADY) {
            await this.start('', forcePreview);
        }
        else if (this.status === WorkflowStatus.RUNNING) {
            // 更新 replaceFrom 内容时，除非强制 closed 否则不关闭，因为后面可能还有 replaceTo 内容要更新
            await this.update('', forceClosed);
        }
        // 如果是 DONE 状态，且 from 内容是新增的，可能是前一个任务结束了，后面合并的任务触发新的更新，需要重新开始
        else if (content && !previousFromText && this.status === WorkflowStatus.SUCCESS) {
            this.currentActiveId = id;
            await this.restart('', forcePreview);
        }
    }

    async endWithId(id: string, content: string, forcePreview?: boolean) {
        await this.replaceFromWithId(id, content, forcePreview);
    }

    calculateAutoScrollLine() {
        return findStartDifferentLine(this.originalContent, this.content);
    }
}
