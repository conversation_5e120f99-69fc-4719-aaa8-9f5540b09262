import _ from 'lodash';
import {
    PatchFileDelimiterError,
    SearchReplacePatchError,
    applySearchReplaceChunk,
} from '@comate/plugin-shared-internals';
import {sleep} from '../utils/index.js';
import {WorkflowStatus} from '../types.js';
import {EditComposerTask} from './EditComposerTask.js';
import {asyncDebounce} from '../../AgentConversation/Zulu/utils/tool.js';

const DEFAULT_ERROR_MESSAGE =
    'this patch_file action is canceled completely. Your request should respect the required format.';

/**
 * 替换代码块，格式如下
 *
 *  <<< SEARCH  <<<
 *  @media (prefers-color-scheme: light) {
 *      color: #000;
 *  ==== REPLACED_BY ====
 *  @media (prefers-color-scheme: dark) {
 *      color: #fff;
 */
export class SearchReplaceComposerTask extends EditComposerTask {
    async immediateUpdate(content: string, closed: boolean = false) {
        if (this.status === WorkflowStatus.CANCELLED) {
            this.applyStreamFinished = true;
            return;
        }

        const {patchedContent, patches, error} = applySearchReplaceChunk(
            this.originalContent || '',
            content,
            {stream: !closed}
        );

        this.generatedContentLines.lines = patchedContent.split(/\r?\n/);
        this.applyStreamFinished = closed;
        // 关闭的那一次更新强制检查解析的逻辑是否正确，这块逻辑是 patchWorkflow 需要
        // 不好拆出去了，先写死在这
        if (closed) {
            this.content = patchedContent;
            // 如果没在原代码里找到
            if (error instanceof SearchReplacePatchError) {
                this.reportPatchFileError('patchFileIndexError', content);
                this.status = WorkflowStatus.CANCELLED;
                throw error;
            }
            else if (error instanceof PatchFileDelimiterError) {
                this.status = WorkflowStatus.CANCELLED;
                await this.revert();
                this.reportPatchFileError('patchFileDelimiterError', content);
                throw error;
            }
            while (!this.finish) {
                await sleep(100);
            }
            this.computeDiffLine();
        }
        else {
            this.computeDiffLineThrottled();
        }
    }

    update = asyncDebounce(
        this.immediateUpdate.bind(this),
        100,
        {leading: true}
    ) as (content: string, closed?: boolean) => Promise<void>;

    reportPatchFileError(kind: string, patchStr: string) {
        this.logger?.logUploader?.logUserAction({
            category: 'zulu',
            action: kind,
            content: patchStr,
            source: this.originalContent || '',
        });
    }

    toAcceptedLabel() {
        return `edit:${this.filePath}`;
    }
}
