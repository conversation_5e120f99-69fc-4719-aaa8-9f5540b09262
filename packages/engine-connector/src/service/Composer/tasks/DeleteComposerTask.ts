import {Task} from './Task.js';
import {isFileExist} from '../../../utils/fs.js';
import {WorkflowStatus} from '../types.js';

export class DeleteComposerTask extends Task {
    actionName = '删除';

    get rollbackAction() {
        return 'willCreate' as const;
    }
    update() {}
    async start() {
        this.originalContent = await this.getDocumentText();
        this.content = '';
        this.autoApproveDeleteTaskInVSCodeAgentMode();
        this.status = WorkflowStatus.SUCCESS;
    }

    async revert() {
        this.reject();
        // 只有文件不存在的时候需要还原
        if (!await isFileExist(this.absolutePath)) {
            await this.createDocument(this.originalContent || '');
            await this.openDocument();
        }
    }

    async rollback() {
        this.reject();
        await this.createDocument(this.originalContent || '');
    }

    async save() {
        this.accept();
        // 关闭tab, 并删除文件
        await this.closeVirtualDocument();
        await this.removeDocument();
    }
    end() {}
}
