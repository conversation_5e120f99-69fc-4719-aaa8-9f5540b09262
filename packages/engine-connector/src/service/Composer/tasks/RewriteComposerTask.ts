import _ from 'lodash';
import {sleep} from '../utils/index.js';
import {WorkflowStatus} from '../types.js';
import {EditComposerTask} from './EditComposerTask.js';
import {asyncDebounce} from '../../AgentConversation/Zulu/utils/tool.js';

export class RewriteComposerTask extends EditComposerTask {
    async immediateUpdate(content: string, closed: boolean = false) {
        if (this.status === WorkflowStatus.CANCELLED) {
            this.applyStreamFinished = true;
            return;
        }

        this.generatedContentLines.lines = content.split(/\r?\n/);
        this.applyStreamFinished = closed;
        if (closed) {
            this.content = content;
            while (!this.finish) {
                await sleep(100);
            }
            this.computeDiffLine();
        }
        else {
            this.computeDiffLineThrottled();
        }
    }

    update = asyncDebounce(
        this.immediateUpdate.bind(this),
        100,
        {leading: true}
    );

    toAcceptedLabel() {
        if (this.originalContent === null) {
            return `create:${this.filePath}`;
        }
        else {
            return `edit:${this.filePath}`;
        }
    }
}
