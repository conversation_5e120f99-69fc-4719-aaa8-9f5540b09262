import {join, isAbsolute, relative, dirname, basename} from 'node:path';
import {mkdir} from 'fs/promises';
import {AcceptState, WorkflowStatus} from '../types.js';
import {VirtualEditor} from '@comate/plugin-shared-internals';
import {LoggerRoleInstance} from '../../Logger.js';
import {formatWin32PathSep, isFilePathEqual} from '../utils/index.js';
import {isJetbrains} from '../../../utils/checkIDE.js';
import {convertChangesToDMP, diffLines} from 'diff';
import {kernel} from '@comate/kernel-shared';
import {Repo} from '../../AgentConversation/Zulu/types/knowledge.js';
export interface Opts {
    /** 是否使用单列的 Diff 视图 */
    inlineView?: boolean;
    acceptPayload?: {conversationId: string, elementId: string};
    /** 用于替换掉绝对路径检索的匹配标识，解决一次composer里有多个同路径任务的场景 */
    uniqKeyPrefix?: string;
}

const getRelativePath = (filePath: string, repo: Repo) => {
    if (filePath.startsWith(repo.rootPath)) {
        return relative(repo.rootPath, filePath);
    }
    return filePath;
};

export type TaskRollbackAction = 'willChange' | 'willDelete' | 'willCreate';

export abstract class Task {
    abstract actionName: string;
    abstract start(content?: string): void;
    abstract update(content: string, closed?: boolean): any;
    abstract end(content: string, forcePreview?: boolean): any;
    abstract save(): any;
    abstract revert(): any;

    foreground: boolean = false;
    accepted: AcceptState = AcceptState.UNTOUCHED;
    status: WorkflowStatus = WorkflowStatus.UNREADY;
    content: string = '';
    originalContent: string | null = null;
    filePath: string = '';
    addLineCount = 0;
    removeLineCount = 0;
    constructor(
        protected virtualEditor: VirtualEditor,
        readonly repo: Repo,
        readonly action: string,
        filePath: string,
        readonly source: string,
        protected readonly opts?: Opts,
        protected readonly logger?: LoggerRoleInstance
    ) {
        // NOTE: UTAgent 这里的 filePath 存在 /$ 开头的情况，具体可以定位到 FILE_NAME_SIGN_$
        this.filePath = getRelativePath(filePath, repo);
    }

    get rollbackAction(): TaskRollbackAction {
        return this.originalContent === null ? 'willDelete' : 'willChange';
    }

    get absolutePath() {
        // 如果是绝对路径直接返回
        // 这里的 startsWith('/') 还不能去掉，单测智能体可能是虚拟路径已 / 开头
        if (this.filePath.startsWith('/') || isAbsolute(this.filePath)) {
            return this.filePath;
        }
        return join(this.repo.rootPath, this.filePath);
    }

    get key() {
        if (this.opts?.uniqKeyPrefix) {
            return `${this.opts.uniqKeyPrefix}/${this.absolutePath}`;
        }
        return this.normalizedAbsolutedPath;
    }

    get finish() {
        return this.status === WorkflowStatus.SUCCESS || this.status === WorkflowStatus.CANCELLED;
    }

    get completed() {
        return this.status === WorkflowStatus.SUCCESS;
    }

    get cancelled() {
        return this.status === WorkflowStatus.CANCELLED;
    }

    get untouched() {
        return this.accepted === AcceptState.UNTOUCHED;
    }

    get calculatedDiff() {
        const temp = convertChangesToDMP(diffLines(this.originalContent || '', this.content || '')) || [];
        const res = temp.filter(item => item[0] !== 0).map(item => {
            if (item[0] === 1) {
                return ('+  ' + item[1]);
            }
            return ('-  ' + item[1]);
        });
        return res.join('');
    }

    protected dependTask: Task | undefined;

    protected uuid: string = '';
    setAdoptUuid(uuid: string) {
        this.uuid = uuid;
    }

    setPath(filePath: string) {
        this.filePath = getRelativePath(filePath, this.repo);
    }

    dependOn(task: Task) {
        this.dependTask = task;
    }

    accept() {
        this.accepted = AcceptState.ACCEPT;
    }

    reject() {
        this.accepted = AcceptState.REJECT;
    }

    cancel() {
        this.status = WorkflowStatus.CANCELLED;
    }

    protected async openDocument() {
        if (!this.foreground) {
            return;
        }
        return this.virtualEditor.openDocument({absolutePath: this.absolutePath});
    }

    protected async getActiveDocument() {
        return this.virtualEditor.getActiveDocument();
    }

    protected async openVirtualDocument(
        leftContent?: string | null,
        rightContent?: string | null,
        source?: 'preview' | 'edit'
    ) {
        if (!this.foreground) {
            return;
        }
        await this.virtualEditor.openVirtualDiffDocument({
            absolutePath: this.normalizedAbsolutedPath,
            content: leftContent || '',
            modified: rightContent || '',
            stream: true,
            inlineView: this.opts?.inlineView,
            source: source || 'preview',
            ...this.opts?.acceptPayload,
        });
    }

    get isPendingDiffView() {
        return this.opts?.inlineView && (kernel.env.ideName === 'jetbrains' || kernel.env.ideName === 'vs');
    }

    protected async openVirtualDocumentNecessary(
        forcePreview?: boolean,
        leftContent?: string | null,
        rightContent?: string | null
    ) {
        // 当前版本，vs和jetbrains当前版本不在输出时打开diff
        if (!this.foreground || this.isPendingDiffView) {
            return;
        }

        if (forcePreview) {
            await this.openVirtualDocument(leftContent, rightContent, 'edit');
        }
        else {
            const document = await this.getActiveDocument();
            if (
                document.scheme === 'diff'
                && isFilePathEqual(document.absolutePath, this.dependTask?.absolutePath || '')
            ) {
                await this.openVirtualDocument(leftContent, rightContent, 'edit');
            }
        }
    }
    protected async replaceVirtualDocument(content: string | null, opts?: {
        stream: boolean;
        scrollToLine?: number;
        preview?: boolean;
    }) {
        if (opts?.preview && kernel.env.ideName !== 'vscode') {
            await this.openVirtualDocument(this.originalContent || '', content || '', 'edit');
        }

        // 当前版本，vs和jetbrains不支持流式
        if (this.isPendingDiffView && opts?.stream) {
            return;
        }

        await this.virtualEditor.replaceVirtualDiffModifiedDocument({
            absolutePath: this.normalizedAbsolutedPath,
            content: content || '',
            stream: !!opts?.stream,
            scrollToLine: opts?.scrollToLine,
            inlineView: !!this.opts?.inlineView,
            ...this.opts?.acceptPayload,
        });
    }

    protected async getDocumentText() {
        const {content, existed} = await this.virtualEditor.getDocument({absolutePath: this.absolutePath});
        return existed ? content : null;
    }

    protected async refreshProjectTree() {
        await this.virtualEditor.refreshProjectTree();
    }

    protected async removeDocument() {
        await this.virtualEditor.deleteDocument({absolutePath: this.absolutePath});
        await this.virtualEditor.closeDocument({absolutePath: this.absolutePath});
    }

    protected eol: string = '\n';
    protected async getDocumentEndOfLine() {
        // const document = await vscode.workspace.openTextDocument(vscode.Uri.file(this.absolutePath));
        // this.eol = document.eol === vscode.EndOfLine.LF ? '\n' : '\r\n';
    }

    async closeVirtualDocument() {
        if (!this.foreground) {
            return;
        }
        await this.virtualEditor.closeVirtualDiffDocument({absolutePath: this.absolutePath});
    }

    protected async closeDocument() {
        if (!this.foreground) {
            return;
        }
        await this.virtualEditor.closeDocument({absolutePath: this.absolutePath});
    }

    protected async createDocument(content: string | null) {
        try {
            // 确保目录存在
            await mkdir(dirname(this.absolutePath), {recursive: true});
            await this.saveDocumentWithReplaceContent(content || '');
        }
        catch (ex) {
            this.logger?.error('Task.createDocument failed', ex);
        }
    }

    /**
     * 总共有4种格式的路径，idea在windows上无法处理第一个种，统一转成第三种
     * 1. c:\Users\<USER>\IdeaProjects\untitled\a.js
     * 2. c:\Users\<USER>\IdeaProjects\untitled/a.js
     * 3. C:/Users/<USER>/IdeaProjects/untitled/a.js
     * 4. /Users/<USER>/IdeaProjects/untitled/a.js
     */
    get normalizedAbsolutedPath() {
        return isJetbrains ? formatWin32PathSep(this.absolutePath) : this.absolutePath;
    }

    protected async saveDocumentWithReplaceContent(content: string) {
        await this.virtualEditor.saveDocumentWithReplaceContent({
            absolutePath: this.normalizedAbsolutedPath,
            content,
            source: this.source,
        });
        await this.refreshProjectTree();
    }

    protected async saveDocumentWithReplaceContentAndOpen(content: string) {
        await this.virtualEditor.saveDocumentWithReplaceContentAndOpen({
            absolutePath: this.normalizedAbsolutedPath,
            content,
            source: this.source,
        });
        await this.refreshProjectTree();
    }

    protected async getVirtualDocumentText() {
        const {content, existed} = await this.virtualEditor.getVirtualDiffDocument({
            absolutePath: this.absolutePath,
        });
        if (existed) {
            return content;
        }
        else {
            return this.content;
        }
    }

    setForeground(foreground: boolean) {
        this.foreground = foreground;
    }

    async openDiff(patch?: Partial<Parameters<typeof this.virtualEditor.openVirtualDiffDocument>[0]>) {
        await this.virtualEditor.openVirtualDiffDocument({
            absolutePath: this.normalizedAbsolutedPath,
            content: typeof patch?.content === 'string' ? patch.content : this.originalContent || '',
            modified: this.content,
            stream: false,
            inlineView: typeof patch?.inlineView === 'boolean' ? patch.inlineView : this.opts?.inlineView,
            source: 'preview',
            ...this.opts?.acceptPayload,
        });
    }

    async rollback() {
        await this.revert();
    }

    protected async autoApproveWriteTaskInVSCodeAgentMode(content: string) {
        this.accept();
        if (kernel.env.ideName === 'vscode' && this.opts?.inlineView) {
            await this.createDocument(content);
        }
    }
    protected async autoApproveDeleteTaskInVSCodeAgentMode() {
        this.accept();
        await this.removeDocument();
    }

    toJSON() {
        return {
            action: this.action,
            accepted: this.accepted,
            status: this.status,
            filePath: this.filePath,
            absolutePath: this.absolutePath,
            basename: basename(this.absolutePath),
            content: this.content,
            addLineCount: this.addLineCount,
            removeLineCount: this.removeLineCount,
        };
    }

    toAcceptedLabel() {
        return `${this.action}:${this.filePath}`;
    }
}
