import {existsSync} from 'node:fs';
import {join, extname, basename} from 'path';
import {Token, Tokens, TokensList, marked} from 'marked';
import {WorkflowStatus, ComposerFileAction} from './types.js';
import {SUFFIX_LANG_MAP, VirtualEditor} from '@comate/plugin-shared-internals';
import {LoggerRoleInstance} from '../Logger.js';
import {CreateComposerTask} from './tasks/CreateComposerTask.js';
import {EditComposerTask} from './tasks/EditComposerTask.js';
import {DeleteComposerTask} from './tasks/DeleteComposerTask.js';
import {ExplainComposerTask} from './tasks/ExplainComposerTask.js';
import {PreviewComposerTask} from './tasks/PreviewComposerTask.js';
import {RewriteComposerTask} from './tasks/RewriteComposerTask.js';
import {ReplaceComposerTask} from './tasks/ReplaceComposerTask.js';
import {SearchReplaceComposerTask} from './tasks/SearchReplaceComposerTask.js';
import {Repo} from '../AgentConversation/Zulu/types/knowledge.js';

export const getComposerCodeMeta = (block: Tokens.Code | Tokens.Generic) => {
    const result = /^(create|edit|rewrite|delete|explain|preview|replaceFrom):(.*)$/.exec(block.lang);
    if (result) {
        return {action: result[1] as ComposerFileAction, filePath: result[2].trim()};
    }
    return null;
};

function isObjectString(obj: string): boolean {
    const result = (() => {
        try {
            return JSON.parse(obj);
        }
        catch {
            return obj;
        }
    })();
    return Object.prototype.toString.call(result) === '[object Object]';
}

const isReplaceToCodeBlock = (lang?: string) => lang && lang.trim().startsWith('replaceTo:');
const isReplaceFromCodeBlock = (lang?: string) => lang && lang.trim().startsWith('replaceFrom:');

export interface Opts {
    /** 是否使用单列的 Diff 视图 */
    inlineView?: boolean;
    acceptPayload?: {conversationId: string, elementId: string};
}

export class Composer {
    constructor(
        readonly virtualEditor: VirtualEditor,
        readonly repo: Repo,
        readonly logger: LoggerRoleInstance,
        readonly source: string,
        readonly opts?: Opts
    ) {}
    foreground: boolean = false;

    private subTasks: Array<
        | CreateComposerTask
        | EditComposerTask
        | RewriteComposerTask
        | DeleteComposerTask
        | ExplainComposerTask
        | PreviewComposerTask
        | ReplaceComposerTask
    > = [];
    private markdown: string = '';
    createTask(virtualEditor: VirtualEditor, repo: Repo, action: 'create', filePath: string): CreateComposerTask;
    createTask(virtualEditor: VirtualEditor, repo: Repo, action: 'edit', filePath: string): EditComposerTask;
    createTask(virtualEditor: VirtualEditor, repo: Repo, action: 'rewrite', filePath: string): RewriteComposerTask;
    createTask(virtualEditor: VirtualEditor, repo: Repo, action: 'delete', filePath: string): DeleteComposerTask;
    createTask(virtualEditor: VirtualEditor, repo: Repo, action: 'explain', filePath: string): ExplainComposerTask;
    createTask(virtualEditor: VirtualEditor, repo: Repo, action: 'preview', filePath: string): PreviewComposerTask;
    createTask(
        virtualEditor: VirtualEditor,
        repo: Repo,
        action: 'searchReplace',
        filePath: string
    ): SearchReplaceComposerTask;
    createTask(virtualEditor: VirtualEditor, repo: Repo, action: ComposerFileAction, filePath: string) {
        switch (action) {
            case 'create':
                const createTask = new CreateComposerTask(
                    virtualEditor,
                    repo,
                    action,
                    filePath,
                    this.source,
                    this.opts,
                    this.logger
                );
                createTask.foreground = this.foreground;
                this.subTasks.push(createTask);
                return createTask;
            case 'edit':
                const editTask = new EditComposerTask(
                    virtualEditor,
                    repo,
                    action,
                    filePath,
                    this.source,
                    this.opts,
                    this.logger
                );
                editTask.foreground = this.foreground;
                this.subTasks.push(editTask);
                return editTask;
            case 'rewrite':
                const writeTask = new RewriteComposerTask(
                    virtualEditor,
                    repo,
                    action,
                    filePath,
                    this.source,
                    this.opts,
                    this.logger
                );
                writeTask.foreground = this.foreground;
                this.subTasks.push(writeTask);
                return writeTask;
            case 'delete':
                const deleteTask = new DeleteComposerTask(
                    virtualEditor,
                    repo,
                    action,
                    filePath,
                    this.source,
                    this.opts,
                    this.logger
                );
                deleteTask.foreground = this.foreground;
                this.subTasks.push(deleteTask);
                return deleteTask;
            case 'explain':
                const explainTask = new ExplainComposerTask(
                    virtualEditor,
                    repo,
                    action,
                    filePath,
                    this.source,
                    this.opts,
                    this.logger
                );
                explainTask.foreground = this.foreground;
                this.subTasks.push(explainTask);
                return explainTask;
            case 'preview':
                const previewTask = new PreviewComposerTask(
                    virtualEditor,
                    repo,
                    action,
                    filePath,
                    this.source,
                    this.opts,
                    this.logger
                );
                previewTask.foreground = this.foreground;
                this.subTasks.push(previewTask);
                return previewTask;
            case 'searchReplace':
                const searchReplaceTask = new SearchReplaceComposerTask(
                    virtualEditor,
                    repo,
                    'edit',
                    filePath,
                    this.source,
                    this.opts,
                    this.logger
                );
                searchReplaceTask.foreground = this.foreground;
                this.subTasks.push(searchReplaceTask);
                return searchReplaceTask;
            case 'replaceFrom':
                const replaceTask = ReplaceComposerTask.create(
                    virtualEditor,
                    repo,
                    action,
                    filePath,
                    this.subTasks,
                    this.source
                );
                replaceTask.foreground = this.foreground;
                this.subTasks.push(replaceTask);
                return replaceTask;
        }
    }

    async update(markdown: string) {
        this.markdown = markdown;
        const composedMarkdown = await this.composeMarkdownWithStatus();
        return composedMarkdown;
    }

    async updatePreview(content: string) {
        const existed = this.subTasks.find(task => task instanceof PreviewComposerTask);
        if (existed) {
            existed.update(content, true);
        }
        else {
            const task = this.createTask(this.virtualEditor, this.repo, 'preview', '');
            task.update(content, true);
        }
    }
    private async composeMarkdownWithStatus(forceClosed?: boolean) {
        if (!this.markdown) {
            return '';
        }

        const parsedMarkdownBlocks = marked.lexer(this.markdown);
        const composerCodeBlock = parsedMarkdownBlocks.filter(block => !!getComposerCodeMeta(block));
        for (const block of parsedMarkdownBlocks) {
            if (block.type === 'code') {
                // 必须等language全输出完，否则拿不到完整的路径
                if (!block.raw.includes('\n')) {
                    continue;
                }
                // 某些情况，比如模型返回一半没有最后的 ``` 就结束了，就会一直轮询，这时候可以强制结束
                const closed = forceClosed === true ? true : /\n```\n?/.test(block.raw);
                if (isReplaceToCodeBlock(block.lang)) {
                    await this.updatePreviousReplaceTask(
                        parsedMarkdownBlocks,
                        composerCodeBlock,
                        block as Tokens.Code,
                        closed
                    );
                    continue;
                }
                const result = getComposerCodeMeta(block);
                if (!result) {
                    continue;
                }

                const {action, filePath} = result;
                const index = composerCodeBlock.indexOf(block);

                const existTask = this.subTasks[index];
                const forcePreviewAsfirstTask = index === 0;
                if (existTask) {
                    switch (existTask.action) {
                        case 'delete': {
                            if (existTask.status === WorkflowStatus.UNREADY) {
                                existTask.start(block.text);
                            }
                            break;
                        }
                        case 'replaceFrom': {
                            const replaceTask = existTask as ReplaceComposerTask;
                            await replaceTask.replaceFrom(block.text, forcePreviewAsfirstTask, forceClosed);
                            break;
                        }
                        case 'create':
                        case 'edit':
                        case 'rewrite': {
                            if (existTask.status === WorkflowStatus.UNREADY) {
                                await existTask.start(block.text, forcePreviewAsfirstTask);
                                if (closed) {
                                    await existTask.update(block.text, closed);
                                }
                            }
                            else {
                                await existTask.update(block.text, closed);
                            }
                            break;
                        }
                        case 'explain': {
                            existTask.start(block.text);
                        }
                    }
                }
                else if (!this.repo.rootPath && action === 'create') {
                    this.createTask(this.virtualEditor, this.repo, 'explain', filePath);
                }
                else {
                    let validFilePath = filePath;
                    // 编辑模式下，如果文件不存在，则改为创建，比如JS转TS修改文件后缀的场景
                    // eslint-disable-next-line no-lonely-if
                    if (action === 'edit' && !existsSync(join(this.repo.rootPath, validFilePath))) {
                        validFilePath = filePath.replace(/^@/, '');
                        if (!existsSync(join(this.repo.rootPath, validFilePath))) {
                            this.createTask(this.virtualEditor, this.repo, 'explain', validFilePath);
                            continue;
                        }
                    }

                    const shouldConvertCreateTask2Rewrite = action === 'create'
                        && existsSync(join(this.repo.rootPath, validFilePath));

                    const task = shouldConvertCreateTask2Rewrite
                        ? this.createTask(this.virtualEditor, this.repo, 'rewrite', validFilePath)
                        : this.createTask(this.virtualEditor, this.repo, action as any, validFilePath);

                    const previousTask = this.subTasks[this.subTasks.length - 2];
                    previousTask && task.dependOn(previousTask);
                    if (closed) {
                        if (task.status === WorkflowStatus.UNREADY) {
                            await task.start(block.text, forcePreviewAsfirstTask);
                        }
                        await task.end(block.text);
                    }
                }
            }
        }
        return parsedMarkdownBlocks
            .map(
                block => {
                    if (block.type === 'code') {
                        const result = getComposerCodeMeta(block);
                        const i = composerCodeBlock.indexOf(block);
                        const composerCodeTask = this.tasks[i];
                        if (result && composerCodeTask) {
                            const {action, status, absolutePath, accepted} = composerCodeTask;
                            const language = [action, status, accepted, encodeURI(absolutePath)].join(':');
                            if (action === 'preview' && isObjectString(block.text)) {
                                return `\`\`\`${language}\n${block.text}\n\`\`\``;
                            }
                            if (action === 'explain') {
                                const ext = extname(absolutePath).replace(/^./, '');
                                const language = SUFFIX_LANG_MAP[ext] || 'plaintext';
                                return `\`\`\`${language}\n${block.text}\n\`\`\``;
                            }
                            // 多个 replace 任务如果路径相同会合并，后面的在界面上不需要展示了
                            if (
                                action === 'replaceFrom'
                                && (composerCodeTask as ReplaceComposerTask).visible === false
                            ) {
                                return '';
                            }
                            // eslint-disable-next-line max-len
                            return `\`\`\`${language}\n${block.text}\n\`\`\``;
                        }
                        // replaceTo 需要与 replaceFrom 搭配使用，显示 replaceFrom 就行，所以构建 markdown 时需要过滤掉 replaceTo
                        if (isReplaceToCodeBlock(block.lang)) {
                            return '';
                        }
                    }
                    return block.raw;
                }
            )
            .join('\n');
    }

    /**
     * 根据 replaceTo 更新前面一个 replaceFrom 任务
     * 这块逻辑非常非常非常恶心
     *
     * @param parsedMarkdownBlocks 解析后的 Markdown 块列表
     * @param composerCodeBlock Composer 代码块列表
     * @param currentBlock 当前代码块
     * @param closed 是否已结束
     */
    private async updatePreviousReplaceTask(
        parsedMarkdownBlocks: TokensList,
        composerCodeBlock: Token[],
        currentBlock: Tokens.Code,
        closed: boolean
    ) {
        // parsedMarkdownBlocks 是包含所有 block 的列表
        // composerCodeBlock 是去掉了 replaceTo 的列表
        // 现在要找 replaceTo 之前的一个 replaceFrom，只能先找到前面所有的 parsedMarkdownBlocks
        // 然后一个一个找是不是 replaceFrom 找到了再用这个 block 去找对应的 task
        const currentBlockIndex = parsedMarkdownBlocks.indexOf(currentBlock);
        let previousReplaceFromBlock = null;
        for (let i = currentBlockIndex - 1; i >= 0; i--) {
            const block = parsedMarkdownBlocks[i];
            if (block.type !== 'code') {
                continue;
            }
            if (isReplaceToCodeBlock(block.lang)) {
                break;
            }
            if (isReplaceFromCodeBlock(block.lang)) {
                previousReplaceFromBlock = block;
                break;
            }
        }
        if (!previousReplaceFromBlock) {
            return;
        }
        const taskIndex = composerCodeBlock.indexOf(previousReplaceFromBlock);
        const task = this.subTasks[taskIndex] as ReplaceComposerTask;
        if (!task || task.finish) {
            return;
        }
        await task.replaceTo(currentBlock.text, closed);
    }

    async toMarkdown() {
        const markdown = await this.composeMarkdownWithStatus();
        const previewTask = this.tasks.find((task): task is PreviewComposerTask => task instanceof PreviewComposerTask);
        if (previewTask) {
            const {action, status, absolutePath, accepted} = previewTask;
            const language = [action, status, accepted, absolutePath].join(':');
            // Fix typo: changed previewTask.updatedConetnt to previewTask.updatedContent
            return `${markdown}\n\n\`\`\`${language}\n${previewTask.updatedContent}\n\`\`\``;
        }
        return markdown;
    }

    createTaskFromSnapshot(snapshot: {markdown: string, tasks: any[]}) {
        this.subTasks = snapshot.tasks.map(snapshot => {
            const task = this.createTask(this.virtualEditor, this.repo, snapshot.action, snapshot.filePath);
            task.status = snapshot.status === WorkflowStatus.SUCCESS ? snapshot.status : WorkflowStatus.CANCELLED;
            task.accepted = snapshot.accepted;
            task.content = snapshot.content;
            task.addLineCount = snapshot.addLineCount || 0;
            task.removeLineCount = snapshot.removeLineCount || 0;
            task.originalContent = snapshot.originalContent;
            return task;
        });
        this.markdown = snapshot.markdown;
        return this;
    }

    toJSON() {
        const tasks = this
            .subTasks
            .filter(task => task.action !== 'replaceFrom' || (task as ReplaceComposerTask).visible === true)
            .map(task => ({
                action: task.action,
                accepted: task.accepted,
                status: task.status,
                filePath: task.filePath,
                absolutePath: task.absolutePath,
                basename: basename(task.filePath),
                originalContent: task.originalContent,
                content: task.content,
                addLineCount: task.addLineCount,
                removeLineCount: task.removeLineCount,
            }));
        return {
            markdown: this.markdown,
            tasks,
        };
    }

    /**
     * 确保任务都强制结束并返回最终的 Markdown 字符串。
     *
     * @returns 返回一个 Markdown 格式的字符串。
     */
    safeToMarkdown() {
        return this.composeMarkdownWithStatus(true);
    }

    setForeground(foreground: boolean) {
        this.foreground = foreground;
        for (const task of this.tasks) {
            task.setForeground(foreground);
        }
    }

    async saveAll() {
        for await (const task of this.tasks) {
            await task.save().catch(() => ({}));
        }
    }

    async rejectAll() {
        for await (const task of this.tasks) {
            await task.revert().catch(() => ({}));
        }
    }

    get tasks() {
        return this.subTasks;
    }
}
