// import fs from 'node:fs/promises';
// import path from 'path';
import crypto from 'crypto';
import {LoggerRoleInstance} from './Logger.js';
import {Mediator} from './Mediator.js';
import {
    SystemInfoParsed,
    SADiagnosticScanInvokePayload,
    ACTION_SA_SCAN_DIAGNOSTIC,
    SAScanDiagnosticResult,
} from '@comate/plugin-shared-internals';

export class SADiagnostic {
    private readonly logger: LoggerRoleInstance;
    private readonly mediator: Mediator;
    private pluginName: string = 'devaux';
    // TODO: @zhaomingming04 补充sa扫描的能力名称
    private capabilityName: string = '';
    private systemInfo: SystemInfoParsed | undefined;
    constructor(mediator: Mediator, logger: LoggerRoleInstance) {
        this.logger = logger;
        this.mediator = mediator;
    }

    async init(systemInfo: SystemInfoParsed){
        try {
            this.systemInfo = systemInfo;
            const sessionId = crypto.randomUUID();
            let retryCount = 0;

            while (retryCount < 3) {
                for await (
                    const message of this.mediator.sendToPlugin(
                        sessionId,
                        {
                            action: ACTION_SA_SCAN_DIAGNOSTIC,
                            payload: {
                                pluginName: this.pluginName,
                                capabilityName: this.capabilityName,
                                systemInfo,
                                scanType: 'init',
                                diagnosticScanId: Date.now().toString(),
                            } as SADiagnosticScanInvokePayload,
                        }
                    )
                ) {
                    const chunk = message?.data.payload.chunk as unknown as boolean;
                    if (chunk) {
                        return true;
                    }
                    retryCount++;
                    break;
                }
            }
            return false;
        }
        catch (error) {
            this.logger.error('获取SA扫描结果异常', (error as Error).message);
            return false;
        }
    }

    async getSADiagnosticScanResult(systemInfo: SystemInfoParsed, absolutePath: string) {
        try {
            const sessionId = crypto.randomUUID();
            for await (
                const message of this.mediator.sendToPlugin(
                    sessionId,
                    {
                        action: ACTION_SA_SCAN_DIAGNOSTIC,
                        payload: {
                            pluginName: this.pluginName,
                            capabilityName: this.capabilityName,
                            systemInfo,
                            scanType: 'getResult',
                            absolutePath,
                            diagnosticScanId: Date.now().toString(),
                        } as SADiagnosticScanInvokePayload,
                    }
                )
            ) {
                // 只需要处理返回的第一个message
                return message?.data.payload.chunk as unknown as SAScanDiagnosticResult;
            }
        }
        catch (error) {
            this.logger.error('获取SA扫描结果异常', (error as Error).message);
            return;
        }
    }

    dispose() {
        const sessionId = crypto.randomUUID();
        this.mediator.launchSendToPlugin(
            sessionId,
            {
                action: ACTION_SA_SCAN_DIAGNOSTIC,
                    payload: {
                        pluginName: this.pluginName,
                        capabilityName: this.capabilityName,
                        systemInfo: this.systemInfo,
                        scanType: 'destroy',
                        diagnosticScanId: Date.now().toString(),
                    } as SADiagnosticScanInvokePayload,
            }
        );
    }
}