import {
    Canceled<PERSON><PERSON>r,
    SSEProcessor,
    applyDiff,
    axiosInstance,
    createAxiosCancelTokenSource,
} from '@comate/plugin-shared-internals';
import {kernel} from '@comate/kernel-shared';
import {LoggerRoleInstance} from '../Logger.js';
import {detectLineEnding} from '../../utils/fs.js';

interface Params {
    /** 变更后的代码内容 */
    change: string;
    /** 采纳的文件信息 */
    file: {
        /** 路径 */
        path: string;
        /** 文件名 */
        name: string;
        /** 原文内容 */
        content: string;
        /**  */
        repo: string;
    };
    /** 用户名，saas是license */
    username: string;
    /** 编辑器 */
    ide: string;
    /** 光标所在行 */
    cursorLine: number;
    /** 光标上下5行 */
    contextContent: string;
    /** vscode 版本 */
    vscodeVersion: string;
}

export async function* apiStreamPostSmartApply(logger: LoggerRoleInstance, {username, ...params}: Params) {
    const eol = detectLineEnding(params.file.content);
    const cancelToken = createAxiosCancelTokenSource();
    const rewrite = params.file.content.split(/\r?\n/).length < 20;
    let traceId: string = '';
    const timeout = setTimeout(() => cancelToken.cancel(), 60 * 1000);
    try {
        const res = await axiosInstance.post('/api/aidevops/autocomate/rest/v1/shortcut/auto/apply/stream', {
            ...params,
            ide: kernel.env.ideName,
            rewrite,
        }, {
            headers: {
                'X-Source': 'COMATE',
                'Content-Type': 'application/json',
                'login-name': username,
                'Uuap-login-name': username,
            },
            cancelToken: cancelToken.token,
            responseType: 'stream',
        });
        const processor = new SSEProcessor<{content: string | null, message: string | null, code: number}>(res.data);
        let text = '';
        let previousEOLIndex = 0;
        for await (const chunk of processor.processSSE()) {
            if (chunk.code === 401) {
                throw new Error('当前License无效，您可在官网查看License状态');
            }
            else if (chunk.message) {
                throw new Error(chunk.message);
            }
            text = text + chunk.content || '';
            let eolIndex = text.lastIndexOf(eol);
            // 出了至少一行后再输出
            if (eolIndex === -1) {
                continue;
            }
            text = text.replace(/```(\w+)?\n/, '').replace(/\n```(\n?)$/, '');
            eolIndex = text.lastIndexOf('\n');
            // 保证每次yield的diff都是完整的一行
            if (eolIndex > previousEOLIndex) {
                previousEOLIndex = eolIndex;
                if (rewrite) {
                    yield {text};
                }
                else {
                    const diffText = text.slice(0, eolIndex + 1);
                    yield {
                        text: applyDiff(
                            params.file.content,
                            diffText,
                            {
                                returnContentWhenDiffLineLoopFinish: true,
                                eol,
                            }
                        ),
                        finish: false,
                    };
                }
            }
        }

        traceId = res.headers['x-trace-id'];
        if (rewrite) {
            yield {text, diff: text, traceId, finish: true};
        }
        else {
            yield {
                text: applyDiff(
                    params.file.content,
                    text,
                    {
                        returnContentWhenDiffLineLoopFinish: false,
                        eol,
                    }
                ),
                diff: text,
                traceId,
                finish: true,
            };
        }
    }
    catch (ex) {
        if (ex instanceof CanceledError) {
            yield {text: '', traceId, error: '超时'};
        }
        else {
            const message = (ex as Error).message;
            yield {text: '', traceId, error: message};
            logger.error('apiStreamPostSmartApply:', message);
        }
    }
    finally {
        clearTimeout(timeout);
    }
}
