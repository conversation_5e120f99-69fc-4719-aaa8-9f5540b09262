import _ from 'lodash';
import {
    AgentConversationStatus,
    AgentConversationType,
    axiosInstance,
} from '@comate/plugin-shared-internals';
import {
    ChatParams,
} from '../Composer/api.js';
import {Roundtrip} from '../Roundtrip/index.js';

import {McpManager} from '../McpManager/index.js';
import {ConversationContext} from './AbstractConversation.js';
import {Knowledge} from './Zulu/types/knowledge.js';
import {E2EBotConversation} from './E2EBotConversation.js';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import {ToolCallMessage} from '../Roundtrip/Message.js';
import {Model} from '../../api/generate.js';

interface ComposerFileAcceptRecord {
    fileOperate: {
        acceptedFiles: string[];
        rejectedFiles: string[];
    };
}

export interface Payload {
    query: string;
    knowledgeList: Knowledge[];
    figmaInfo: {
        figmaUrl: string;
        figmaToken: string;
        strategy: string;
        name: string;
        nodeId: string;
    };
    conversationId?: number;
    extend: ComposerFileAcceptRecord;
    origin?: 'USER' | 'DEBUG';
    // 会话 ID ，Zulu拿Debug历史时用
    originConversationId?: string;
}

export class F2cBotConversation extends E2EBotConversation {
    model: Model = 'F2C_AGENT';
    type: AgentConversationType = AgentConversationType.F2cBotConversation;
    inlineDiffView: boolean = true;
    status = AgentConversationStatus.Ready;
    figmaInfo = {
        figmaUrl: '',
        figmaToken: '',
        strategy: '',
        name: '',
        nodeId: '',
    };
    constructor(context: ConversationContext, mcpManager: McpManager) {
        super(context, mcpManager);
    }

    private replaceVariable(template: string, variables: Record<string, any>): string {
        let result = template;
        for (const [key, value] of Object.entries(variables)) {
            const regex = new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, 'g');
            result = result.replace(regex, value);
        }
        return result;
    }

    async startWork(roundtrip: Roundtrip<Payload>): Promise<void> {
        this.updateStatus(AgentConversationStatus.Running);
        this.figmaInfo = roundtrip.request.payload.figmaInfo;
        const params = await this.injectExtendParamsIfPreviousMessageExisted(roundtrip.request.payload);
        if (this.roundtrips.length > 4) {
            this.model = 'FULL_STACK_AGENT';
        }
        await this.createConversation(roundtrip, params);
    }
    protected async startChat(roundtrip: Roundtrip<Payload>, chatParams: ChatParams) {
        if (this.roundtrips.length !== 1) {
            await this.recursivelyRegenerate(roundtrip, chatParams, []);
            return;
        }
        const fileKey = this.figmaInfo.nodeId.split('-')[0];
        const node = this.figmaInfo.nodeId.split('-').slice(1).join('-');
        await roundtrip.appendTextResponse(
            '根据用户请求，我对该Figma文件进行分析并生成对应前端代码并在还原设计稿时我将重点保证结构正确：'
        );
        await roundtrip.updateWebviewMessages(true);
        await new Promise(resolve => setTimeout(resolve, 1000));
        await await roundtrip.appendToolCallResponse({
            eventType: 'TOOL_CALL_CREATE',
            name: 'f2c_tool',
            params: {
                toolName: '请求Figma',
                url: this.figmaInfo.figmaUrl,
                content: `design: ${fileKey}`,
            },
        });
        await roundtrip.appendToolCallResponse({
            eventType: 'TOOL_CALL_CREATE',
            name: 'f2c_tool',
            params: {
                toolName: '元素分析',
                url: this.figmaInfo.figmaUrl,
                figmaToken: this.figmaInfo.figmaToken,
                strategy: this.figmaInfo.strategy,
                content: `node: ${node}`,
                nodeId: this.figmaInfo.nodeId,
            },
        });
        await roundtrip.updateWebviewMessages(true);
        const lastMessage = await roundtrip.getLatestMessage() as ToolCallMessage;
        const res = await lastMessage.startWorkflow().workflowPromise;
        const html = res?.result;
        if (html) {
            try {
                // 创建临时文件路径
                const tempDir = os.tmpdir();
                const tempFilePath = path.join(
                    tempDir,
                    `${this.figmaInfo.nodeId}.html`
                );
                // 写入 HTML 内容
                fs.writeFileSync(tempFilePath, html, 'utf8');
                // 生成 file:// URL
                const fileUrl = `file://${tempFilePath}`;
                // 发送预览 URL 到 VSCode
                await roundtrip.appendTextResponse('解析成功，您可以进行预览：');
                await roundtrip.updateWebviewMessages(true);
                await roundtrip.appendToolCallResponse({
                    eventType: 'TOOL_CALL_CREATE',
                    name: 'f2c_tool',
                    params: {url: fileUrl, content: `file://${this.figmaInfo.nodeId}.html`, toolName: '预览'},
                });
                roundtrip.updateWebviewMessages(true);
            }
            catch (error) {
                throw new Error('解析figma设计稿失败。');
            }
        }
        const rulePath = path.join(this.repo.rootPath, '.comate-f2c/f2c-rules.md');
        const rule: string = fs.existsSync(rulePath)
            ? fs.readFileSync(rulePath, 'utf-8')
            : (await axiosInstance.get('/api/figma/defaultRule')).data.content;
        const remoteQueryTemplate = (await axiosInstance.get('/api/figma/defaultQueryTemplate')).data.content;
        const realQuery = this.replaceVariable(remoteQueryTemplate, {
            rule,
            html,
        });
        await this.recursivelyRegenerate(roundtrip, {
            ...chatParams,
            query: realQuery,
        }, []);
        return;
    }
}
