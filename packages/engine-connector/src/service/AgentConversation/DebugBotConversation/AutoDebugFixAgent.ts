import _ from 'lodash';
import {
    AGENT_DEBUG_CUSTOM_ACTION,
    DebugAgentPayload,
    DebugAgentResponse,
    SSEProcessor,
} from '@comate/plugin-shared-internals';
import {
    analyze,
    AnalyzeData,
    autoDebugFix,
    AutoDebugFixResponse,
    CodeContext,
} from '../../../api/debug.js';
import {UserDetailWithId} from '../../User.js';
import {IdeEventAction} from './types.js';
import {buildDisplayCodeItem, getQuery, parseMarkdownToBlocks} from './utils.js';

const {isEmpty} = _;

export class AutoDebugFixAgent {
    // 接口分析结果
    analyzeResult: AnalyzeData | undefined = undefined;
    // 本地代码检索结果
    private codeContext: CodeContext | undefined = undefined;
    private readonly sendIdeAction: (name: IdeEventAction, data?: any) => Promise<any>;

    constructor(
        private readonly conversationId: string,
        private readonly username: string,
        private readonly payload: DebugAgentPayload | undefined,
        private readonly userDetail: UserDetailWithId,
        onMessageChange: (name: string, data: any) => Promise<any>
    ) {
        this.sendIdeAction = (action: IdeEventAction, data?: any) => {
            return onMessageChange(AGENT_DEBUG_CUSTOM_ACTION, {action, data});
        };
    }

    get query() {
        const {code, customPrompt} = this.payload ?? {};
        return customPrompt ? customPrompt : getQuery(code);
    }

    /**
     * 根据 payload 进行分析并生成代码，没有状态也不会对结果进行处理
     * @yields 返回生成的代码对象，包含：错误原因、检索到的代码片段、模型返回的 markdown 内容
     */
    async *generateCode() {
        const result: DebugAgentResponse = {};
        try {
            await this.analyzePayload();
            result.errorReason = this.analyzeResult?.errorReason;
            yield {result, end: false};

            await this.searchCode();
            result.contexts = this.codeContext ? buildDisplayCodeItem(this.codeContext) : [];
            yield {result, end: false};

            for await (const content of this.generateContent()) {
                result.content = content;
                result.blocks = parseMarkdownToBlocks(content);
                yield {result, end: false};
            }
            yield {result, end: true};
        }
        catch (ex) {
            const errorMsg = ex instanceof Error ? ex.message : '生成失败';
            result.content = errorMsg;
            yield {result, end: true};
        }
    }

    /**
     * 请求远端分析问题，并更新状态到 analyzing
     */
    private async analyzePayload() {
        const {platform, cwd, contexts} = this.payload ?? {};
        const analyzeResult = await analyze({
            userDetail: {...this.userDetail, username: this.username},
            conversationId: this.conversationId,
            query: this.query,
            cwd,
            platform,
            contexts: typeof contexts === 'object' ? JSON.stringify(contexts) : contexts,
        });
        this.analyzeResult = analyzeResult;
    }

    /**
     * 根据分析结果请求 IDE 进行代码检索，并更新状态到 fixing
     */
    private async searchCode() {
        const codeContext = this.analyzeResult && !isEmpty(this.analyzeResult.context)
            ? await this.sendIdeAction('search', this.analyzeResult.context) as CodeContext
            : {};
        this.codeContext = codeContext;
    }

    /** 根据分析结果和检索的代码生成结果 */
    private async *generateContent() {
        const chatStreamResponse = await autoDebugFix({
            userDetail: {...this.userDetail, username: this.username},
            query: this.query,
            context: this.codeContext,
            device: this.userDetail.device,
            recordId: this.analyzeResult?.recordId,
            conversationId: this.conversationId,
            queryType: this.analyzeResult?.queryType,
        });
        const processor = new SSEProcessor<AutoDebugFixResponse>(chatStreamResponse);
        let fullText = '';
        for await (const chunk of processor.processSSE()) {
            const {data, code, message} = chunk;
            if (code !== 200) {
                throw new Error(message);
            }
            if (!data) {
                continue;
            }
            if (data.isEnd) {
                return fullText;
            }
            fullText += data.result;
            yield fullText;
        }
    }
}
