import crypto from 'node:crypto';
import _ from 'lodash';
import {
    SSEProcessor,
    DebugAgentPayload,
    AGENT_DEBUG_CUSTOM_ACTION,
    AgentConversationType,
} from '@comate/plugin-shared-internals';
import {
    AnalyzeData,
    CodeContext,
    agenticAnalyze,
    AgenticFixParams,
    agenticAutoDebugFix,
    AutoDebugFixResponse,
    codeAdopt,
    codeGenerate,
    taskStatus,
} from '../../../api/debug.js';
import {throttleAsyncIterator} from '../../../utils/throttleAsyncIterator.js';
import {Model} from '../../../api/generate.js';
import {WorkflowResult, WorkflowType} from '../../Roundtrip/Workflow/WorkflowBase.js';
import {listFiles} from '../../Roundtrip/Workflow/ListFilesWorkflow.js';
import {Roundtrip, RoundtripStatus} from '../../Roundtrip/index.js';
import {ToolCallMessage} from '../../Roundtrip/Message.js';
import {E2EBotConversation, Payload as E2EBotPayload} from '../E2EBotConversation.js';
import {
    IdeEventData,
    IdeEventAction,
} from './types.js';
import {getBaseSysInfo} from '../../Roundtrip/utils/os.js';
import {MultiAcceptData} from '../../Track/api.js';
import {McpManager} from '../../McpManager/index.js';
import {ConversationContext} from '../AbstractConversation.js';

const {isEmpty} = _;

const getQuery = (payload: DebugAgentPayload) =>
    payload.customPrompt
        ? payload.customPrompt
        : `请帮我分析终端的报错日志，并提供解决方案。只解决第一个报错\n${payload.code ?? ''}`;

const editFileWorkflowType = [
    WorkflowType.WriteFile,
    WorkflowType.DeleteFile,
    WorkflowType.PatchFile,
];

interface DebugCommandResult {
    status: 'success' | 'error' | 'failed' | 'timeout' | 'unknown' | 'killed' | 'aborted';
    matchError?: string;
}

function getValidationResultMessage(status: DebugCommandResult['status'], matchError?: string) {
    const errorText = matchError ? `\n\n\`\`\`text\n${matchError}\n\`\`\`\n` : '';
    switch (status) {
        case 'aborted':
            return '已跳过验证，任务结束';
        case 'killed':
            return '🚫 任务已终止，验证失败 (；一_一)';
        case 'timeout':
            return '⏰ 运行超时，验证失败 (｡•́︿•̀｡)';
        case 'success':
            return '✨ 运行成功，修复完成 (ﾉ*>∀<)ﾉ♡';
        case 'error':
            return '🚨 运行结束，但未监测到错误，验证失败 T_T';
        case 'failed':
            return `🛠️ 运行失败，发现新错误：${errorText}`;
        default:
            return '❓ 运行结束，无法获取运行结果，验证失败 (｡•́︿•̀｡)';
    }
}

export class DebugBotConversation extends E2EBotConversation {
    model: Model = 'DEBUG_AGENT';
    type: AgentConversationType = AgentConversationType.DebugBotConversation;
    private sendCustomEventToIde: (name: string, data: IdeEventData) => Promise<any>;

    constructor(
        context: ConversationContext,
        mcpManager: McpManager,
        sendCustomEventToIde: (name: string, data: IdeEventData) => Promise<any>
    ) {
        super(context, mcpManager);
        this.sendCustomEventToIde = sendCustomEventToIde;
        this.model = 'DEBUG_AGENT';
    }

    async startWork(roundtrip: Roundtrip<DebugAgentPayload>) {
        // 第一次固定走 debug 逻辑
        if (this.roundtrips.length === 1) {
            await this.startDebugConversation(roundtrip);
        }
        // 第二次是会话需要适配zulu历史
        else if (this.roundtrips.length === 2) {
            this.model = 'FULL_STACK_AGENT';
            const params = await this.injectExtendParamsIfPreviousMessageExisted(
                roundtrip.request.payload as E2EBotPayload
            );
            params.origin = 'DEBUG';
            params.originConversationId = this.id;
            await this.createConversation(roundtrip as Roundtrip<E2EBotPayload>, params);
        }
        // 后面的会话都走zulu逻辑
        else {
            this.model = 'FULL_STACK_AGENT';
            const params = await this.injectExtendParamsIfPreviousMessageExisted(
                roundtrip.request.payload as E2EBotPayload
            );
            params.originConversationId = this.id;
            await this.createConversation(roundtrip as Roundtrip<E2EBotPayload>, params);
        }
    }

    protected async afterStop() {
        if (this.latestRoundtrip.reportedId) {
            this.reportConversationStatusChange('ABORT');
        }
    }

    private async startDebugConversation(roundtrip: Roundtrip<DebugAgentPayload>) {
        const payload = roundtrip.request.payload;
        // 1. 分析
        const analyzeResult = await this.analyze(roundtrip.uuid, payload);
        roundtrip.appendTextResponse(analyzeResult?.errorReason ?? '');

        // 2. 检索本地代码
        const codeContext = await this.searchCode(analyzeResult);

        // 3. 构建参数
        const params = await this.buildParams(
            // 目前只有单轮可以直接用这个，多轮点全部采纳时区分不了是哪一轮的taskId，后面需要改
            roundtrip.uuid,
            payload,
            analyzeResult,
            codeContext
        );

        roundtrip.status = RoundtripStatus.Generating;

        // 4. 执行修复
        // 一期先不加多轮自动修复
        // await this.recursivelyFix(roundtrip, params);
        await this.recursivelyChat(roundtrip, params, []);
    }

    /**
     * 请求远端分析问题
     */
    private async analyze(taskId: string, payload: DebugAgentPayload) {
        const {platform, cwd, contexts} = payload;
        const analyzeResult = await agenticAnalyze({
            userDetail: {...this.userDetail, username: this.repo.username},
            conversationId: this.id,
            taskId,
            query: getQuery(payload),
            cwd,
            platform,
            contexts: typeof contexts === 'object' ? JSON.stringify(contexts) : contexts,
        });
        return analyzeResult;
    }

    /**
     * 根据分析结果请求 IDE 进行代码检索
     */
    private async searchCode(analyzeResult?: AnalyzeData) {
        const codeContext = analyzeResult && !isEmpty(analyzeResult.context)
            ? await this.sendIdeAction('search', analyzeResult.context) as CodeContext
            : {};
        return codeContext;
    }

    private async buildParams(
        taskId: string,
        payload: DebugAgentPayload,
        analyzeResult?: AnalyzeData,
        codeContext?: CodeContext
    ) {
        const [files] = await listFiles(this.repo.rootPath, this.repo.rootPath, true, 200);
        const sysInfo = {
            ...getBaseSysInfo(),
            workspaceFolder: this.repo.rootPath,
            workspaceFolderTrees: files.map(file => file.type === 'folder' ? `${file.path}/` : file.path),
        };
        const params: AgenticFixParams = {
            userDetail: {...this.userDetail, username: this.repo.username},
            query: getQuery(payload),
            taskId,
            // @ts-expect-error
            context: {...codeContext, sysInfo},
            device: this.userDetail.device,
            recordId: analyzeResult?.recordId,
            conversationId: this.id,
            queryType: analyzeResult?.queryType,
            toolUseResults: [],
            type: 'NORMAL',
        };
        return params;
    }

    async recursivelyFix(roundtrip: Roundtrip<DebugAgentPayload>, params: AgenticFixParams) {
        const toolCallMessageLength = roundtrip.toolCallMessages.length;
        await this.recursivelyChat(roundtrip, params, []);
        const newAddedToolCallMessages = roundtrip.toolCallMessages.slice(toolCallMessageLength);
        const result = await this.runValidationCommand(roundtrip, newAddedToolCallMessages);
        if (!result) {
            return;
        }
        roundtrip.appendTextResponse(result.message);
        if (result.status === 'failed') {
            const newPayload = {
                ...roundtrip.request.payload,
                code: `\`\`\`text\n${result.matchError ?? ''}\n\`\`\`\n`,
            };
            const newParams = {
                ...params,
                taskId: crypto.randomUUID(),
                query: getQuery(newPayload),
                agenticSolutionType: 'FAILRETRY',
            };
            await this.recursivelyFix(roundtrip, newParams);
        }
    }

    private async recursivelyChat(
        roundtrip: Roundtrip<DebugAgentPayload>,
        params: AgenticFixParams,
        workflowResults: WorkflowResult[]
    ) {
        const chatStreamResponse = await agenticAutoDebugFix({...params, toolUseResults: workflowResults});
        const processor = new SSEProcessor<AutoDebugFixResponse>(chatStreamResponse);

        let requestId: string = '';

        const chatStream = processor.processSSE();
        for await (const chunks of throttleAsyncIterator(chatStream, 200)) {
            for (const chunk of chunks) {
                if (!requestId && chunk.requestId) {
                    requestId = chunk.requestId;
                }
                const {data, code, message} = chunk;
                if (code !== 200) {
                    throw new Error(message);
                }
                if (!data) {
                    continue;
                }
                if (data.content || data.result) {
                    roundtrip.appendTextResponse(data.content || data.result);
                }
                if (Array.isArray(data.toolUse)) {
                    for (const toolUse of data.toolUse) {
                        await roundtrip.appendToolCallResponse(toolUse);
                    }
                }

                if (data.isEnd) {
                    break;
                }
            }
            roundtrip.updateWebviewMessages();
        }

        const newWorkflowResults = await roundtrip.resolveNewAddedMessages();
        if (newWorkflowResults.length > 0) {
            const newParams = {...params, query: '', context: undefined, type: 'NORMAL'} as const;
            await this.recursivelyChat(roundtrip, newParams, newWorkflowResults);
        }
    }

    private async runValidationCommand(
        roundtrip: Roundtrip<DebugAgentPayload>,
        newAddedToolCallMessages: Array<ToolCallMessage>
    ) {
        const payload = roundtrip.request.payload;
        if (payload?.needsValidation === false) {
            return;
        }
        const hasEditToolCall = newAddedToolCallMessages
            .some(message => editFileWorkflowType.includes(message.workflow.type));
        if (!hasEditToolCall) {
            return;
        }

        const shellCommand = payload.command;
        if (shellCommand && shellCommand.commandLine) {
            return this.runDebugCommand(roundtrip, shellCommand.commandLine, shellCommand.pwd);
        }

        await this.runIDECommand(roundtrip);
    }

    private async runDebugCommand(roundtrip: Roundtrip<DebugAgentPayload>, shellCommand: string, pwd?: string) {
        roundtrip.appendTextResponse(`我将使用已「采纳」的文件，尝试重新运行 \`${shellCommand}\``);
        await roundtrip.appendToolCallResponse({
            eventType: 'TOOL_CALL_CREATE',
            name: WorkflowType.RunDebugCommand,
            params: {
                command: shellCommand,
                path: pwd ?? this.repo.rootPath,
            },
        });
        roundtrip.updateWebviewMessages();
        const newWorkflowResults = await roundtrip.resolveNewAddedMessages();
        return this.resolveValidationCommandResult(newWorkflowResults[0]);
    }

    private async runIDECommand(roundtrip: Roundtrip<DebugAgentPayload>) {
        const commandName = await this.sendIdeAction('commandName');
        if (!commandName) {
            return;
        }
        roundtrip.appendTextResponse(`我将使用已「采纳」的文件，尝试重新运行 \`${commandName}\``);
        // TODO: 执行命令
        return this.resolveValidationCommandResult();
    }

    private resolveValidationCommandResult(workflowResult?: WorkflowResult) {
        if (!workflowResult || !workflowResult.result) {
            const msg = workflowResult?.message ?? '运行结束，无法获取运行结果，验证失败 (｡•́︿•̀｡)';
            return {status: 'error', message: msg};
        }
        const {status, matchError} = workflowResult.result as DebugCommandResult;
        const message = getValidationResultMessage(status, matchError);
        return {
            status,
            message,
            matchError,
        };
    }

    private sendIdeAction = (action: IdeEventAction, data?: any) => {
        return this.sendCustomEventToIde(AGENT_DEBUG_CUSTOM_ACTION, {action, data});
    };

    protected onAcceptLog(acceptData: MultiAcceptData[], from?: 'action' | 'end'): void {
        if (!this.latestRoundtrip || !this.latestRoundtrip.reportedId) {
            return;
        }

        if (from === 'end') {
            this.onCodeGenerated(this.latestRoundtrip.reportedId, acceptData);
        }
        else if (from === 'action') {
            this.onCodeAccepted(acceptData);
        }
    }

    // AutoDebug 的采纳上报
    private onCodeAccepted(multiSuggestions: MultiAcceptData[]) {
        const adoptedCode = multiSuggestions
            .filter(v => v.accepted === true)
            .map(v => v.generatedContent)
            .join('\n');
        codeAdopt({
            userDetail: {...this.userDetail, username: this.repo.username},
            conversationId: this.id,
            adoptedCode,
        });
    }

    private onCodeGenerated(uuid: string, multiSuggestions: MultiAcceptData[]) {
        const generatedCode = multiSuggestions.map(v => v.generatedContent).join('\n');
        codeGenerate({
            userDetail: {...this.userDetail, username: this.repo.username},
            uuid,
            generatedCode,
            conversationId: this.id,
        });
    }

    private reportConversationStatusChange(status: 'ABORT') {
        taskStatus({
            userDetail: {...this.userDetail, username: this.repo.username},
            conversationId: this.id,
            status,
        });
    }
}
