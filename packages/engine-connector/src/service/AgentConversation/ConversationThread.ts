import _, {round, slice} from 'lodash';
import {
    AgentAssistantMessage,
    AgentConversationStatus,
    AgentMessage,
    AgentPayload,
    CanceledError,
    RollbackMessageFileInfo,
} from '@comate/plugin-shared-internals';
import {Roundtrip} from '../Roundtrip/index.js';
import {UserMessagePayload} from '../Roundtrip/types.js';
import {AbstractConversation} from './AbstractConversation.js';
import {ToolCallMessage} from '../Roundtrip/Message.js';
import {AcceptState} from '../Composer/types.js';
import {Task} from '../Composer/tasks/Task.js';
import {apiRollbackZuluMessage} from '../Composer/api.js';
import {contentEqualRough} from './Zulu/utils/equal.js';

const {isEqual, omit} = _;

export type RollbackFileSummary = Array<RollbackMessageFileInfo & {changes: Task[]}>;

export abstract class ConversationThread<MessageOperationPayload = any>
    extends AbstractConversation<MessageOperationPayload> {
    foreground: boolean = false;
    protected readonly roundtrips: Array<Roundtrip<UserMessagePayload>> = [];
    /** 上一次调用 updateLatestMessage 更新的消息，用于计算增量更新内容 */
    private lastUpdatedMessage: AgentMessage | null = null;

    get latestRoundtrip() {
        return this.roundtrips[this.roundtrips.length - 1];
    }

    get previousRoundtrip() {
        return this.roundtrips[this.roundtrips.length - 2];
    }

    async onConversationMessage(agentPayload: AgentPayload<MessageOperationPayload>) {
        this.logger.info('onConversationMessage', agentPayload);
        switch (agentPayload.messageType) {
            case 'add-message':
                this.startNewWork(agentPayload.payload as UserMessagePayload);
                break;
            case 'stop-generating':
                await this.stopGenerating();
                break;
            case 'message-operation': {
                // 处理需要转发到message的消息
                const res = await this.handleNewMessage(agentPayload?.payload);
                this.updateLatestMessage(true);
                return res;
            }
            case 'refresh-messages': {
                this.updateAllMessages();
                break;
            }
        }
    }

    // @ts-expect-error
    async startNewWork(payload: UserMessagePayload) {
        this.logger.info('startNewWork', payload);

        this.updateStatus(AgentConversationStatus.Running);

        const roundtrip = this.startRoundtrip(payload);
        this.updateAllMessages();
        try {
            await this.startWork(roundtrip);
            roundtrip.complete();
            if (this.status === AgentConversationStatus.Cancelled) {
                return;
            }
            // 有可能上一个会话取消了，但一些异步的处理不会立即结束，然后新来一轮会话
            if (this.latestRoundtrip.uuid !== roundtrip.uuid) {
                return;
            }
            this.updateStatus(AgentConversationStatus.Completed);
            this.acceptLogOnEnd('end');
        }
        catch (ex) {
            if (ex instanceof CanceledError) {
                return;
            }
            if (ex instanceof Error) {
                const hasChinese = /[\u4E00-\u9FA5]/.test(ex.message);
                if (hasChinese) {
                    roundtrip.fail(ex.message);
                    this.updateStatus(AgentConversationStatus.Failed);
                    return;
                }
            }
            this.reportZuluError(ex);
            this.logger.error('[Zulu] failed create conversaion, reason:', (ex as Error).message, ex);

            // 有可能上一个会话取消了，但一些异步的处理不会立即结束，新来一轮会话后，上一个会话突然失败，这里的状态不能被覆盖
            if (this.latestRoundtrip.uuid !== roundtrip.uuid) {
                return;
            }
            roundtrip.fail('当前服务响应异常，建议稍后重试。');
            this.updateStatus(AgentConversationStatus.Failed);
        }
    }

    async reportZuluError(ex: unknown) {
        try {
            const tryConversationId = this.roundtrips[0]?.context?.conversationId;
            const error = ex as any;
            const latestMessage = this.latestRoundtrip.getLatestMessage() as ToolCallMessage;
            const fileContentSize = latestMessage?.composer?.tasks[0]?.originalContent?.length || 0;
            const reportMessage = _.omit(latestMessage?.toMessage(), ['composer']);
            this.logger.logUploader?.logUserAction({
                category: 'zulu',
                action: 'conversationError',
                label: `${error.message}-${error.code}`,
                content: `fileLength=${fileContentSize},message=${reportMessage} `,
                source: tryConversationId ? String(tryConversationId) : '',
            });
        }
        catch (ex) {
            //
        }
    }

    private previousRollbackSummary: RollbackFileSummary = [];
    async getRollbackSummary(userMessageUuid: string) {
        const sliceIndex = this.roundtrips.findIndex(roundtrip => roundtrip.request.uuid === userMessageUuid);
        const roundtripWillDiscard = this.roundtrips.slice(sliceIndex + 1, this.roundtrips.length);
        const summary = roundtripWillDiscard.reduce<RollbackFileSummary>(
            (result, roundTrip) => {
                for (const msg of roundTrip.toolCallMessages) {
                    const task = msg?.composer.tasks[0];
                    if (task && task.completed && task.accepted === AcceptState.ACCEPT) {
                        const previous = result.find(v => v.absolutePath === task.absolutePath);
                        if (previous) {
                            previous.changes.push(task);
                        }
                        else {
                            result.push({
                                absolutePath: task.absolutePath,
                                relativePath: task.filePath,
                                action: task.rollbackAction,
                                selected: true,
                                changes: [task],
                            });
                        }
                    }
                }
                return result;
            },
            []
        );

        for (const changed of summary) {
            const {absolutePath, changes, action} = changed;
            if (action === 'willDelete') {
                // 删除不考虑，假设创建的文件已经被用户删了，也无所谓，跳过处理即可
            }
            else if (action === 'willCreate') {
                // 该分支是把原来zulu删除的文件，进行还原，如果此时工作区已经有一个文件同名了，则认为冲突
                const {existed} = await this.virtualEditor.getDocument({absolutePath});
                if (existed) {
                    changed.conflict = true;
                    changed.selected = false;
                }
            }
            else if (action === 'willChange') {
                // 该分支是还原zulu修改的文件，将changes中的content内容依次和当前文件进行比较，只有有任意一个版本相等，则认为不冲突
                const {content: currentFileContent, existed} = await this.virtualEditor.getDocument({absolutePath});
                // 如果被用户手动删了的话，改成新建
                if (!existed) {
                    changed.action = 'willCreate';
                }

                const matched = changes.some(task => contentEqualRough(task.content, currentFileContent));
                if (!matched) {
                    changed.conflict = true;
                    changed.selected = false;
                }
            }
        }

        this.previousRollbackSummary = summary;
        return summary;
    }

    async openRollbackDiff(absolutePath: string) {
        const file = this.previousRollbackSummary.find(summary => absolutePath === summary.absolutePath)!;
        const task = file?.changes[0];
        const {content} = await this.virtualEditor.getDocument({absolutePath});

        await this.virtualEditor.openVirtualDiffDocument({
            absolutePath,
            content: content,
            modified: task.originalContent || '',
            source: 'preview',
        });
    }

    async executeRollback(userMessageUuid: string, selectedAbsolutePaths: string[]) {
        try {
            const tryConversationId = this.roundtrips[0]?.context?.conversationId;
            await apiRollbackZuluMessage({
                conversationId: tryConversationId as string,
                lastUserMessageId: userMessageUuid,
            });

            for await (const change of this.previousRollbackSummary) {
                if (selectedAbsolutePaths.includes(change.absolutePath)) {
                    const rollbackSummary = this.previousRollbackSummary.find(({absolutePath}) =>
                        absolutePath === change.absolutePath
                    );
                    await rollbackSummary?.changes[0].rollback();
                }
            }

            const sliceIndex = this.roundtrips.findIndex(roundtrip => roundtrip.request.uuid === userMessageUuid);
            this.roundtrips.length = sliceIndex + 1;
            this.updateAllMessages();
        }
        catch (e) {
            return {error: (e as Error).message};
        }
    }

    toMessages() {
        const processedMessages = this.roundtrips.map(r => r.toMessages()).flat();
        // TODO: webviewMessages 过滤掉不需要的字段，减少数据量
        const webviewMessages = processedMessages.map(message => {
            if (message.role === 'assistant') {
                const elements = (message.elements ?? [])
                    .map(ele => omit(ele, 'workflowSnapshot', 'params'));
                return {...message, elements: elements as AgentAssistantMessage['elements']};
            }
            return message;
        });
        return {
            processedMessages,
            webviewMessages,
        };
    }

    toJSON() {
        return this.roundtrips.map(r => r.toJSON()).flat();
    }

    setForeground(foreground: boolean) {
        this.foreground = foreground;
    }

    private startRoundtrip(payload: UserMessagePayload) {
        const roundtrip = new Roundtrip(
            this.id,
            this.repo,
            this.logger,
            this.virtualEditor,
            (forceUpdate?: boolean) => this.updateLatestMessage(forceUpdate),
            payload
        );
        roundtrip.setForeground(this.foreground);
        this.roundtrips.push(roundtrip);
        return roundtrip;
    }

    protected async stopGenerating() {
        this.beforeStop && await this.beforeStop();
        this.logger.info('stopGenerating');
        this.latestRoundtrip?.cancel();
        this.afterStop && await this.afterStop();
        this.updateLatestMessage(true);
        this.updateStatus(AgentConversationStatus.Cancelled);
    }

    protected rebuildRoundtrips(messages: any[]) {
        let roundtrip: Roundtrip | null = null;
        for (const message of messages) {
            if (message.role === 'user') {
                roundtrip = new Roundtrip(
                    this.id,
                    this.repo,
                    this.logger,
                    this.virtualEditor,
                    (forceUpdate?: boolean) => this.updateLatestMessage(forceUpdate),
                    Roundtrip.extractPayloadFromSnapshot(message)
                );
                roundtrip.context = message.context ?? {};
                this.roundtrips.push(roundtrip);
            }
            else if (message.role === 'assistant' && roundtrip) {
                roundtrip.rebuild(message);
                roundtrip = null;
            }
        }
    }

    protected getToolMessage(id: string) {
        for (const roundtrip of this.roundtrips) {
            const toolMessage = roundtrip.getToolCallMessage(id);
            if (toolMessage) {
                return toolMessage;
            }
        }
        return null;
    }

    protected getConversationToolCallMessages() {
        const messages: ToolCallMessage[] = [];
        for (const roundtrip of this.roundtrips) {
            messages.push(...roundtrip.toolCallMessages);
        }
        return messages;
    }

    /**
     * 全量更新当前会话的所有消息
     */
    protected updateAllMessages() {
        const {processedMessages, webviewMessages} = this.toMessages();
        this.doUpdateAllMessages(processedMessages, webviewMessages);
    }

    /**
     * 只更新最新的一条消息
     * 自动与上一次更新的消息对比，如果如果只更新了 elements 的内容会增量更新，否则全量更新最后一条 message
     * @param forceUpdate 强制全量更新
     */
    protected updateLatestMessage(forceUpdate: boolean = false) {
        const {processedMessages, webviewMessages} = this.toMessages();

        const latestMessage = webviewMessages[webviewMessages.length - 1];
        if (!latestMessage) {
            this.doUpdateAllMessages(processedMessages, webviewMessages);
            return;
        }
        const options = forceUpdate
            ? {scope: 'message', message: latestMessage} as const
            : this.getMessageUpdateOptions(latestMessage);

        this.lastUpdatedMessage = latestMessage;

        // 如果没有内容直接跳过
        if (options.scope === 'elements' && options.messageData.elements.length === 0) {
            return;
        }

        this.onMessageChange(this.id, processedMessages, options);
    }

    private doUpdateAllMessages(processedMessages: AgentMessage[], webviewMessages: AgentMessage[]) {
        this.onMessageChange(this.id, processedMessages, {scope: 'conversation', messages: webviewMessages});
    }

    /**
     * 计算消息的增量更新内容
     * @param message webviewMessages
     * @returns 更新选项
     */
    private getMessageUpdateOptions(message: AgentMessage) {
        const defaultOptions = {scope: 'message', message} as const;
        if (!this.lastUpdatedMessage || this.lastUpdatedMessage.id !== message.id) {
            return defaultOptions;
        }

        if (this.lastUpdatedMessage.role !== 'assistant' || message.role !== 'assistant') {
            return defaultOptions;
        }

        const isEqualWithoutElements = isEqual(
            omit(this.lastUpdatedMessage, 'elements'),
            omit(message, 'elements')
        );
        // 排除 elements 字段，如果其他字段都相等，只更新 elements 字段，否则全量
        if (!isEqualWithoutElements) {
            return defaultOptions;
        }

        const newElements = message.elements;
        const oldElements = this.lastUpdatedMessage.elements;
        const notMatchElements = newElements.some((ele, index) =>
            !oldElements[index] || oldElements[index].id !== ele.id
        );
        // 如果 elements 数量有变化，也全量更新
        if (notMatchElements) {
            return defaultOptions;
        }

        const changedElements = [];
        for (let i = 0; i < newElements.length; i++) {
            const newEle = newElements[i];
            const oldEle = oldElements[i];
            if (!isEqual(newEle, oldEle)) {
                changedElements.push(newEle);
            }
        }

        return {
            scope: 'elements',
            messageData: {
                id: message.id,
                elements: changedElements,
            },
        } as const;
    }

    // 结束前清理一些内容，如果需要就覆盖
    protected beforeStop(): Promise<void> {
        return Promise.resolve();
    }
    protected afterStop(): Promise<void> {
        return Promise.resolve();
    }

    protected abstract startWork(roundtrip: Roundtrip<UserMessagePayload>): Promise<void>;
    protected abstract handleNewMessage(messageOperationPayload: MessageOperationPayload): Promise<any>;
    protected abstract acceptLogOnEnd(from?: string): void;
}
