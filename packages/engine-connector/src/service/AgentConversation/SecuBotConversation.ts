import crypto from 'crypto';
import _ from 'lodash';
import path from 'path';
import {
    ACTION_SECUBOT,
    AgentConversationType,
    DrawChunk,
    SecubotAgentParsed,
    SecubotQueryPayload,
    SecubotFileFlaw,
    SECUBOT_DEFAULT_QUERY,
    ChatSessionDetail,
} from '@comate/plugin-shared-internals';
import {Mediator} from '../Mediator.js';
import {ConversationBase} from './ConversationBase.js';
import {Task} from '../Composer/tasks/Task.js';
import {MultiAcceptData} from '../Track/api.js';
import {Composer} from '../Composer/Composer.js';
import {ConversationContext} from './AbstractConversation.js';

type MessageFromViewView =
    | {action: 'file-view' | 'file-reject' | 'file-accept', id: string, filePath: string}
    | {action: 'file-accept-all' | 'file-reject-all' | 'rescan' | 'return-to-chat', id: string}
    | {action: 'fix-hardcode' | 'fix-common', id: string, url: string, title: string, data: any};

function equalFilePath(filePath?: string) {
    return (task: {absolutePath: string}) => {
        return filePath ? task.absolutePath === filePath : true;
    };
}
type RelativePath = string;
interface Knowledge {
    id: RelativePath;
    name: RelativePath;
    type: 'FILE' | 'FOLDER' | 'CURRENT_FILE';
}
interface Payload {
    query: string;
    data: any;
    knowledgeList: Knowledge[];
    conversationId?: number;
}

export const sleep = (ms: number) => new Promise(r => setTimeout(r, ms));

export class SecuBotConversation extends ConversationBase {
    type: AgentConversationType = AgentConversationType.SecuBotConversation;
    mediator: Mediator;
    constructor(context: ConversationContext, mediator: Mediator) {
        super(context, 'SECURITY_AGENT');
        this.mediator = mediator;
    }

    get multiSuggestions() {
        const tasks = this.runningMessage?.composer.tasks;
        const res: MultiAcceptData[] | undefined = tasks?.map((item: Task) => {
            return {
                id: `${item.filePath}-${item.action}`,
                path: item.absolutePath,
                row: '1',
                col: '1',
                generatedContent: item.calculatedDiff,
                accepted: item.accepted === 1,
            };
        });
        return res;
    }

    async *startWork(params: Payload) {
        try {
            const userDetail = this.mediator.userDetail();
            const payload: SecubotQueryPayload = {
                capabilityName: 'secubot',
                pluginName: 'security',
                // TODO 加入智能体获取上下文
                context: {
                    query: params.query === SECUBOT_DEFAULT_QUERY ? '' : params.query,
                    data: {
                        messageId: this.runningMessage?.id,
                    }, // uuid透传给插件 帮助统计数据
                },
                systemInfo: {
                    userId: userDetail.uid,
                    userDetail: {
                        name: userDetail.name,
                        displayName: userDetail.displayName,
                        email: userDetail.email,
                        license: userDetail.license,
                    },
                    cwd: this.repo.rootPath,
                },
                input: {
                    messageId: this.runningMessage?.id,
                    pluginName: 'security',
                    capability: 'secubot',
                    query: params.query === SECUBOT_DEFAULT_QUERY ? '' : params.query,
                    // 带data说明是第二轮
                    data: params.data ? params : '',
                    informationList: params.knowledgeList?.map(item => ({
                        ...item,
                        uuid: item.id,
                    })), // 加一个uuid支持插件需要的Information类型
                } as SecubotAgentParsed,
            };
            const sessionId = crypto.randomUUID();
            let preChunks: string[] = [];
            for await (
                const message of this.mediator.sendToPlugin(
                    sessionId,
                    {action: ACTION_SECUBOT, payload}
                )
            ) {
                const data = message?.data?.payload?.chunk as DrawChunk;
                const chunk = data.content as string[];
                // 增量更新
                let isNew = chunk.length > preChunks.length;
                if (chunk[chunk.length - 1].startsWith('```secubot')) {
                    const temp = await this.getChunkWithComposer(chunk[chunk.length - 1]);
                    isNew
                        ? preChunks.push(temp)
                        : preChunks[preChunks.length - 1] = temp;
                    yield preChunks.join('\n\n');
                }
                else {
                    isNew
                        ? preChunks.push(chunk[chunk.length - 1])
                        : preChunks[preChunks.length - 1] = chunk[chunk.length - 1];
                    let pre = preChunks.length > 1
                        ? preChunks.slice(0, preChunks.length - 1).join(`\n\n`) + `\n\n`
                        : '';
                    for (const str of preChunks[preChunks.length - 1]) {
                        pre += str;
                        await sleep(30);
                        yield pre;
                    }
                }
            }
            this.runningMessage!.status = 'success';
            return;
        }
        catch (error) {
            return;
        }
    }

    async getChunkWithComposer(chunkStr: string) {
        // 手动拼接composer语法
        const composerMarkdownWithState = await this.runningMessage!.composer.update(
            this.chunkToComposer(chunkStr)
        );
        // 防止太快，为了演示效果，这里手动sleep一下
        await sleep(1000);
        // composer的状态注入chunkStr
        return this.composerToChunk(composerMarkdownWithState, chunkStr);
    }

    getStopChunk(chunk: string) {
        const regexForSecubot = /```secubot\s([\s\S]*?)\s*```/g;
        let match;
        let res = chunk;
        const matches: string[] = [];
        while ((match = regexForSecubot.exec(chunk)) !== null) {
            matches.push(match[1]);
        }
        matches.forEach(item => {
            try {
                const data = JSON.parse(item);
                data.cancellationToken = true;
                res = res.replace(item, JSON.stringify(data));
            }
            catch {
                this.logger.info('secubot getStopChunk parse error');
            }
        });
        return res;
    }

    async afterStop() {
        if (this.runningMessage) {
            this.runningMessage.composer.tasks.forEach(task => task.cancel());
            const markdown = await this.runningMessage.composer.toMarkdown();
            this.runningMessage.content = this.composerToChunk(markdown, this.runningMessage.content);
            this.runningMessage.content = this.getStopChunk(this.runningMessage.content);
        }
    }

    async handleNewMessage(payload: MessageFromViewView) {
        switch (payload.action) {
            case 'fix-hardcode': {
                this.delFixButtons();
                this.virtualEditor.openUrlInEditorWebview({
                    url: payload.url,
                    title: payload.title,
                });
                this.startNewWork(payload);
                break;
            }
            case 'fix-common': {
                this.delFixButtons();
                this.startNewWork(payload);
                break;
            }
            case 'rescan': {
                this.startNewWork(this.userMessages[0].payload);
                break;
            }
            case 'file-view': {
                const task = this.runningMessage?.composer.tasks.find(
                    equalFilePath(path.join(this.repo.rootPath, payload.filePath))
                );
                await task?.openDiff();
                break;
            }
            case 'file-accept': {
                const task = this.runningMessage?.composer.tasks.find(
                    equalFilePath(path.join(this.repo.rootPath, payload.filePath))
                );
                try {
                    await task?.save();
                    if (this.multiSuggestions) {
                        this.acceptLog(this.runningMessage!.id, this.multiSuggestions);
                    }
                    this.mediator.sendToIde('TOAST_MESSAGE_CHANGE_MESSAGE', {
                        data: {type: 'success', message: `采纳成功`},
                    });
                }
                catch (error) {
                    this.mediator.sendToIde('TOAST_MESSAGE_CHANGE_MESSAGE', {
                        data: {type: 'fail', message: `采纳失败。${error}`},
                    });
                }
                break;
            }
            case 'file-reject': {
                const task = this.runningMessage?.composer.tasks.find(
                    equalFilePath(path.join(this.repo.rootPath, payload.filePath))
                );
                try {
                    await task?.revert();
                    if (this.multiSuggestions) {
                        this.acceptLog(this.runningMessage!.id, this.multiSuggestions);
                    }
                    this.mediator.sendToIde('TOAST_MESSAGE_CHANGE_MESSAGE', {
                        data: {type: 'fail', message: `放弃采纳成功`},
                    });
                }
                catch (error) {
                    this.mediator.sendToIde('TOAST_MESSAGE_CHANGE_MESSAGE', {
                        data: {type: 'fail', message: `放弃采纳失败。${error}`},
                    });
                }
                break;
            }
            case 'file-accept-all': {
                const tasks = this.runningMessage?.composer.tasks || [];
                try {
                    await Promise.allSettled(tasks.map(async item => await item.save()));
                    // 已经绑定了toast
                    if (this.multiSuggestions) {
                        this.acceptLog(this.runningMessage!.id, this.multiSuggestions);
                    }
                }
                catch (error) {
                    this.mediator.sendToIde('TOAST_MESSAGE_CHANGE_MESSAGE', {
                        data: {type: 'error', message: `采纳文件时出错: ${error}`},
                    });
                }
                break;
            }
            case 'file-reject-all': {
                const tasks = this.runningMessage?.composer.tasks || [];
                try {
                    await Promise.allSettled(tasks.map(async item => await item.revert()));
                    if (this.multiSuggestions) {
                        this.acceptLog(this.runningMessage!.id, this.multiSuggestions);
                    }
                }
                catch (error) {
                    this.mediator.sendToIde('TOAST_MESSAGE_CHANGE_MESSAGE', {
                        data: {type: 'error', message: `放弃采纳文件时出错: ${error}`},
                    });
                }
                break;
            }
        }
        const markdown = await this.runningMessage!.composer.toMarkdown();
        const content = this.composerToChunk(markdown, this.runningMessage!.content);
        this.runningMessage!.content = content;
    }

    setConversationChunk(chunk: string) {
        this.runningMessage!.content = chunk;
    }

    // 拼接composer codeblock 用于定制化的composer
    chunkToComposer(chunk: string) {
        const regex = /```secubot\s([\s\S]*?)\s*```/g;
        const res: string[] = [];
        let match;

        while ((match = regex.exec(chunk)) !== null) {
            const [, jsonString] = match;
            try {
                const data = JSON.parse(jsonString);
                if (['repair', 'flawTree'].includes(data.type) && Array.isArray(data.children)) {
                    const fileFlaw = data.children as SecubotFileFlaw[];
                    const formattedStrings = fileFlaw.filter((item: SecubotFileFlaw) => item.content !== '').map((
                        props: any
                    ) => `\`\`\`rewrite:${props.filePath}\n${props.content}${props.onProcessing ? '' : `\n\`\`\``}`);
                    res.push(...formattedStrings);
                }
            }
            catch (error) {
                this.logger.error('chunkToComposer error', error);
                return res.join('\n');
            }
        }
        return res.join('\n'); // 以换行符连接所有结果并返回合并后的字符串
    }

    /**
     * 将Markdown文本中的composer 状态注入chunk字符串中
     *
     * @param markdown 带状态的Markdown格式的字符串
     * @param chunk chunk字符串
     * @returns 处理后的chunk字符串
     */
    composerToChunk(markdown: string, chunk: string) {
        if (!markdown) {
            return chunk;
        }
        let res = chunk;

        const regexForSecubot = /```secubot\s([\s\S]*?)\s*```/g;
        let m;
        // 注入composer的状态
        while ((m = regexForSecubot.exec(chunk)) !== null) {
            const [full, jsonString] = m;
            try {
                const data = JSON.parse(jsonString);
                if (
                    ['repair', 'flawTree'].includes(data.type) && Array.isArray(data.children)
                ) {
                    const newData = data.children.map((props: any, index: number) => {
                        const id = path.join(this.repo.rootPath, props.filePath);
                        const task: Task | undefined = this.runningMessage?.composer.tasks.find(item =>
                            item.absolutePath === id
                        );
                        if (task) {
                            return {
                                ...props,
                                hasContent: props.content !== '',
                                status: task?.status,
                                accepted: Number(task?.accepted),
                                action: task?.action,
                            };
                        }
                        else {
                            return props;
                        }
                    });
                    res = res.replace(
                        full,
                        `\`\`\`secubot\n${JSON.stringify({...data, children: newData}, null, 2)}\n\`\`\``
                    );
                }
            }
            catch (error) {
                this.logger.error('composerToChunk error', error);
                return res;
            }
        }
        return res;
    }

    delFixButtons() {
        const secubotRegex = /```secubot\n([\s\S]*?)```/g;
        this.runningMessage!.content = this.runningMessage!
            .content
            .replace(secubotRegex, (match: string, jsonContent: string) => {
                try {
                    const parsed = JSON.parse(jsonContent);
                    if (parsed.type === 'fixButtons') {
                        return ''; // 删除这个块
                    }
                }
                catch (e) {
                    // JSON 解析失败，不处理这个块
                }
                return match; // 保留非 fixButtons 类型
            })
            .trim();
    }

    rebuildConversation(payload: ChatSessionDetail) {
        this.messages.push(...payload.messages.map(message => {
            if (message.role === 'assistant') {
                const composer = new Composer(
                    this.virtualEditor,
                    this.repo,
                    this.logger,
                    AgentConversationType.SecuBotConversation
                );
                return {
                    ...message,
                    composer: composer.createTaskFromSnapshot(message.composer),
                };
            }
            return message;
        }));
    }

    acceptLogOnEnd() {
        if (this.multiSuggestions) {
            this.acceptLog(this.runningMessage!.id, this.multiSuggestions);
        }
    }
}
