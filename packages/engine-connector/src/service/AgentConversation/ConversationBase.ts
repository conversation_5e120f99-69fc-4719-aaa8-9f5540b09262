import crypto from 'node:crypto';
import _ from 'lodash';
import {AgentConversationStatus, AgentConversationType, AgentPayload} from '@comate/plugin-shared-internals';
import {Composer} from '../Composer/Composer.js';
import {UserDetailForServer, UserDetailWithId} from '../User.js';
import {
    AbstractConversation,
    UserMessage,
    AssistantMessage as AbstractAssistantMessage,
} from './AbstractConversation.js';

const {findLast} = _;

export interface AssistantMessage<T = any> extends AbstractAssistantMessage<T> {
    composer: Composer;
}

export type UserDetail = UserDetailWithId & UserDetailForServer;

export abstract class ConversationBase<YieldChunk = string, WebviewContent = any, MessageOperationPayload = any>
    extends AbstractConversation<MessageOperationPayload> {
    foreground: boolean = false;
    messages: Array<UserMessage | AssistantMessage<WebviewContent>> = [];
    cancellationToken: boolean = false;
    inlineDiffView: boolean = false;

    get runningMessage() {
        return findLast(
            this.messages,
            (message): message is AssistantMessage<WebviewContent> => message.role === 'assistant'
        );
    }

    get assistantMessages() {
        return this.messages.filter((message): message is AssistantMessage<WebviewContent> =>
            message.role === 'assistant'
        );
    }

    get userMessages() {
        return this.messages.filter((message): message is UserMessage => message.role === 'user');
    }

    get previousMessage() {
        const messages = this.assistantMessages;
        return messages[Math.max(0, messages.length - 2)];
    }

    toMessages() {
        return {
            processedMessages: this.messages,
            webviewMessages: this.messages,
        };
    }

    toJSON() {
        return this.messages;
    }

    async onConversationMessage(agentPayload: AgentPayload<MessageOperationPayload>) {
        this.logger.info('onConversationMessage', agentPayload);
        switch (agentPayload.messageType) {
            case 'add-message':
                // 智能体内部实现也可以自己发新的一轮
                this.startNewWork(agentPayload.payload);
                break;
            case 'stop-generating':
                await this.stopGenerating();
                break;
            case 'message-operation': {
                // 处理需要转发到message的消息
                await this.handleNewMessage(agentPayload?.payload);
                this.onMessageChange(this.id, this.messages);
                break;
            }
        }
    }

    // 追加一轮，一轮表现为一个用户消息，同时启动智能体的逻辑开始处理
    // 第一轮默认会被调用，参数为用户的query或智能体自己写死
    async startNewWork(payload: MessageOperationPayload) {
        this.logger.info('startNewWork', payload);

        this.cancellationToken = false;
        this.updateStatus(AgentConversationStatus.Running);

        const userMessage: UserMessage = {
            id: crypto.randomUUID(),
            role: 'user',
            status: 'success',
            // TODO：还是把message消息和操作消息分开来比较好
            // @ts-expect-error
            content: payload?.query ?? '',
            // @ts-expect-error
            code: payload?.code,
            payload,
        };
        this.messages.push(userMessage);
        const composer = new Composer(
            this.virtualEditor,
            this.repo,
            this.logger,
            AgentConversationType.E2EBotConversation
        );
        composer.setForeground(this.foreground);
        const assistantMessageId = await this.generateMessageId();
        const assistantMessage: AssistantMessage = {
            id: assistantMessageId,
            role: 'assistant',
            status: 'inProgress',
            content: '',
            composer,
            elements: [],
        };
        this.messages.push(assistantMessage);
        // 返回第一次对话保证问题消息展示
        this.onMessageChange(this.id, this.messages);
        const iterator = this.startWork(payload);
        while (true) {
            if (this.cancellationToken) {
                break;
            }

            this.setUtime();
            try {
                let checkToken: NodeJS.Timer | null = null;
                const result = await Promise.race([
                    iterator.next(),
                    new Promise<{done: boolean, value: any}>(resolve => {
                        checkToken = setInterval(() => {
                            if (this.cancellationToken) {
                                checkToken && clearInterval(checkToken);
                                resolve({done: true, value: 'stop-generating'});
                            }
                        }, 500);
                    }),
                ]);
                checkToken && clearInterval(checkToken);
                const {value: chunk, done} = result;
                // 用户点击停止生成，任务可能还在执行，但是肯定不会再发新的消息
                if ((done && chunk === 'stop-generating') || this.cancellationToken) {
                    break;
                }
                // 正常结束，done为true，最后一次chunk为空
                if (done) {
                    this.updateStatus(AgentConversationStatus.Completed);
                    this.acceptLogOnEnd();
                    break;
                }
                this.setConversationChunk(chunk);
                this.onMessageChange(this.id, this.messages);
            }
            catch (ex) {
                this.updateStatus(AgentConversationStatus.Failed);
                break;
            }
        }
    }

    // 业务实现自己的setConversatoin
    abstract setConversationChunk(chunk: YieldChunk): void;

    async stopGenerating() {
        this.beforeStop && await this.beforeStop();
        this.logger.info('stopGenerating');
        this.cancellationToken = true;
        this.afterStop && await this.afterStop();
        this.onMessageChange(this.id, this.messages);
        this.updateStatus(AgentConversationStatus.Cancelled);
        this.acceptLogOnEnd();
    }

    setForeground(foreground: boolean) {
        this.foreground = foreground;
        this.assistantMessages.forEach(message => message.composer?.setForeground(foreground));
    }

    // 结束前清理一些内容，如果需要就覆盖
    protected beforeStop(): Promise<void> {
        return Promise.resolve();
    }
    protected afterStop(): Promise<void> {
        return Promise.resolve();
    }

    // 可以取到this.messages来调度怎么执行
    protected abstract startWork(userMessageContent: any): AsyncIterableIterator<YieldChunk>;
    protected abstract handleNewMessage(messageOperationPayload: MessageOperationPayload): void;
    protected abstract acceptLogOnEnd(): void;
}
