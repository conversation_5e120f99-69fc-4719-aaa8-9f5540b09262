import _ from 'lodash';
import {join, dirname} from 'node:path';
import {access, mkdir, writeFile} from 'node:fs/promises';
import {
    AgentConversationInfo,
    AgentConversationStatus,
    AgentConversationType,
    AgentMessage,
    AgentPayload,
    axiosInstance,
    VirtualEditor,
} from '@comate/plugin-shared-internals';
import {LoggerRoleInstance} from '../Logger.js';
import {Mediator} from '../Mediator.js';
import {ChatSessionManager} from '../ChatSessionManager/index.js';
import {McpManager} from '../McpManager/index.js';
import {ConversationBase} from './ConversationBase.js';
import {SecuBotConversation} from './SecuBotConversation.js';
import {E2EBotConversation} from './E2EBotConversation.js';
import {DebugBotConversation} from './DebugBotConversation/index.js';
import {TestBotConversation} from './TestBotConversation/index.js';
import {
    AbstractConversation,
    ConversationInfo,
    MessageUpdateOptions,
    UpdatedMessageData,
} from './AbstractConversation.js';
import {Repo} from './Zulu/types/knowledge.js';
import {
    kernel,
    PT_WEBVIEW_AGENT_CREATE_FIGMA_RULES,
} from '@comate/kernel-shared';
import {F2cBotConversation} from './F2CBotConversation.js';

const {debounce} = _;

type UpdateMessageType =
    | 'conversation-messages'
    | 'conversation-status'
    | 'partial-message-data'
    | 'partial-message-elements';

interface ConversationData {
    data: {
        type: UpdateMessageType;
        conversationInfo: ConversationInfo;
        messages?: AgentMessage[];
        messageData?: UpdatedMessageData;
    };
}

// 定义 ConversationManager 来管理多个任务
export class ConversationManager {
    private readonly conversations: Map<string, AbstractConversation> = new Map();
    private foregroundConversationId: string = '';

    constructor(
        private readonly repo: Repo,
        private readonly mediator: Mediator,
        private readonly virtualEditor: VirtualEditor,
        private readonly chatSessionManager: ChatSessionManager,
        private readonly logger: LoggerRoleInstance,
        private readonly mcpManager: McpManager
    ) {
        // webview 暂时没有打通文件操作，先在这里处理一下
        kernel.connect.onWebviewMessage(
            PT_WEBVIEW_AGENT_CREATE_FIGMA_RULES,
            this.createFigmaRules.bind(this)
        );
    }

    createConversation(agentPayload: AgentPayload): AbstractConversation {
        const baseContext = {
            repo: this.repo,
            virtualEditor: this.virtualEditor,
            onStatusChange: this.onStatusChange.bind(this),
            onMessageChange: this.onMessageChange.bind(this),
            logger: this.logger,
            userDetail: this.mediator.userDetail(),
        };
        switch (agentPayload.conversationType) {
            case AgentConversationType.SecuBotConversation:
                return new SecuBotConversation(baseContext, this.mediator);
            case AgentConversationType.TestBotConversation:
                return new TestBotConversation(baseContext);
            case AgentConversationType.E2EBotConversation:
                return new E2EBotConversation(baseContext, this.mcpManager);
            case AgentConversationType.DebugBotConversation:
                return new DebugBotConversation(
                    baseContext,
                    this.mcpManager,
                    this.sendCustomEventToIde.bind(this)
                );
            case AgentConversationType.F2cBotConversation:
                return new F2cBotConversation(baseContext, this.mcpManager);
            default:
                throw new Error(`Unknown conversation type: ${agentPayload.conversationType}`);
        }
    }

    addConversation(agentPayload: AgentPayload): AbstractConversation | undefined {
        try {
            const conversation = this.createConversation(agentPayload);
            this.conversations.set(conversation.id, conversation);
            this.setForegroundConversation(conversation.id);
            return conversation;
        }
        catch (e) {
            this.logger.error('addConversation', e);
        }
    }

    // 通知函数（例如更新Tab上的红点）
    onStatusChange(conversationId: string): void {
        const conversationInfo = this.conversations.get(conversationId)!.getConversationInfo();
        this.updateConversationMessages('conversation-status', conversationInfo);
    }

    private debouncedMap = new Map<string, any>();

    /**
     * 如果是前台任务，则更新前台任务的内容
     * @param conversationId 任务id
     * @param messages 会话的所有消息，会用于缓存历史，如果发给 webview 时想过滤掉一些字段，可以通过 options 来实现
     * @param options 更新选项，可以选择更新策略
     */
    onMessageChange(conversationId: string, messages: AgentMessage[], options?: MessageUpdateOptions): void {
        const conversation = this.conversations.get(conversationId)!;
        const conversationInfo = conversation.getConversationInfo();
        if (conversationId === this.foregroundConversationId) {
            if (options) {
                this.updateConversationMessageWithOptions(conversationInfo, options);
            }
            else {
                this.updateConversationMessages(
                    'conversation-messages',
                    conversationInfo,
                    messages
                );
            }
        }
        const title = messages[0]?.content;
        if (title) {
            const isRunning = conversation.status === AgentConversationStatus.Running;
            let execute = this.debouncedMap.get(conversationInfo.id)
                || debounce(history => this.chatSessionManager.save(history), 2000, {leading: true});
            if (isRunning) {
                this.debouncedMap.set(conversationId, execute);
            }
            execute({
                source: conversationInfo.type,
                sessionUuid: conversationInfo.id,
                messages,
                title: title,
                ctime: conversationInfo.ctime,
                utime: conversationInfo.utime,
                workspaceDirectory: this.repo.rootPath,
            });
            // 做一些兜底处理，避免一直增长
            if (!isRunning) {
                this.debouncedMap.delete(conversationId);
            }
        }
    }

    async rebuildConversationFromHistory(conversationId: string) {
        if (!conversationId) {
            return;
        }

        const history = await this.chatSessionManager.find(conversationId);
        if (history) {
            try {
                const conversation = this.createConversation({
                    conversationType: history.source as AgentConversationType,
                    conversationId: history.sessionUuid,
                    payload: {},
                    messageType: 'rebuild-conversation',
                });
                conversation.setConversationId(history.sessionUuid);
                conversation.setUtime(history.utime);
                conversation.rebuildConversation(history);
                return conversation;
            }
            catch (ex) {
                this.logger.error('rebuildConversationFromHistory', ex);
            }
        }
    }

    // 切换前台任务
    setForegroundConversation(conversationId: string) {
        // todo 触发时机
        const conversation = this.conversations.get(conversationId);
        if (!conversation) {
            return;
        }

        Array.from(this.conversations.values()).forEach(
            conversation => conversation.setForeground(false)
        );
        // 设置新任务为前台任务
        conversation.setForeground(true);
        this.foregroundConversationId = conversationId;
        this.logger.info('setForegroundConversation', conversationId);

        const {processedMessages, webviewMessages} = conversation.toMessages();
        this.onMessageChange(
            conversationId,
            processedMessages,
            {scope: 'conversation', messages: webviewMessages}
        );
    }

    // 列出所有任务
    listAllConversations(): AgentConversationInfo[] {
        const allConversations = Array
            .from(this.conversations)
            .filter(([, conversation]) => conversation.toMessages().webviewMessages.length)
            .map(([, conversation]) => {
                const lastUserMessage = conversation
                    .toMessages()
                    .webviewMessages
                    .slice()
                    .reverse()
                    .find(msg => msg.role === 'user');
                return {
                    ...conversation.getConversationInfo(),
                    lastQuery: lastUserMessage?.content,
                };
            });
        return allConversations.reverse();
    }

    // 添加一个新对话，可能带消息，可能不带，带消息直接触发一轮work
    async onNewMessage(agentPayload: AgentPayload) {
        const {conversationId} = agentPayload;
        // 已存在的conversation发送了新消息
        let conversation: AbstractConversation | undefined = this.conversations.get(conversationId!);
        if (conversation) {
            conversation = this.conversations.get(conversationId);
            const res = await conversation?.onConversationMessage(agentPayload);
            if (agentPayload.messageType === 'message-operation') {
                return res;
            }
        }
        else {
            conversation = await this.rebuildConversationFromHistory(conversationId);
            if (conversation) {
                this.conversations.set(conversation.id, conversation);
                this.setForegroundConversation(conversation.id);
            }
            else {
                conversation = this.addConversation(agentPayload);
            }
        }
        if (conversation) {
            return {
                id: conversation.id,
                status: conversation.status,
                type: conversation.type,
            };
        }
    }

    /**
     * 智能体向IDE发送自定义事件
     */
    private async sendCustomEventToIde(event: string, data: any) {
        return this.mediator.sendToIde(event, data);
    }

    private async updateConversationMessages(
        type: UpdateMessageType,
        conversationInfo: ConversationInfo,
        messages?: AgentMessage[]
    ) {
        if (conversationInfo.id !== this.foregroundConversationId) {
            return;
        }

        const newData: ConversationData = {
            data: {
                type,
                conversationInfo,
            },
        };
        if (messages) {
            newData.data.messages = messages;
        }
        this.sendMessageImmediately(newData);
    }

    /**
     * 自定义更新消息
     */
    private async updateConversationMessageWithOptions(
        conversationInfo: ConversationInfo,
        options: MessageUpdateOptions
    ) {
        if (options.scope === 'conversation') {
            this.updateConversationMessages(
                'conversation-messages',
                conversationInfo,
                options.messages
            );
        }
        else if (options.scope === 'message') {
            const data: ConversationData = {
                data: {
                    type: 'partial-message-data',
                    conversationInfo,
                    messages: [options.message],
                },
            };
            this.sendMessageImmediately(data);
        }
        else {
            const data: ConversationData = {
                data: {
                    type: 'partial-message-elements',
                    conversationInfo,
                    messageData: options.messageData,
                },
            };
            this.sendMessageImmediately(data);
        }
    }

    private sendMessageImmediately(data: ConversationData) {
        return this.mediator.sendToIde('COMATE_AGENT_UPDATE_MESSAGE', data, {
            skipWriteLog: data.data.conversationInfo.status === AgentConversationStatus.Running,
        });
    }

    private async createFigmaRules(data: {
        path: string;
    }): Promise<string> {
        try {
            const {path: relativePath} = data;
            const absolutePath = join(this.repo.rootPath, relativePath);
            try {
                // 检查文件是否存在
                await access(absolutePath);
                return absolutePath;
            }
            catch {
                // 文件不存在，继续创建
            }
            const dirPath = dirname(absolutePath);
            await mkdir(dirPath, {recursive: true});
            const response = await axiosInstance.get('/api/figma/defaultRule');
            await writeFile(absolutePath, response.data?.content);
            return absolutePath;
        }
        catch (error: unknown) {
            this.logger.error('createFigmaRules error:', error);
            throw error;
        }
    }
}
