type RelativePath = string;

export interface Knowledge {
    id: RelativePath;
    name: RelativePath;
    type: ContextType;
    selection?: [number, number];
    filePath?: string;
    content?: string;
}

export interface Repo {
    repoId: string;
    username: string;
    rootPath: string;
    repoUrl?: string;
    branch?: string;
}

export enum IntentStrategyRule {
    /** 当前仓库关键词检索 */
    REPO_KEY = 'REPO_KEY',
    /** 当前仓库向量检索 */
    REPO_VECTOR = 'REPO_VECTOR',
    /** 当前代码库正则检索 */
    REPO_REGEX = 'REPO_REGEX',
    /** 指令目录关键词检索 */
    FOLDER_KEY = 'FOLDER_KEY',
    /** 指令目录向量检索 */
    FOLDER_VECTOR = 'FOLDER_VECTOR',
    /** 当前目录正则检索 */
    FOLDER_REGEX = 'FOLDER_REGEX',
    /** 指令目录读取 */
    FOLDER_READ = 'FOLDER_READ',
    /** 指令文件读取 */
    FILE_READ = 'FILE_READ',
    /** 当前文件读取 */
    CURRENT_FILE_READ = 'CURRENT_FILE_READ',
}

export enum ContextType {
    REPO = 'REPO',
    FOLDER = 'FOLDER',
    FILE = 'FILE',
    CURRENT_FILE = 'CURRENT_FILE',
    IMAGE = 'IMAGE',
    TERMINAL = 'TERMINAL',
    CODE_SELECTION = 'CODE_SELECTION',
    TERMINAL_SELECTION = 'TERMINAL_SELECTION',
    RULE = 'RULE',
}

interface IntentContext<T extends ContextType> {
    type: T;
    id: string;
    // '#当前代码库';
    name: string;
    site: null | string[];
    url: null | string;
    repo_id: string | null;
}

/** 字符串正则 */
type StringRegexp = string;
interface CoreIntentStrategy<R extends IntentStrategyRule, T> {
    /** 意图识别的检索规则 */
    rule: R;
    /** 意图识别的检索规则上下文的类型 */
    context: T;
    /** 改写后的 query */
    query: string;
    /** 正则检索的规则 */
    pattern: {
        file_name: StringRegexp[];
        file_content: StringRegexp[];
    };
    start: null;
    end: null;
}

export type RepoKeyIntentStrategy = CoreIntentStrategy<
    IntentStrategyRule.REPO_KEY,
    IntentContext<ContextType.REPO>
>;
export type RepoVectorIntentStrategy = CoreIntentStrategy<
    IntentStrategyRule.REPO_VECTOR,
    IntentContext<ContextType.REPO>
>;
export type RepoRegexIntentStrategy = CoreIntentStrategy<
    IntentStrategyRule.REPO_REGEX,
    IntentContext<ContextType.REPO>
>;
export type FolderVectorIntentStrategy = CoreIntentStrategy<
    IntentStrategyRule.FOLDER_VECTOR,
    IntentContext<ContextType.FOLDER>
>;
export type FolderKeyIntentStrategy = CoreIntentStrategy<
    IntentStrategyRule.FOLDER_KEY,
    IntentContext<ContextType.FOLDER>
>;
export type FolderRegexIntentStrategy = CoreIntentStrategy<
    IntentStrategyRule.FOLDER_REGEX,
    IntentContext<ContextType.FOLDER>
>;
export type FolderReadIntentStrategy = CoreIntentStrategy<
    IntentStrategyRule.FOLDER_READ,
    IntentContext<ContextType.FOLDER>
>;
export type FileReadIntentStrategy = CoreIntentStrategy<
    IntentStrategyRule.FILE_READ,
    IntentContext<ContextType.FILE>
>;
export type CurrentFileReadIntentStrategy = CoreIntentStrategy<
    IntentStrategyRule.CURRENT_FILE_READ,
    IntentContext<ContextType.FILE>
>;

export type IntentStrategy =
    | RepoKeyIntentStrategy
    | RepoVectorIntentStrategy
    | RepoRegexIntentStrategy
    | FolderVectorIntentStrategy
    | FolderKeyIntentStrategy
    | FolderRegexIntentStrategy
    | FolderReadIntentStrategy
    | FileReadIntentStrategy
    | CurrentFileReadIntentStrategy;
