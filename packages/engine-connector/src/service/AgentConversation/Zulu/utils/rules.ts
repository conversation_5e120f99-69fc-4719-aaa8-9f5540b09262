import path from 'node:path';
import matter from 'gray-matter';
import picomatch from 'picomatch';
import {readdir, stat} from 'node:fs/promises';
import _ from 'lodash';
import {
    VirtualEditor,
    DEFAULT_WORKSPACE_CONFIG_FOLDER,
    DEFAULT_RULE_CONFIG_FOLDER,
    extractMentionFilesFromRuleConfig,
    RuleMetadata,
} from '@comate/plugin-shared-internals';
import { isFileExist } from '../../../../utils/fs.js';

const {uniq} = _;

export const COMATE_RULE_FILE_SUFFIX = '.mdr';
export const CURSOR_RULE_FILE_SUFFIX = '.mdc';

export async function extractMentionFiles(content: string, rootPath: string) {
    const {files: mentions} = extractMentionFilesFromRuleConfig(content);
    const files: string[] = [];
    for (const mention of uniq(mentions)) {
        const absolutePath = path.join(rootPath, mention);
        const fileExist = await isFileExist(absolutePath);
        if (fileExist) {
            files.push(mention);
        }
    }
    return files;
}

function getWorkspaceRuleFolder(rootPath: string) {
    return path.join(rootPath, DEFAULT_WORKSPACE_CONFIG_FOLDER, DEFAULT_RULE_CONFIG_FOLDER);
}

function getCursorRuleFolder(rootPath: string) {
    return path.join(rootPath, '.cursor', 'rules');
}

/**
 * 验证文件名是否只包含小写字母、数字和连字符
 * @param filename 要验证的文件名（不包含路径）
 * @param suffix 文件后缀
 */
export function isValidRuleFilename(filename: string, suffix: string): boolean {
    if (!filename.endsWith(suffix)) {
        return false;
    }
    const nameWithoutSuffix = filename.slice(0, -suffix.length);
    // 只允许小写字母、数字和连字符
    return /^[a-z0-9-]+$/.test(nameWithoutSuffix);
}

async function listWorkspaceRuleFiles(rootPath: string): Promise<string[]> {
    const folder = getWorkspaceRuleFolder(rootPath);
    const cursorFolder = getCursorRuleFolder(rootPath);
    const [workspaceRules, cursorRules] = await Promise.allSettled([
        readdir(folder),
        readdir(cursorFolder),
    ]);
    const workspaceFiles = workspaceRules.status === 'fulfilled'
        ? workspaceRules.value
            .filter(file => isValidRuleFilename(file, COMATE_RULE_FILE_SUFFIX))
            .map(file => path.relative(rootPath, path.join(folder, file)))
        : [];

    const cursorFiles = cursorRules.status === 'fulfilled'
        ? cursorRules.value
            .filter(file => isValidRuleFilename(file, CURSOR_RULE_FILE_SUFFIX))
            .map(file => path.relative(rootPath, path.join(cursorFolder, file)))
        : [];

    return [...workspaceFiles, ...cursorFiles];
}

interface WorkspaceRule {
    path: string;
    content: string;
    metadata: RuleMetadata;
}

/**
 * 获取文件的创建时间，如果获取失败则返回当前时间
 * @param filePath 文件路径
 * @returns 文件的创建时间戳（毫秒）
 */
async function getFileBirthTime(filePath: string): Promise<number> {
    try {
        const stats = await stat(filePath);
        return stats.birthtime.getTime();
    } catch {
        return Date.now();
    }
}

function isFulfilled<T>(result: PromiseSettledResult<T>): result is PromiseFulfilledResult<T> {
    return result.status === 'fulfilled';
}

export async function getWorkspaceRules(rootPath: string, virtualEditor: VirtualEditor) {
    const files = await listWorkspaceRuleFiles(rootPath);
    const rules = await Promise.allSettled(files.map(async file => {
        const absolutePath = path.join(rootPath, file);
        const {metadata, content, name} = await getWorkspaceRule(absolutePath, virtualEditor);
        const birthTime = await getFileBirthTime(absolutePath);

        return {
            path: file,
            name,
            content,
            metadata,
            birthTime,
        };
    }));

    return rules
        .filter(isFulfilled)
        .map(rule => rule.value)
        .sort((a, b) => b.birthTime - a.birthTime);
}

function customYamlParser(str: string) {
    const lines = str.split('\n');
    const result: Record<string, string> = {};
    for (const line of lines) {
        const trimmedLine = line.trim();
        if (trimmedLine === '' || trimmedLine.startsWith('#')) {
            continue;
        }
        const parts = line.split(':');
        if (parts.length < 2) {
            continue;
        }
        const key = parts[0].trim();
        if (!key) {
            continue;
        }
        const value = parts.slice(1).join(':').trim();
        result[key] = value;
    }
    return result;
}

export function parseRuleContent(content: string): {
    metadata: Record<string, string>;
    content: string;
} {
    const {data: metadata, content: ruleContent} = matter(
        content,
        {engines: {yaml: customYamlParser}},
    );
    return {
        metadata,
        content: ruleContent,
    };
}

export async function getWorkspaceRule(
    absolutePath: string,
    virtualEditor: VirtualEditor
) {
    const {content = ''} = await virtualEditor.getDocument({absolutePath});
    const {metadata, content: ruleContent} = parseRuleContent(content);

    return {
        name: path.basename(absolutePath),
        content: ruleContent,
        metadata: normalizeMetadata(metadata),
    };
}

export function addFilePatternSuffix(metadata: RuleMetadata, content: string) {
    const globs = metadata.globs ?? '';
    const patterns = globs
        .split(',')
        .map(g => g.trim())
        .filter(g => g)
        .map(g => `\`${g}\``);
    return patterns.length > 0 ? `${content}\n(只作用于以下类型的文件： ${patterns.join(', ')})` : content;
}


/**
 * 规范化规则元数据，提取需要的字段
 * @param metadata 原始元数据
 * @returns 处理后的元数据对象
 */
export function normalizeMetadata(metadata: Record<string, string>): RuleMetadata {
    if (typeof metadata !== 'object' || metadata === null) {
        return {};
    }

    const result: RuleMetadata = {};

    if (metadata.description) {
        result.description = metadata.description;
    }
    if (metadata.globs) {
        result.globs = metadata.globs;
    }
    if (metadata.alwaysApply) {
        result.alwaysApply = metadata.alwaysApply === 'true';
    }

    return result;
}

export function filterActiveRule(
    rule: WorkspaceRule,
    files: string[],
): boolean {
    if (rule.metadata.alwaysApply === true) {
        return true;
    }
    if (typeof rule.metadata.globs === 'string' && rule.metadata.globs) {
        const globs = rule.metadata.globs
            .split(',')
            .map(g => g.trim())
            .filter(Boolean)
            .map(g => `**/${g}`);
        const isMatch = picomatch(globs, {dot: true});
        if (files.some(file => isMatch(file))) {
            return true;
        }
    }
    return false;
}

export function metadataStringify(metadata: RuleMetadata): string {
    const lines = ['---'];
    lines.push(`description: ${metadata.description ?? ''}`);
    lines.push(`globs: ${metadata.globs ?? ''}`);
    lines.push(`alwaysApply: ${!!metadata.alwaysApply}`);
    lines.push('---');
    return lines.join('\n');
}
