import _, {DebounceSettingsLeading} from 'lodash';

export const asyncDebounce = <T extends (...args: any) => Promise<void>>(
    func: T,
    wait: number,
    opts: DebounceSettingsLeading
) => {
    const debounced = _.debounce(
        (resolve, reject, args) => {
            func(...args).then(resolve).catch(reject);
        },
        wait,
        opts
    );
    return (...args: Parameters<T>) => {
        return new Promise<void>((resolve, reject) => {
            debounced(resolve, reject, args);
        });
    };
};
