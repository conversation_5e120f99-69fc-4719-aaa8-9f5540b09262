import {kernel} from '@comate/kernel-shared';
import {isFilePathEqual} from '../../../Composer/utils/index.js';

/**
 * 比较两个多行的字符串是否相等，所有的行首行尾缩进会被忽略
 * @param c1
 * @param c2
 * @returns Boolean
 */
export function contentEqualRough(c1: string, c2: string) {
    if (typeof c1 !== 'string' || typeof c2 !== 'string') {
        return false;
    }
    const l1 = c1.split(/\r?\n/);
    const l2 = c2.split(/\r?\n/);
    if (l1.length !== l2.length) {
        return false;
    }
    return l1.every((line, i) => line.trim() === l2[i].trim());
}
export function equalFilePathOrUniqKey(uniqKey?: string) {
    return (task: {key: string}) => {
        return uniqKey ? isFilePathEqual(task.key, uniqKey) : true;
    };
}
