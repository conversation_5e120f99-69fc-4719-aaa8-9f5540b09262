import {join} from 'node:path';
import {readdirSync} from 'node:fs';
import {isNotJunk} from 'junk';
import {globby} from 'globby';
import {fdir} from 'fdir';
import picomatch from 'picomatch';
import {directories as defaultIgnoreDirectories} from 'ignore-by-default';
import {WorkspaceFileStructure} from './tree.js';

/**
 * 获取指定目录下的所有文件路径
 * @param dirPath
 * @param excludeFolderNames
 * @returns
 */
export function getFilePathRecursiveFrom(dirPath: string, excludeFolderNames: string[]): string[] {
    const files: string[] = [];
    const entries = readdirSync(dirPath, {withFileTypes: true});
    for (const entry of entries) {
        const filePath = join(dirPath, entry.name);
        if (entry.isDirectory()) {
            files.push(...getFilePathRecursiveFrom(join(dirPath, entry.name), excludeFolderNames));
        }
        else if (isNotJunk(filePath)) {
            files.push(filePath);
        }
    }
    return files;
}

export class ReadWorkspacePermissionError extends Error {
    constructor() {
        super('Zulu 无法读取工作区文件，请检查是否有权限访问工作区');
    }
}

export async function streamingListEntries(root: string) {
    try {
        const options = {
            cwd: root,
            gitignore: true,
            ignore: defaultIgnoreDirectories().map(v => `**/${v}`),
            markDirectories: true,
            onlyFiles: false,
            dot: true,
        };
        const files = await globby('**', options);
        const tree = new WorkspaceFileStructure();
        for (const file of files) {
            tree.add(file);
        }
        return tree.toOverviewStructure();
    }
    catch (ex) {
        const msg = (ex as Error).message;
        if (/permitted|permission/.test(msg)) {
            throw new ReadWorkspacePermissionError();
        }
        return {tree: (ex as Error).message};
    }
}

export async function hasFilesMatchingPatterns(root: string, filePatterns: string[]) {
    const ignoreDirs = defaultIgnoreDirectories();
    const result = await new fdir()
        .withRelativePaths()
        .withMaxFiles(1)
        .onlyCounts()
        .withGlobFunction(picomatch)
        .globWithOptions(filePatterns, {dot: true})
        .exclude((dirName: string) => {
            return ignoreDirs.includes(dirName);
        })
        .crawl(root)
        .withPromise();

    return result.files > 0;
}