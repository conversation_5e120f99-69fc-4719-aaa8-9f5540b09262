import {DocumentPosition} from '@comate/plugin-shared-internals';
import {readFileContent} from '../../Composer/utils/codeChunk.js';

export class CodeChunk {
    constructor(
        protected repo: string,
        protected type: string,
        protected path: string,
        public content: string,
        protected contentEnd: DocumentPosition = {line: 0, column: 0},
        protected contentStart: DocumentPosition = {line: 0, column: 0}
    ) {
    }

    toJSON() {
        return {
            repo: this.repo,
            type: this.type,
            path: this.path,
            content: this.content,
            contentStart: this.contentStart,
            contentEnd: this.contentEnd,
        };
    }
}

export class TerminalCodeChunk extends CodeChunk {
    constructor(repo: string, content: string) {
        super(repo, 'terminal', '', content);
    }
}

export class FileCodeChunk extends CodeChunk {
    constructor(repo: string, path: string, content: string) {
        const lines = content.split(/\r?\n/);
        const contentEnd = {line: lines.length - 1, column: lines[lines.length - 1].length};
        super(repo, 'fullContent', path, content, contentEnd);
    }

    hiddenContent() {
    }
}

export class MediaChunk extends CodeChunk {
    constructor(repo: string, path: string, type: string) {
        super(repo, type, path, '', {line: 0, column: 0});
    }

    hiddenContent() {
    }
}

export class RuleCodeChunk extends CodeChunk {
    constructor(repo: string, path: string, content: string) {
        const lines = content.split(/\r?\n/);
        const contentEnd = {line: lines.length - 1, column: lines[lines.length - 1].length};
        super(repo, 'rule', path, content, contentEnd);
    }
}

export class FileSelectionCodeChunk extends CodeChunk {
    constructor(
        repo: string,
        path: string,
        content: string,
        contentStart: DocumentPosition,
        contentEnd: DocumentPosition
    ) {
        const selectionContent = readFileContent(content, contentStart, contentEnd);
        super(repo, 'selection', path, selectionContent, contentEnd, contentStart);
    }
}
