import _ from 'lodash';
import {relative} from 'node:path';
import {kernel} from '@comate/kernel-shared';
import {
    AgentConversationStatus,
    AgentConversationType,
    Axios,
    ChatSessionDetail,
    CodeChunk,
    createAxiosCancelTokenSource,
} from '@comate/plugin-shared-internals';
import {AcceptState} from '../Composer/types.js';
import {
    AnalyzeResult,
    ChatParams,
    QueueCancelledError,
    analyze,
    chat,
} from '../Composer/api.js';
import {extractLocalhostAddressFromOutput} from '../../utils/terminal.js';
import {isPortAvailable} from '../../utils/net.js';
import {Roundtrip, RoundtripStatus} from '../Roundtrip/index.js';
import {getBaseSysInfo} from '../Roundtrip/utils/os.js';
import {WorkflowResult} from '../Roundtrip/Workflow/WorkflowBase.js';
import {ConversationThread} from './ConversationThread.js';
import {getTraceRepoInfo} from '../../utils/track.js';
import {filterSupportedCommand} from '../../utils/command.js';
import {addFilePatternSuffix, extractMentionFiles, filterActiveRule, getWorkspaceRules} from './Zulu/utils/rules.js';
import {throttleAsyncIterator} from '../../utils/throttleAsyncIterator.js';
import {ToolCallEventType} from '../Roundtrip/types.js';
import {MultiAcceptData, acceptCode} from '../Track/api.js';
import {McpManager} from '../McpManager/index.js';
import {ConversationContext} from './AbstractConversation.js';
import {ContextType, Knowledge} from './Zulu/types/knowledge.js';
import {
    context2CodeChunks,
    excludeIllegalContexts,
    transformContexts,
    transformKnowledges,
} from './Zulu/utils/context.js';
import {equalFilePathOrUniqKey} from './Zulu/utils/equal.js';
import {streamingListEntries} from './Zulu/utils/fs.js';

const COMMON_COMMANDS = [
    'python',
    'python3',
    'pip',
    'pip3',
    'go',
    'docker',
    'curl',
    'ffmpeg',
    'brew',
    'pip',
    'jq',
    'nuget',
];

interface ComposerFileAcceptRecord {
    fileOperate: {
        acceptedFiles: string[];
        rejectedFiles: string[];
    };
}

export interface Payload {
    query: string;
    knowledgeList: Knowledge[];
    conversationId?: number;
    extend: ComposerFileAcceptRecord;
    origin?: 'USER' | 'DEBUG';
    // 会话 ID ，Zulu拿Debug历史时用
    originConversationId?: string;
}

type MessageFromViewView =
    | {
        action: 'file-view' | 'file-reject' | 'file-accept' | 'copy';
        id: string;
        elementId: string;
        filePath: string;
    }
    | {
        action: 'file-accept-block' | 'file-reject-block';
        id: string;
        elementId: string;
        filePath: string;
        source: 'preview' | 'edit';
        acceptance: {
            addLines: number;
            removeLines: number;
        };
    }
    | {action: 'inquire-rollback-message', uuid: string}
    | {action: 'open-rollback-file-diff', absolutePath: string}
    | {action: 'rollback-message', uuid: string, selectedAbsolutePaths: string[]}
    | {action: 'file-accept-all' | 'file-reject-all' | 'regenerate-chat', id: string}
    | {action: 'execute-shell' | 'insert-shell', shell: 'string', id: string, elementId: string}
    | {action: 'tool-call-accept' | 'tool-call-reject', id: string, elementId: string};

export class E2EBotConversation extends ConversationThread<MessageFromViewView> {
    type: AgentConversationType = AgentConversationType.E2EBotConversation;
    inlineDiffView: boolean = true;
    status = AgentConversationStatus.Ready;
    private cancelTokenSource: Axios.CancelTokenSource = createAxiosCancelTokenSource();
    /* 现在只有 E2EBotConversation 才会有 mcpManager 能否改成 ConversationThread 默认带上呢 */
    readonly mcpManager: McpManager;

    constructor(context: ConversationContext, mcpManager: McpManager) {
        super(context, 'FULL_STACK_AGENT');
        this.mcpManager = mcpManager;
        this.status = AgentConversationStatus.Running;
    }

    async startWork(roundtrip: Roundtrip<Payload>): Promise<void> {
        const params = await this.injectExtendParamsIfPreviousMessageExisted(roundtrip.request.payload);
        await this.createConversation(roundtrip, params);
    }

    protected async injectExtendParamsIfPreviousMessageExisted(payload: Omit<Payload, 'extend'>): Promise<Payload> {
        const extend: ComposerFileAcceptRecord = {
            fileOperate: {
                acceptedFiles: [],
                rejectedFiles: [],
            },
        };

        // 将所有会话中的引用过的rule搜集起来，放在当前knowledgeList的最后面
        const ruleKnowledgeList: Knowledge[] = [];
        for (const roundtrip of this.roundtrips) {
            const payload = roundtrip.request.payload as Payload;
            const list = payload.knowledgeList ?? [];
            const rules = list.filter(knowledge => knowledge.type === ContextType.RULE);
            ruleKnowledgeList.unshift(...rules);
        }
        const knowledgeList = _.uniqBy([...payload.knowledgeList, ...ruleKnowledgeList], 'id');

        if (this.previousRoundtrip) {
            const previousRoundtrip = this.previousRoundtrip;
            const allComposerTasks = previousRoundtrip.getComposerTasks();
            // 多轮步骤1：把状态塞到参数里
            for (const task of allComposerTasks) {
                const key = task.toAcceptedLabel();
                if (task.accepted === AcceptState.ACCEPT && !extend.fileOperate.acceptedFiles.includes(key)) {
                    extend.fileOperate.acceptedFiles.push(key);
                }
                else if (task.accepted === AcceptState.REJECT && !extend.fileOperate.rejectedFiles.includes(key)) {
                    extend.fileOperate.rejectedFiles.push(key);
                }
            }
            // 多轮步骤2：把这一轮对话所有相关的文件都丢到knowledgeList里
            // for (const roundtrip of this.roundtrips) {
            //     const abstractedContextFiles: Knowledge[] = roundtrip
            //         .getComposerTasks()
            //         .map(task => ({
            //             type: 'FILE' as const,
            //             name: basename(task.filePath),
            //             id: task.filePath,
            //         }))
            //         .filter(task => task.id && existsSync(join(this.repo.rootPath, task.id)));
            //     payload.knowledgeList = uniqBy([...payload.knowledgeList, ...abstractedContextFiles], 'id');
            // }
            return {
                query: payload.query,
                conversationId: previousRoundtrip.context.conversationId as number,
                knowledgeList,
                extend,
            };
        }
        return {
            query: payload.query,
            knowledgeList,
            extend,
        };
    }

    recreateCancelTokenSource() {
        this.cancelTokenSource.cancel();
        this.cancelTokenSource = createAxiosCancelTokenSource();
        return this.cancelTokenSource;
    }

    private getMcpServers() {
        const mcpServers = this.mcpManager.getConnectionsForWebview();
        return mcpServers
            .filter(({status, disabled}) => status === 'connected' && !disabled)
            .map(({name, tools}) => ({
                serverName: name,
                tools: tools?.map(({name, description, inputSchema}) => ({name, description, inputSchema})),
            }));
    }

    async createConversation(roundtrip: Roundtrip<Payload>, params: Payload) {
        const cancelTokenSource = this.recreateCancelTokenSource();
        let analyzeResult: AnalyzeResult | null = null;
        roundtrip.status = RoundtripStatus.Analyzing;
        /* 在这里获取 mcpInfo 可以保证 analyze 和 chat 的数据一致，但是可能会有中间 MCP Server 挂掉的情况 */
        const mcpInfo = this.getMcpServers();
        try {
            // 1.通过anaylze接口创建对话
            // - 获取conversationId用于后端处理多轮
            // - 返回 generator 用于排队
            // - 此时对话的状态是 analyzing
            const chatQueueGenerator = await analyze({
                username: this.repo.username,
                query: params.query,
                cancelToken: cancelTokenSource.token,
                conversationId: params?.conversationId,
                slash: 'Composer',
                contexts: [],
                origin: params.origin ?? 'USER',
                originConversationId: params.originConversationId,
                mcpInfo,
            });
            for await (const chatQueue of chatQueueGenerator) {
                if (typeof chatQueue === 'string') {
                    roundtrip.appendTextResponse(chatQueue, {replace: true});
                }
                else {
                    roundtrip.pop();
                    analyzeResult = chatQueue;
                }
                roundtrip.updateWebviewMessages(true);
            }
        }
        catch (ex) {
            if (!(ex instanceof QueueCancelledError)) {
                throw ex;
            }
            return;
        }

        // 2. 对话状态从 analyzing 变成 generating
        roundtrip.status = RoundtripStatus.Generating;
        const {conversationId, extend, messageId} = analyzeResult!;
        // 3. 对话的uuid改为服务端的 userRequestId，用于做消息回滚时的唯一标识
        roundtrip.request.updateUuid(String(messageId));
        // 打印下 conversationId 方便测试，后面不要的话去掉
        this.logger.info('E2EBot start new roundtrip: ' + conversationId);
        // // 4. 把意图识别的 context 转化为普通的 context，实际 intent 参数未开启，没有使用到
        // const contextsSerializedFromIntent = intentContext2Knowledge(analyzeResult?.intentStrategies);
        const activeDocument = await this.virtualEditor.getActiveDocument();
        const {existed, selections, absolutePath} = activeDocument;
        const activeDocumentRelativePath = existed
            ? relative(
                this.repo.rootPath.replace(/\\/g, '/'),
                absolutePath.replace(/\\/g, '/')
            )
            : undefined;
        params.knowledgeList = transformKnowledges(
            params.knowledgeList,
            {relativePath: activeDocumentRelativePath}
        );
        roundtrip.context.conversationId = conversationId;

        // 5. 读取自定义规则的内容，并把里面的 #文件 提取出来
        const {ruleMentionKnowledge, ruleContent} = await this.getAppliedWorkspaceRules(params.knowledgeList);
        const contexts = transformContexts([...params.knowledgeList, ...ruleMentionKnowledge]);
        const codeChunks = await context2CodeChunks(contexts, activeDocument, this.repo.rootPath, this.virtualEditor);
        const sysInfo = await this.getSysInfo();
        const chatParams = {
            messageId,
            username: this.repo.username,
            query: params.query,
            contexts: excludeIllegalContexts(contexts),
            conversationId,
            codeChunks,
            extend: {...params.extend, ...extend},
            analyze: analyzeResult,
            sysInfo,
            cancelToken: this.cancelTokenSource.token,
            paths: [],
            customRules: ruleContent,
            mcpInfo,
        };
        // 第四步：调用chat接口，并处理流式输出
        await this.startChat(roundtrip, chatParams);
    }

    protected async startChat(roundtrip: Roundtrip<Payload>, chatParams: ChatParams) {
        await this.recursivelyRegenerate(roundtrip, chatParams, []);
    }

    async beforeStop() {
        if (this.cancelTokenSource) {
            if (this.latestRoundtrip.status === RoundtripStatus.Analyzing) {
                this.latestRoundtrip.appendTextResponse('您已主动终止本次会话。', {replace: true});
                this.latestRoundtrip.updateWebviewMessages(true);
            }
            this.cancelTokenSource.cancel();
        }
    }

    sanitizeDiffString(input: string) {
        const removeDiffLineAnnotation = (inputs: string[]) => inputs.filter(line => !/@@(.*)@@/.test(line));
        const lines = input.split(/\r?\n/);
        if (
            lines[0].startsWith('---')
            && lines[1].startsWith('+++')
        ) {
            return removeDiffLineAnnotation(lines.slice(2)).join('\n');
        }

        return removeDiffLineAnnotation(lines).join('\n');
    }

    async acceptLogOnEnd(from?: 'action' | 'end') {
        if (!this.latestRoundtrip) {
            return;
        }

        const suggestions = this.latestRoundtrip.updateAcceptSuggestions();
        if (suggestions.length) {
            if (from === 'end') {
                const reportedId = await this.generateMessageId(suggestions);
                this.logger.info('[Zulu] Roundtrip completed: uuid=', reportedId);
                this.latestRoundtrip.reportedId = reportedId;
            }

            this.logger.info('[Zulu] Track roundtrip completed: reportedId=', this.latestRoundtrip.reportedId);
            this.acceptLog(this.latestRoundtrip.reportedId, suggestions);
        }
        this.onAcceptLog(suggestions, from);
    }

    async onFileAcceptedChange(
        filePath: string,
        accepted: AcceptState,
        acceptance?: {addLines: number, removeLines: number}
    ) {
        if (!this.latestRoundtrip) {
            return;
        }

        const suggestions = this.latestRoundtrip.updateSuggestionsByPath(filePath, accepted, acceptance);
        if (suggestions.length) {
            this.logger.info('[Zulu] Track file accepted: reportedId=', this.latestRoundtrip.reportedId);
            this.acceptLog(this.latestRoundtrip.reportedId, suggestions);
        }
        this.onAcceptLog(suggestions, 'action');
    }

    protected async acceptLog(reportedId: string, data: MultiAcceptData[]) {
        // 如果没有 reportedId 证明不是正常结束后触发的，都不上报
        if (!reportedId) {
            return;
        }
        const multiSuggestions = {fileContent: data};
        await acceptCode({
            uuid: reportedId,
            accepted: true,
            content: '',
            multiSuggestions,
        });
    }

    async handleNewMessage(payload: MessageFromViewView) {
        switch (payload.action) {
            case 'execute-shell': {
                const {output} = await this.virtualEditor.executeTerminalShell({
                    cmd: payload.shell,
                    cwd: this.repo.rootPath,
                    duration: 5000,
                    run: true,
                });
                if (payload.id !== this.latestRoundtrip?.uuid) {
                    return;
                }
                // 检查output字符串中是否存在http(s?)://localhost:任意端口，或者ip+端口之类的地址
                const localhostAddress = extractLocalhostAddressFromOutput(output);
                if (localhostAddress) {
                    try {
                        const {port} = new URL(localhostAddress);
                        const portUsed = await isPortAvailable(Number(port));
                        if (portUsed) {
                            const textMessage = this.latestRoundtrip.getTextMessage(payload.elementId);
                            textMessage?.composer.updatePreview(localhostAddress);
                        }
                    }
                    catch (ex) {
                        this.logger.error('execute-shell failed, reason:', ex);
                    }
                }
                break;
            }
            case 'insert-shell': {
                await this.virtualEditor.executeTerminalShell({
                    cmd: payload.shell,
                    cwd: this.repo.rootPath,
                    duration: 5000,
                    run: false,
                });
                break;
            }
            case 'file-view': {
                const roundTrip = this.roundtrips.reduce<Roundtrip | null>(
                    (result, roundTrip) => {
                        if (result) {
                            return result;
                        }
                        const current = roundTrip.getTextMessage(payload.elementId)
                            || roundTrip.getToolCallMessage(payload.elementId);
                        return current ? roundTrip : null;
                    },
                    null
                );

                if (!roundTrip) {
                    return;
                }

                const isLatest = this.roundtrips.at(-1) === roundTrip;
                const tasks = roundTrip.getComposerTasks().filter(equalFilePathOrUniqKey(payload.filePath));
                const responsePayload = {
                    stream: false,
                    elementId: payload.elementId,
                };
                if (tasks.length === 1) {
                    const task = tasks[0];
                    if (isLatest) {
                        const {content} = await this.virtualEditor.getDocument({absolutePath: task.absolutePath});
                        await task.openDiff({
                            ...responsePayload,
                            content: task.originalContent || '',
                            modified: content,
                            inlineView: true,
                        });
                    }
                    else {
                        await task.openDiff({
                            stream: false,
                            content: task.originalContent || '',
                            modified: task.content,
                            inlineView: false,
                        });
                    }
                }
                else if (tasks.length > 1) {
                    const task = tasks[tasks.length - 1];
                    const originalContent = tasks[0]!.originalContent || '';
                    if (isLatest) {
                        const {content} = await this.virtualEditor.getDocument({absolutePath: task.absolutePath});
                        await task.openDiff({
                            ...responsePayload,
                            inlineView: true,
                            content: originalContent,
                            modified: content,
                        });
                    }
                    else {
                        const modifiedContent = tasks[tasks.length - 1]!.content;
                        await task.openDiff({
                            stream: false,
                            inlineView: false,
                            content: originalContent,
                            modified: modifiedContent,
                        });
                    }
                }
                break;
            }
            case 'file-accept': {
                const elementTasks = this.latestRoundtrip.getComposerTasks(payload.elementId);
                const tasks = elementTasks?.filter(equalFilePathOrUniqKey(payload.filePath)) || [];
                for (const task of tasks) {
                    await task.save();
                }
                this.onFileAcceptedChange(payload.filePath, AcceptState.ACCEPT);
                break;
            }
            case 'file-reject': {
                const elementTasks = this.latestRoundtrip.getComposerTasks(payload.elementId);
                const tasks = elementTasks?.filter(equalFilePathOrUniqKey(payload.filePath)) || [];
                for (const task of tasks) {
                    await task.revert();
                }
                this.onFileAcceptedChange(payload.filePath, AcceptState.REJECT);
                break;
            }
            case 'file-accept-block': {
                const elementTasks = this.latestRoundtrip.getComposerTasks(payload.elementId);
                const task = elementTasks.find(equalFilePathOrUniqKey(payload.filePath));
                task?.accept();
                break;
            }
            case 'file-reject-block': {
                const elementTasks = this.latestRoundtrip.getComposerTasks(payload.elementId);
                const task = elementTasks.find(equalFilePathOrUniqKey(payload.filePath));
                task?.reject();
                if (payload.source === 'edit') {
                    this.onFileAcceptedChange(payload.filePath, AcceptState.REJECT, payload.acceptance);
                }
                break;
            }
            case 'tool-call-accept': {
                const toolCallMessage = this.getToolMessage(payload.elementId);
                toolCallMessage?.workflow.acceptToRunWorkflow();
                break;
            }
            case 'tool-call-reject': {
                const toolCallMessage = this.getToolMessage(payload.elementId);
                toolCallMessage?.workflow.rejectToRunWorkflow();
                break;
            }
            case 'file-accept-all': {
                const tasks = this.latestRoundtrip?.getComposerTasks() || [];
                for (const task of tasks) {
                    if (task.completed) {
                        await task.save({openDocument: false});
                    }
                }
                this.acceptLogOnEnd('action');
                break;
            }
            case 'file-reject-all': {
                const tasks = this.latestRoundtrip?.getComposerTasks() || [];
                for (const task of tasks.reverse()) {
                    if (task.completed) {
                        await task.revert({openDocument: false});
                    }
                }
                this.acceptLogOnEnd('action');
                break;
            }
            case 'regenerate-chat': {
                const previous = this.latestRoundtrip;
                if (previous) {
                    super.startNewWork(previous.request.payload);
                }
                break;
            }
            case 'inquire-rollback-message': {
                const summary = await this.getRollbackSummary(payload.uuid);
                return summary.map(t => _.omit(t, 'changes'));
            }
            case 'rollback-message': {
                const response = await this.executeRollback(payload.uuid, payload.selectedAbsolutePaths);
                return response;
            }
            case 'open-rollback-file-diff': {
                await this.openRollbackDiff(payload.absolutePath);
            }
        }
    }

    rebuildConversation(payload: ChatSessionDetail) {
        this.rebuildRoundtrips(payload.messages);
    }

    protected async recursivelyRegenerate(
        roundtrip: Roundtrip<Payload>,
        params: ChatParams,
        workflowResults: WorkflowResult[]
    ) {
        const {paths, ...chatParams} = await this.updateDynamicChatParams(params, workflowResults);
        if (!roundtrip.inProgress) {
            return;
        }
        const chatStreamResponse = await chat(chatParams);
        let fullSummary = '';
        for await (const chunks of throttleAsyncIterator(chatStreamResponse)) {
            // 当前更新 webview 的事件太多了，有些 IDE 会很卡
            // 这里改成缓存一批 chunk 再处理，然后再更新一次 webview 视图，界面上看起来可能会一卡一卡的
            // chunks 大小由缓存时间控制，后面消息更新改成增量后可以适量把时间间隔调小一点
            for (const chunk of chunks) {
                const {type, detail, end} = chunk.content;
                const {reasoningSummary, reasoningDelta = '', reasoningEnd} = chunk.content.detail;
                // 根据接口返回结果决定是否增量
                if (reasoningSummary && !chunk.incremental) {
                    await roundtrip.appendReasonResponse(chunk.messageId, reasoningSummary, reasoningEnd, false);
                }
                if (chunk.incremental) {
                    await roundtrip.appendReasonResponse(chunk.messageId, reasoningDelta, reasoningEnd, true);
                }

                if (type === 'ANSWER') {
                    if (chunk.incremental) {
                        const summaryDelta = detail.delta ?? '';
                        fullSummary += summaryDelta;
                        roundtrip.appendTextResponse(summaryDelta);
                    }
                    else {
                        const newSummary = detail.summary ?? '';
                        const subString = newSummary.slice(fullSummary.length);
                        fullSummary = newSummary;
                        roundtrip.appendTextResponse(subString);
                    }
                    continue;
                }
                const toolCallEvents = detail.toolUse ?? [];
                for (const event of toolCallEvents) {
                    const {input: params, ...eventData} = event;

                    // await roundtrip.appendToolCallResponse(
                    //     {
                    //         eventType: type.replace('FUNCTION_CALL', 'TOOL_CALL') as ToolCallEventType,
                    //         params: {
                    //             server_name: 'sequential-thinking',
                    //             tool_name: 'sequentialthinking',
                    //             arguments: '{"thought":"10!等于几","nextThoughtNeeded":true,"thoughtNumber":1,"totalThoughts":3}',
                    //         },
                    //         ...eventData,
                    //         name: 'use_mcp_tool',
                    //     },
                    //     this.mcpManager!
                    // );
                    await roundtrip.appendToolCallResponse(
                        {
                            eventType: type.replace('FUNCTION_CALL', 'TOOL_CALL') as ToolCallEventType,
                            params,
                            ...eventData,
                        },
                        {
                            mcpManager: this.mcpManager,
                            requestInfo: {
                                messageId: chunk.messageId,
                                conversationId: chunk.conversationId,
                            },
                        }
                    );
                }
                if (end === true) {
                    break;
                }
            }

            roundtrip.updateWebviewMessages();
        }

        const newWorkflowResults = await roundtrip.resolveNewAddedMessages(paths);
        if (newWorkflowResults.length > 0) {
            // 由工具触发的请求都需要强制清空下 query
            const newParams = {
                ...params,
                messageId: undefined,
                query: '',
                contexts: [],
                codeChunks: [],
                knowledgeList: [],
            };
            await this.recursivelyRegenerate(roundtrip, newParams, newWorkflowResults);
        }
    }

    private aggregateRelativeFilePathsFromContext(codeChunks: CodeChunk[]) {
        const roundtrip = this.latestRoundtrip;
        if (!roundtrip) {
            return [];
        }

        const paths = new Set<string>();
        for (const codeChunk of codeChunks) {
            if (codeChunk.path && codeChunk.path.length >= 3) {
                paths.add(codeChunk.path);
            }
        }

        for (const msg of roundtrip.toolCallMessages) {
            for (const path of msg.workflow.paths) {
                if (path.length >= 3) {
                    paths.add(path);
                }
            }
        }
        return Array.from(paths);
    }

    private aggregateFilePathsFromKnowledgeList(knowledgeList: Knowledge[]) {
        const paths = new Set<string>();
        for (const knowledge of knowledgeList) {
            if (knowledge.type === ContextType.FILE && knowledge.id) {
                paths.add(knowledge.id);
            }
        }
        const toolCallMessages = this.getConversationToolCallMessages();
        for (const msg of toolCallMessages) {
            for (const path of msg.workflow.paths) {
                paths.add(path);
            }
        }
        return Array.from(paths);
    }

    private async updateDynamicChatParams(params: ChatParams, workflowResults: WorkflowResult[]) {
        // 每次请求的时候都需要更新下，因为上一次的请求可能会安装工具
        const {notInstalledCommands, installedCommands} = await filterSupportedCommand(
            COMMON_COMMANDS,
            params.sysInfo.defaultShell
        );
        const trackRepo = await getTraceRepoInfo(this.virtualEditor, this.repo.rootPath);
        const paths = this.aggregateRelativeFilePathsFromContext(params.codeChunks);

        return {
            ...params,
            toolUseResults: workflowResults,
            sysInfo: {...params.sysInfo, notInstalledCommands, installedCommands},
            ...trackRepo,
            paths,
            cancelToken: this.cancelTokenSource.token,
        };
    }

    private async getSysInfo() {
        const workspaceDirTree = await streamingListEntries(this.repo.rootPath);
        const baseSysInfo = getBaseSysInfo();
        const ideDefaultShell = kernel.env.ideTerminalInfo?.defaultShell;
        return {
            ...baseSysInfo,
            workspaceDirTree: workspaceDirTree.tree,
            workspacePath: this.repo.rootPath,
            defaultShell: ideDefaultShell ?? baseSysInfo.defaultShell,
        };
    }

    private async getAppliedWorkspaceRules(knowledgeList: Knowledge[]): Promise<{
        ruleMentionKnowledge: Knowledge[];
        ruleContent: string;
    }> {
        if (!this.repo.rootPath) {
            return {ruleMentionKnowledge: [], ruleContent: ''};
        }
        const rules = await getWorkspaceRules(this.repo.rootPath, this.virtualEditor);
        const files = this.aggregateFilePathsFromKnowledgeList(knowledgeList);
        const validRules = rules.filter(rule => filterActiveRule(rule, files));
        const ruleMentionKnowledge = await Promise.all(validRules.map(async rule => {
            const mentionFiles = await extractMentionFiles(rule.content ?? '', this.repo.rootPath);
            return mentionFiles.map(file => ({
                id: file,
                // RULE里引用的文件都当成普通文件处理，RULE是明确用户#的才算
                type: ContextType.FILE,
                name: file,
            }));
        }));
        const ruleContent = validRules.map(rule => addFilePatternSuffix(rule.metadata, rule.content)).join('\n');
        return {ruleMentionKnowledge: ruleMentionKnowledge.flat(), ruleContent};
    }

    protected onAcceptLog(_acceptData: MultiAcceptData[], _from?: 'action' | 'end') {
        //
    }
}
