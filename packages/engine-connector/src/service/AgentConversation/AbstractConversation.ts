import crypto from 'node:crypto';
import {
    AgentConversationStatus,
    AgentConversationType,
    AgentPayload,
    VirtualEditor,
    ChatSessionDetail,
    AgentTextElement,
    AgentToolCallElement,
    AgentMessage,
} from '@comate/plugin-shared-internals';
import {LoggerRoleInstance} from '../Logger.js';
import {generate, Model} from '../../api/generate.js';
import {parseRemoteUri} from '../../utils/track.js';
import {UserDetailForServer, UserDetailWithId} from '../User.js';
import {acceptCode, MultiAcceptData} from '../Track/api.js';
import {McpManager} from '../McpManager/index.js';
import {Repo} from './Zulu/types/knowledge.js';

export interface Message {
    id: string;
    status: 'inProgress' | 'success' | 'failed';
}

export interface UserMessage extends Message {
    role: 'user';
    content: string;
    // 提问时引用的代码块，渲染时用的是 markdown 组件，可以自定义其他内容
    code?: string;
    /** 原始的payload，用于重新生成对话 */
    payload: any;
}

export interface AssistantMessage<T = any> extends Message {
    role: 'assistant';
    content: T;
    conversationId?: number;
    elements: Array<AgentTextElement | AgentToolCallElement>;
}

export interface ConversationInfo {
    id: string;
    status: AgentConversationStatus;
    type: AgentConversationType;
}

export type UserDetail = UserDetailWithId & UserDetailForServer;

export type UpdatedMessageData = Partial<AgentMessage> & {id: string};

export type MessageUpdateOptions = {
    scope: 'conversation';
    messages: AgentMessage[];
} | {
    scope: 'message';
    message: AgentMessage;
} | {
    scope: 'elements';
    messageData: UpdatedMessageData;
};

type MessageChangeFn = (conversationId: string, messages: AgentMessage[], options?: MessageUpdateOptions) => void;

interface MessageResult {
    /** 处理过程的完整消息 */
    processedMessages: AgentMessage[];
    /** 发给 webview 的消息，字段会进行过滤 */
    webviewMessages: AgentMessage[];
}

export interface ConversationContext {
    repo: Repo;
    virtualEditor: VirtualEditor;
    onStatusChange: any;
    onMessageChange: MessageChangeFn;
    logger: LoggerRoleInstance;
    userDetail: UserDetailWithId & UserDetailForServer;
}

export abstract class AbstractConversation<MessageOperationPayload = any> {
    readonly logger: LoggerRoleInstance;
    private conversationId: string = '';
    readonly model: Model;
    readonly userDetail: UserDetail;
    protected readonly repo: Repo;
    protected readonly virtualEditor: VirtualEditor;
    protected readonly onStatusChange: any;
    protected readonly onMessageChange: MessageChangeFn;
    status: AgentConversationStatus = AgentConversationStatus.Ready;
    abstract type: AgentConversationType;
    ctime: number = Date.now();
    utime: number = Date.now();

    constructor(context: ConversationContext, model: Model) {
        const {repo, virtualEditor, onStatusChange, onMessageChange, logger, userDetail} = context;
        this.setConversationId(crypto.randomUUID()); // 使用 UUID 生成唯一的任务 ID
        this.model = model;
        this.logger = logger;
        this.userDetail = userDetail;
        this.repo = repo;
        this.virtualEditor = virtualEditor;
        this.onStatusChange = onStatusChange;
        this.onMessageChange = onMessageChange;
    }

    get id() {
        return this.conversationId;
    }

    setConversationId(id: string) {
        this.conversationId = id;
    }

    abstract onConversationMessage(agentPayload: AgentPayload<MessageOperationPayload>): Promise<void>;

    // 追加一轮，一轮表现为一个用户消息，同时启动智能体的逻辑开始处理
    // 第一轮默认会被调用，参数为用户的query或智能体自己写死
    abstract startNewWork(payload: MessageOperationPayload): Promise<void>;

    getConversationInfo() {
        return {
            id: this.id,
            status: this.status,
            type: this.type,
            utime: this.utime,
            ctime: this.ctime,
        };
    }

    setUtime(utime?: number) {
        this.utime = utime || Date.now();
    }

    updateStatus(status: AgentConversationStatus): void {
        this.status = status;
        this.onStatusChange(this.id);
        this.setUtime();
    }

    protected getGenerateParams(uuid?: string, multiSuggestions?: {fileContent: MultiAcceptData[]}) {
        return {
            uuid,
            repo: parseRemoteUri(this.repo.repoUrl || '').repository,
            branch: this.repo.branch,
            path: multiSuggestions?.fileContent?.[0]?.path ?? 'agent.java',
            // agent 是默认值，会在采纳时覆盖
            content: 'agent',
            model: this.model,
            multiSuggestions,
        };
    }

    private reportedGeneratedUuids = new Set<string>();
    protected async generateMessageId(suggestions?: MultiAcceptData[]) {
        try {
            const params = this.getGenerateParams(
                undefined,
                suggestions ? {fileContent: suggestions} : undefined
            );
            const uuid = await generate(params);
            this.reportedGeneratedUuids.add(uuid);
            return uuid;
        }
        catch (ex) {
            this.logger.error('generate message id error', (ex as Error).message);
            return crypto.randomUUID();
        }
    }

    protected async acceptLog(reportedId: string, data: MultiAcceptData[]) {
        // 如果没有 reportedId 证明不是正常结束后触发的，都不上报
        if (!reportedId) {
            return;
        }
        const multiSuggestions = {fileContent: data};
        const params = this.getGenerateParams(reportedId, multiSuggestions);
        // 保证采纳之前用generate上报multisuggestions
        await generate(params);
        await acceptCode({
            uuid: reportedId,
            accepted: true,
            content: '',
            multiSuggestions,
        });
    }

    abstract setForeground(foreground: boolean): void;
    abstract rebuildConversation(payload: ChatSessionDetail): void;
    abstract toMessages(): MessageResult;
    abstract toJSON(): any[];
}
