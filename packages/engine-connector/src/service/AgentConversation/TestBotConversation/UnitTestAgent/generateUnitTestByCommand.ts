import {join} from 'node:path';
import {spawn, exec, execSync} from 'node:child_process';
import type {ExecException, ChildProcessWithoutNullStreams, SpawnOptionsWithoutStdio} from 'node:child_process';
import os from 'node:os';
import {LoggerRoleInstance} from '../../../Logger.js';
import {UTAgentCLIResourceManager} from '../../../../api/UTAgentCLIResourceManager/index.js';
import {BultinErrorCode, BultinErrorMessage} from './index.js';
import {JsonRPC} from '../../../../utils/json.js';
import {sleep} from '../../../Composer/utils/index.js';

interface UTAgentBaseParams {
    // appRoot: string; // 待测应用根目录
    desiredCoverage?: number; // 期望达到的覆盖率 (default 100)
    exclude?: string; // 排除掉的文件夹或文件，多个输入间使用逗号分隔，例如：**/test/
    include?: string; // 待生成用例的文件夹或文件，为空时默认为workDir路径。支持通配符格式，多个输入用逗号分隔，例如：**/core/
    iterationMax?: number; // 运行迭代次数，选填。取值范围（0,10) (default 1)
    logPath?: string; // 日志文件路径，默认为当前目录 (default "logs/")
    runCmd?: string; // 环境编译指令
    runMode?: string; // UTAgent的运行模式 fast：快速模式，速度优先, exact：精确模式，准确度高，速度较慢 (default "fast")
    saveCaseStrategy?: string; // 用例保留策略 runPass: 保留运行通过的用例  compilePass: 保留编译通过的用例 (default "runPass")
    license?: string; // saas 版本需要输入license
    userName?: string; // 用户名
}

interface UTAgentGenParams extends UTAgentBaseParams {
    command: 'gen';
    workDir: string; // 可构建目录的绝对路径，仅支持一个输入
}

interface UTAgentMethodParams extends UTAgentBaseParams {
    command: 'method';
    workDir: string; // 可构建目录的绝对路径，仅支持一个输入
    startLine: number; // 指定方法起始行
    endLine: number; // 指定方法结束行
}

interface UTAgentDiffParams extends UTAgentBaseParams {
    command: 'diff';
    workDir: string; // 可构建目录的绝对路径，仅支持一个输入
    baseCommitID: string; // 增量生成单测的基准commitId，仅支持一个输入
}

interface ExecCommandOptions extends SpawnOptionsWithoutStdio {
    timeoutWait?: number;
}

export type UTAgentEnhanceGenParams = UTAgentDiffParams | UTAgentMethodParams | UTAgentGenParams;

let $subprocess: null | ChildProcessWithoutNullStreams = null;

// 子进程列表，key 是会话 id
const subprocessMap: Map<string, ChildProcessWithoutNullStreams> = new Map();

export function killSubprocess(messageId: string) {
    if (messageId && subprocessMap.has(messageId)) {
        const subprocess = subprocessMap.get(messageId);
        // kill -2, ctrl + c
        subprocess!.kill('SIGINT');
        return subprocess;
    }
}

// 文本超过下面的长度，再进行超长判断
const MIN_DETECOT_SIZE = 100;

// Logger.ts 对日志长度做了限制 10kb，因此可能无法正常查看
// 超长文本输出的合并
class LongString {
    chunkSlice: string[] = [];

    get content() {
        return this.chunkSlice.join('');
    }

    // 超长文本判断：依据json格式，如果解析失败则判定为超长文本
    is = (str: string) => {
        const result = (() => {
            try {
                return JsonRPC.findJsonRPC(str).parse().getParams();
            }
            catch (e) {
                return null;
            }
        })();

        if (str.length > MIN_DETECOT_SIZE && !result && !JsonRPC.isMoreContent(str)) {
            return true;
        }

        return false;
    };

    add(str: string) {
        this.chunkSlice.push(str);
    }

    reset() {
        this.chunkSlice.length = 0;
    }

    size() {
        return this.chunkSlice.length;
    }

    // 当前的数据未补充完整
    isInProcessing() {
        return this.size() && !(() => {
            try {
                // 能够解析
                return JsonRPC.isRealJson(JsonRPC.findJsonRPC(this.content).getFindedJsonString() ?? '');
            }
            catch {
                return false;
            }
        })();
    }
}

const resolveError = (errorMsg: string) => {
    let shellType = '';
    try {
        shellType = process.platform === 'win32'
            ? 'cmd'
            : execSync('echo $0').toString().trim();
    } catch (e) {
        shellType = 'unknown';
    }

    if (shellType === 'cmd') {
        if (errorMsg.includes('不是内部或外部命令') || errorMsg.includes('Not Recognized as')) {
            return {
                code: BultinErrorCode.LaunchError,
                message: BultinErrorMessage.ENOENT,
            };
        }
        else if (errorMsg.includes('拒绝访问') || errorMsg.includes('access denied')) {
            return {
                code: BultinErrorCode.LaunchError,
                message: BultinErrorMessage.EACCES,
            };
        }
    }
    // shell
    else if (shellType === '/bin/sh') {
        if (errorMsg.includes('No such file or directory')) {
            return {
                code: BultinErrorCode.LaunchError,
                message: BultinErrorMessage.ENOENT,
            };
        }
        else if (errorMsg.includes('Permission denied')) {
            return {
                code: BultinErrorCode.LaunchError,
                message: BultinErrorMessage.EACCES,
            };
        }
    }
    // unknown
    return {
        code: BultinErrorCode.UnknownError,
        message: errorMsg
    };
}

/**
 * spawn 进程执行命令
 */
async function* execCommand(
    messageId: string,
    command: string,
    args: string[],
    options?: ExecCommandOptions,
    logger?: LoggerRoleInstance
): AsyncGenerator<string> {
    const {timeoutWait, ...restOptions} = options || {};

    // 超时计时器
    let timeoutCounter: NodeJS.Timeout | undefined = undefined;

    const run = spawn(command, args, restOptions);
    const longString = new LongString();

    let finishSignal = false;

    let resolvor: ((value: string | PromiseLike<string>) => void) | null = null;
    let rejector: ((reason?: Error) => void) | null = null;

    let curContent = '';

    // 存在超长文本阶段的问题，长度跟操作系统有关
    // https://github.com/nodejs/help/issues/1265
    run.stdout.on('data', chunk => {
        // 更新计时器
        if (timeoutWait) {
            clearTimeout(timeoutCounter);
            timeoutCounter = setTimeout(() => {
                killSubprocess(messageId);
                rejector?.(new Error(BultinErrorMessage.Timeout));
            }, timeoutWait);
        }

        const data = chunk.toString();
        logger?.info('[UTAgent] Recive chunk from child process:', data);
        // 如果是超长文本，且拼接后不可用，则本次不 update
        if (longString.is(data)) {
            longString.add(data);
        }

        // 数据未补充完整时，不进行下一步
        if (longString.isInProcessing()) {
            logger?.info('[UTAgent] Not all long string');
            return;
        }

        // 要更新为文本，如果有超长文本，则更新超长文本; 且缓存
        curContent = longString.content || data;
        try {
            resolvor?.(curContent);
        }
        catch (e: any) {
            if (e.code === BultinErrorCode.RunningError) {
                rejector?.(e);
            }
        }
        finally {
            // 更新过的超长文本要重置
            longString.size() && longString.reset();
        }
    });

    run.stderr.on('data', chunk => {
        finishSignal = true;
        const chunkMsg = chunk.toString();
        logger?.info(`[UTAgent] Error: ${chunkMsg}`);
        const parsedError = resolveError(chunkMsg)
        const outputError: Error & {code?: BultinErrorCode} = new Error(parsedError.message);
        outputError.code = parsedError.code;
        rejector?.(outputError);
    });

    run.on('close', async () => {
        finishSignal = true;
        // 因为/AgentConversation/ConversationBase.ts 在 stop 情况下会有 500s 的时延判断，超时会丢弃，stop 场景是期望用 stop runningMsg 而不是 yield 数据，因为这里 yield 是进行中
        await sleep(500);
        // 最后一条是超长文本
        if (longString.size()) {
            const content = longString.content;
            longString.reset();

            resolvor?.(content);
        }
        else {
            resolvor?.(curContent);
        }
    });

    subprocessMap.set(messageId, run);

    if (timeoutWait) {
        timeoutCounter = setTimeout(() => {
            killSubprocess(messageId);
            rejector?.(new Error(BultinErrorMessage.Timeout));
        }, timeoutWait);
    }

    while (!finishSignal) {
        const value = await new Promise<string>((resolve, reject) => {
            resolvor = resolve;
            rejector = reject;
        });
        yield value;
    }
}

function buildUTAgentParams(params: UTAgentDiffParams | UTAgentMethodParams | UTAgentGenParams) {
    // 保证 command 必须第一个
    const {command, ...rest} = params;
    return Object.entries({command, ...rest}).map(([key, value]) => {
        if (key === 'command') {
            return `${value}`;
        }
        return key.length === 1
            ? `-${key} ${typeof value === 'number' ? value : `"${value}"`}`
            : `--${key} ${typeof value === 'number' ? value : `"${value}"`}`;
    });
}

function trimChar(str: string, charsToRemove?: string) {
    if (!charsToRemove) {
        return charsToRemove;
    }
    const regex = new RegExp(`^[${charsToRemove}]+|[${charsToRemove}]+$`, 'g');
    return str.replace(regex, '');
}

function mergeEnvPathVariable(osEnv: Record<string, string | undefined>, userEnv?: Record<string, string>) {
    return {
        ...userEnv,
        PATH: trimChar(`${(userEnv?.PATH ?? '') + ':' + (osEnv.PATH ?? '')}`, ':') || undefined,
    };
}

function addAuthority(binPath: string, logger?: LoggerRoleInstance) {
    return new Promise(resolve => {
        exec(`chmod +x ${binPath}`, (error, stdout) => {
            if (error) {
                logger?.error(`Add Authority failed in ${binPath}`, error);
            }
            resolve(stdout);
        });
    });
}

async function* generateUnitTestByCommand(
    messageId: string,
    params: UTAgentEnhanceGenParams,
    resourceManager: UTAgentCLIResourceManager,
    logger: LoggerRoleInstance,
    env?: Record<string, string>
) {
    const utAgentPath = resourceManager.resolveResource('UTAgent');
    if (!utAgentPath) {
        throw new Error('UTAgent not found');
    }
    const args = buildUTAgentParams(params);

    const command = join(utAgentPath, 'UTAgent');
    const commandLogPathArg = ['--logPath', `${os.tmpdir()}/comate-engine`];
    const outputArg = ['--trigger', 'IDE', '--saveCaseStrategy', 'canRun'];

    const newEnvrionment = mergeEnvPathVariable(process.env, env);

    logger.info(`Running command: ${command}  ${args.concat(commandLogPathArg, outputArg).join(' ')}`);
    logger.info(`Environment: ${JSON.stringify(newEnvrionment)}`);

    try {
        env?.MAVEN && await addAuthority(`${env.MAVEN}/bin/mvn`, logger);
        const stdout = await execCommand(
            messageId,
            command,
            args.concat(commandLogPathArg, outputArg),
            {shell: true, timeoutWait: 15 * 1000, env},
            logger
        );

        for await (const response of stdout) {
            yield response;
        }
    }
    catch (e) {
        const err = e as ExecException;
        logger.error(`Failed to run UT Agent: exit code ${err.code ?? '-'}, msg: ${err.message ?? '-'}`);
        throw e;
    }
}

export default generateUnitTestByCommand;
