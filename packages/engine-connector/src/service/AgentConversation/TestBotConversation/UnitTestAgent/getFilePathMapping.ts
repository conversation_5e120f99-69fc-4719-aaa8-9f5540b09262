import {WorkflowStatus} from '@comate/plugin-shared-internals';
import {FILE_NAME_SIGN_$, PREVIEW_FILE_CONTENT_TAG, UESABLE_FILE_CONTENT_TAG} from './contans.js';
import {createUtCodeSummary, getComposeUtFileResult} from './createUtFileContent.js';
import type {AgentResult, CommandAndEnv, TestbotBlockSection, UtFileResult} from './types.js';

export function createFirstGenerateTestbotContentMapping(result: AgentResult) {
    const mapping: Record<string, TestbotBlockSection> = {};
    if (result.process?.firstGenerate) {
        mapping[FILE_NAME_SIGN_$ + result.process.firstGenerate.testFilePath] = {
            content: result.process.firstGenerate.caseName?.join('\n'),
            code: result.process.firstGenerate.testFileContent,
            absolutePath: FILE_NAME_SIGN_$ + result.process.firstGenerate.testFilePath,
            status: result.process.firstGenerate.state === 'SUCCEED'
                ? WorkflowStatus.SUCCESS
                : WorkflowStatus.RUNNING,
        };
        mapping[PREVIEW_FILE_CONTENT_TAG + FILE_NAME_SIGN_$ + result.process.firstGenerate.testFilePath] = {
            content: result.process.firstGenerate.testFileContent,
            absolutePath: FILE_NAME_SIGN_$ + result.process.firstGenerate.testFilePath,
        };
    }

    return mapping;
}

export function createUtFileTestbotContentMapping(
    commandAndEnv: CommandAndEnv,
    utFileResultArray?: UtFileResult[],
    isPushUtFileResultArray?: boolean
) {
    const mapping: Record<string, TestbotBlockSection> = {};

    if (utFileResultArray && utFileResultArray.length) {
        (isPushUtFileResultArray ? getComposeUtFileResult(commandAndEnv) : utFileResultArray).forEach(
            (utFile: UtFileResult) => {
                const {testFileContent, testFileContentOnlySuccess} = utFile;
                const codeSummary = createUtCodeSummary(utFile, commandAndEnv);

                mapping[utFile.testFilePath] = {
                    content: codeSummary + (utFile.bugs ? `有${utFile.bugs.length}个疑似bug` : ''),
                    code: testFileContent,
                    absolutePath: utFile.testFilePath,
                    status: utFile.state === 'SUCCEED' ? WorkflowStatus.SUCCESS : WorkflowStatus.RUNNING,
                    extraBadge: {
                        text: '已验证',
                        style: 'warning',
                    },
                    buttons: utFile.bugs
                        ? [{
                            style: 'error',
                            buttonText: '查看疑似bug',
                            clickMethodMessages: utFile.bugs.map(bug => ({
                                title: `${bug.caseName}发现疑似BUG`,
                                content: bug.bug.bugMsg,
                            })),
                        }]
                        : undefined,
                };

                mapping[PREVIEW_FILE_CONTENT_TAG + utFile.testFilePath] = {
                    content: testFileContent,
                    absolutePath: utFile.testFilePath,
                };

                testFileContentOnlySuccess && (mapping[UESABLE_FILE_CONTENT_TAG + utFile.testFilePath] = {
                    content: testFileContentOnlySuccess,
                    absolutePath: utFile.testFilePath,
                });
            }
        );
    }

    return mapping;
}

export default (commandAndEnv: CommandAndEnv, parsedAgentResult: AgentResult, isPushUtFileResultArray?: boolean) => {
    const utFileTestbotContentMapping = createUtFileTestbotContentMapping(
        commandAndEnv,
        parsedAgentResult.utFileResult,
        isPushUtFileResultArray
    );
    const firstGenerateTestbotContentMapping = createFirstGenerateTestbotContentMapping(
        parsedAgentResult
    );
    const testbotMapping = {
        ...firstGenerateTestbotContentMapping,
        ...utFileTestbotContentMapping,
    };

    const filePathAndContentMapping = Object.entries(testbotMapping).reduce<Record<string, string>>(
        (prev, [filePath, testbot]) => ({
            ...prev,
            [filePath]: testbot.code
                ?? (Array.isArray(testbot.content) ? testbot.content.join('\n') : testbot.content),
        }),
        {}
    );

    for (const [key, testbot] of Object.entries(testbotMapping)) {
        // testbotMapping 去除冗余字段 code
        delete testbot.code;
        // 去除具有 tag 的 testbot 内容
        if (key.startsWith(PREVIEW_FILE_CONTENT_TAG) || key.startsWith(UESABLE_FILE_CONTENT_TAG)) {
            delete testbotMapping[key];
        }
    }

    return {testbotMapping, filePathAndContentMapping};
};
