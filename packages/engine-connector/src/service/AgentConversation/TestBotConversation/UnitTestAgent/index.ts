import {LoggerRoleInstance} from '../../../Logger.js';
import generateUnitTestByCommand, {UTAgentEnhanceGenParams, killSubprocess} from './generateUnitTestByCommand.js';
import {UTAgentCLIResourceManager} from '../../../../api/UTAgentCLIResourceManager/index.js';
import {JsonRPC} from '../../../../utils/json.js';
import {tryToWithDefaultNull} from './utils.js';
import {isInternal} from '../../../../utils/isSaaS.js';
import {AgentResult, ExecutePayload, UnitTestTriggerEntry, Reply, ReplyStatus, TestbotBlockSection} from './types.js';
import createUtFileContent, {deleteComposeUtFileResult} from './createUtFileContent.js';
import {
    createFirstGenerateContent,
    createAnalysisPanel,
    detectErrorMsg,
    getLoadingMsg,
    createSrcFileScanContent,
} from './createContents.js';
import createSummaryContent from './createSummaryContent.js';
import getFilePathMapping from './getFilePathMapping.js';
import {WorkflowStatus} from '../../../Composer/types.js';
import {SSEProcessor} from '@comate/plugin-shared-internals';
import {getUserGuide, UserGuideResponse} from '../../../../api/UTAgent.js';
import {sleep} from '../../../Composer/utils/index.js';

export enum BultinErrorMessage {
    Timeout = 'Command timeout',
    ENOENT = '资源引用异常',
    EACCES = '资源权限异常',
}

export enum BultinErrorCode {
    LaunchError = 'LaunchError',
    RunningError = 'RunningError',
    ParseError = 'parseError',
    UnknownError = 'UnknownError',
}

interface UserInfo {
    name: string;
    ide?: string;
    ideVersion?: string;
}

interface AgentStruct {
    stop: (messageId: string) => void;
    run: (payload: ExecutePayload, messageId: string) => AsyncGenerator<Reply, any, any>;
}

export class UnitTestAgent implements AgentStruct {
    private readonly logger: LoggerRoleInstance;
    private readonly userInfo: UserInfo;
    private readonly rootPath: string;
    private resourceManager: UTAgentCLIResourceManager | undefined = undefined;
    private reply: Reply = { // 只缓存了流程中的回复，既 status 只有 Finish 和 Process 两种状态
        status: ReplyStatus.Finish,
        content: '',
    };

    /**
     * @description 构造函数，初始化Logger实例。
     * @param {LoggerRoleInstance} logger - Logger实例。
     * @param {UserDetailWithId & {ide?: string}} userDetail - 用户详情对象，包含userId和可选的ide字段。
     */
    constructor(logger: LoggerRoleInstance, userInfo: UserInfo, rootPath: string) {
        this.logger = logger;
        this.userInfo = userInfo;
        this.rootPath = rootPath;
    }

    /**
     * 执行方法
     *
     * @param {entry: UnitTestTriggerEntry} entry - 触发入口点} 负载数据
     */
    run(payload: ExecutePayload, messageId: string) {
        if (payload.action === 'user-guide') {
            return this.getUserGuideContent(payload.query ?? '你是谁？');
        }
        switch (payload.entry) {
            case UnitTestTriggerEntry.EnhanceGenerate:
            case UnitTestTriggerEntry.BatchGenerate:
                return this.generateEnhanceUnitTestStream(messageId, payload.params, payload.env, payload.extra);
            default:
                return this.defaultReply();
        }
    }

    /**
     * 停止
     */
    stop(messageId: string) {
        const subprocess = killSubprocess(messageId);
        if (subprocess?.killed) {
            this.logger.info('Kill subprocess Successfully! pid = ', subprocess.pid);
        }
        return new Promise((resolve, reject) => {
            subprocess?.on('exit', () => {
                resolve(true);
            });
            subprocess?.on('error', () => {
                this.logger.error('Kill subprocess Error!');
                reject(false);
            });
        });
    }

    /**
     * 结束
     */
    end() {
        const finishedTestbotMapping: Record<string, TestbotBlockSection> = {};
        if (this.reply.testbotMapping) {
            for (const key in this.reply.testbotMapping) {
                finishedTestbotMapping[key] = {
                    ...this.reply.testbotMapping[key],
                    status: WorkflowStatus.SUCCESS,
                };
            }
        }
        this.reply = {
            ...this.reply,
            testbotMapping: finishedTestbotMapping,
            loadingMsg: '',
            status: ReplyStatus.Finish,
        };

        return this.reply;
    }

    /**
     * 默认回复，返回未找到相应能力的信息
     *
     * @returns {Promise<void>} Promise，一个空的回复结果
     */
    private async *defaultReply() {
        return {
            content: '未找到相应能力',
            status: ReplyStatus.Fail,
        };
    }

    /**
     * @description 初始化资源管理器
     * @throws {Error} 如果初始化过程中出现错误，则会记录在日志中并抛出异常
     */
    async initResourceManager() {
        try {
            const resourceManager = new UTAgentCLIResourceManager(
                {
                    // 不确定是否需要，暂时先这样
                    consumer: {name: this.userInfo.ide ?? '', version: this.userInfo.ideVersion},
                },
                this.logger
            );
            await resourceManager.initResource();
            this.resourceManager = resourceManager;
        }
        catch (e) {
            this.logger.error('Fail init resource manager error:', e);
        }
    }

    downloadResources() {
        return this.resourceManager?.loadResources();
    }

    /**
     * 检查可执行文件是否存在
     *
     * @returns 返回一个布尔值，表示可执行文件是否存在
     */
    async checkExecutable() {
        if (!this.resourceManager) {
            await this.initResourceManager();
        }
        // 需要将 maven java 和 gradle 在环境变量中注册好，因此不做判断

        // 二进制文件是否存在
        const executable = await this.resourceManager?.checkResourceExist();

        return !!executable;
    }

    async *generateEnhanceUnitTestStream(
        messageId: string,
        params: UTAgentEnhanceGenParams,
        env?: Record<string, string>,
        extraParams?: Record<string, any>
    ): AsyncGenerator<Reply, any, any> {
        const {license} = extraParams ?? {};

        if (!isInternal() && license) {
            params.license = license;
        }

        if (this.userInfo.name) {
            params.userName = this.userInfo.name;
        }

        const commandAndEnv = {
            ...params,
            ...env,
            messageId,
        };

        // TODO: 这里只判断了 method，为后续的命令扩展做准备，但是不确定是否需要
        const IS_PUSH_UT_FILE_RESULT_ARRAY = params.command !== 'method';

        try {
            if (!this.resourceManager) {
                throw new Error('Resource manager is not init');
            }

            try {
                this.downloadResources();
            }
            catch (e: any) {
                this.logger.error('[UTAgent] Fail to download resources', e.message ?? '');
            }

            yield {
                content: '',
                status: ReplyStatus.Process,
                loadingMsg: '正在启动，请稍候',
            };

            const response = await generateUnitTestByCommand(messageId, params, this.resourceManager, this.logger, env);
            for await (const chunk of response) {
                const parsedAgentResult = tryToWithDefaultNull<AgentResult>(
                    () => JsonRPC.findJsonRPC(chunk).parse().getParams(),
                    this.logger
                );
                if (!parsedAgentResult) {
                    this.logger.error('Parse agent result error');
                    continue;
                }
                const errorMsg = detectErrorMsg(parsedAgentResult);
                const srcScanContent = createSrcFileScanContent(parsedAgentResult.process);
                const firstGenerateContent = createFirstGenerateContent(parsedAgentResult.process);
                const utContent = createUtFileContent(
                    this.rootPath,
                    commandAndEnv,
                    parsedAgentResult.utFileResult,
                    IS_PUSH_UT_FILE_RESULT_ARRAY
                );
                const summaryContent = parsedAgentResult.status === 'SUCCEED'
                    ? createSummaryContent(parsedAgentResult.taskSummary, params.command)
                    : '';
                const content = srcScanContent + '\n' + firstGenerateContent + '\n' + utContent + '\n'
                    + summaryContent + '\n\n' + errorMsg;

                const {testbotMapping, filePathAndContentMapping} = getFilePathMapping(
                    commandAndEnv,
                    parsedAgentResult,
                    IS_PUSH_UT_FILE_RESULT_ARRAY
                );

                this.reply = {
                    content,
                    status: parsedAgentResult.status === 'RUNNING' ? ReplyStatus.Process : ReplyStatus.Finish,
                    fileListTitle: parsedAgentResult.process?.srcFileScan?.tableHeader,
                    loadingMsg: parsedAgentResult.process?.loadingMsg
                        ?? getLoadingMsg(parsedAgentResult, params.command),
                    startIntroduction: parsedAgentResult.process?.taskStart ?? '',
                    analysisPanel: createAnalysisPanel(parsedAgentResult.process),
                    testbotMapping,
                    filePathAndContentMapping,
                };

                // 会存在返回空消息，这里为了去掉空白显示，丢弃掉
                if (parsedAgentResult.status === 'RUNNING' && content.trim() === '') {
                    continue;
                }

                yield this.reply;
            }
        }
        catch (e: any) {
            this.logger.error('generate unit test error:', e.message ?? 'unknown error');

            // 终止进程
            this.stop(messageId);

            if (e.code === BultinErrorCode.LaunchError) {
                yield {
                    ...this.reply,
                    content: this.reply.content + '\n\n' + e.message,
                    status: ReplyStatus.Fail,
                    loadingMsg: '',
                };
                return;
            }

            if (e.message === BultinErrorMessage.Timeout) {
                yield {
                    ...this.reply,
                    content: this.reply.content + '\n\n' + '执行超时，请重试',
                    status: ReplyStatus.Fail,
                    loadingMsg: '',
                };
                return;
            }

            yield {
                ...this.reply,
                content: this.reply.content + '\n\n'
                    + (e.code === BultinErrorCode.RunningError ? e.message : '未知错误，任务异常'),
                status: ReplyStatus.Fail,
                loadingMsg: '',
            };
        }
        finally {
            deleteComposeUtFileResult(commandAndEnv);
        }
    }

    /**
     * 用户引导内容
     */
    async *getUserGuideContent(query: string): AsyncGenerator<Reply, void, unknown> {
        try {
            this.logger.info('[UTAgent] Get user guide', query);
            const response = await getUserGuide({query});
            const processor = new SSEProcessor<UserGuideResponse>(response.data);
            let steamingContent = '';
            for await (const chunk of processor.processSSE()) {
                steamingContent += chunk.msg;
                yield {
                    content: steamingContent,
                    status: ReplyStatus.Process,
                };
                await sleep(100);
            }
        }
        catch (e: any) {
            this.logger.error('[UTAgent] Fail to get intent', e.message ?? '');
            yield {
                content: `我是**Comate单测智能体**，您的IDE内置测试助手，专为提升代码质量而设计，具备以下核心功能：
1. **一键生成单测**：点击方法上的【生成单测✨】按钮，我将快速为该方法生成测试用例，帮助验证代码稳健性。
2. **智能覆盖变更**：在Comate侧边栏的对话框中输入“为代码变更生成单测”，我会分析git diff变动，为更新代码生成相应测试，确保每次更新都得以全面验证。
此外，我还会在测试执行时识别潜在缺陷并提供修复建议，让您的代码更强健！
3. **批量生成单测**：在目录树中右键点击【生成单测✨】按钮，我会为选中的文件或目录批量生成测试用例，让您摆脱繁琐的手动编写工作，将更多精力集中在核心业务上。
此外，我还会在测试执行时识别潜在缺陷并提供修复建议，让您的代码更强健！`,
                status: ReplyStatus.Finish,
            };
        }
    }
}
