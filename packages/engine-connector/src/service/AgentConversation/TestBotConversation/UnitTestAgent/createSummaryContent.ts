import type {TaskSummary} from './types.js';
import type {UTAgentEnhanceGenParams} from './generateUnitTestByCommand.js';

export default function summaryTplString(
    taskSummary: TaskSummary | undefined,
    cmd: UTAgentEnhanceGenParams['command']
) {
    const {
        coverage,
        generatedTestMethodCount,
        oldCoverage,
        processedSrcMethodCount,
        succeedSrcMethodCount,
        srcMethodCount,
        increaseRate,
        incCoverageIncreaseRate,
    } = taskSummary ?? {};

    if (!taskSummary) {
        return '';
    }

    // 区分单方法和多方法
    switch (cmd) {
        case 'method':
            return increaseRate && increaseRate !== '0.00%'
                // 有效的单方法
                // ，当前行覆盖率${coverage} 暂时删除
                ? `已为当前被测方法生成${generatedTestMethodCount}个可执行通过的单测用例，采纳后被测方法的行覆盖率提升${increaseRate}`
                // 无效的单方法
                : '用例验证已完成，但未生成更优结果，建议采纳当前用例并手动优化';
        case 'gen':
            return coverage && coverage !== '0.00%'
                    && parseFloat(coverage) > parseFloat(oldCoverage!)
                // 有效多方法
                ? `共检测到${srcMethodCount}个被测方法`
                    + `${
                        succeedSrcMethodCount
                            ? `${srcMethodCount ? '，' : ''}已为${succeedSrcMethodCount}个被测方法${
                                generatedTestMethodCount ? '' : '生成单测用例'
                            }`
                            : ''
                    }`
                    + `${
                        generatedTestMethodCount
                            ? `${srcMethodCount ? '，' : ''}生成${generatedTestMethodCount}个可执行单测用例`
                            : ''
                    }`
                    + `${
                        incCoverageIncreaseRate && incCoverageIncreaseRate !== ''
                            ? `${srcMethodCount ? '，' : ''}采纳后行覆盖率提升 ${incCoverageIncreaseRate}`
                            : ''
                    }`
                // 无效多方法
                : `${
                    processedSrcMethodCount
                        ? `已尝试为${processedSrcMethodCount}个被测方法生成单测用例`
                        : ''
                }`
                    + '未生成出有效用例，可继续编辑代码后尝试重新生成';
        case 'diff':
            return coverage && coverage !== '0.00%'
                    && parseFloat(coverage) > parseFloat(oldCoverage!)
                // 有效多方法
                ? `共检测到${srcMethodCount}个被测方法`
                    + `${
                        succeedSrcMethodCount
                            ? `${srcMethodCount ? '，' : ''}已为${succeedSrcMethodCount}个被测方法${
                                generatedTestMethodCount ? '' : '生成单测用例'
                            }`
                            : ''
                    }`
                    + `${
                        generatedTestMethodCount
                            ? `${srcMethodCount ? '，' : ''}生成${generatedTestMethodCount}个可执行单测用例`
                            : ''
                    }`
                    + `${
                        incCoverageIncreaseRate && incCoverageIncreaseRate !== ''
                            ? `${srcMethodCount ? '，' : ''}采纳后变更行覆盖率提升 ${incCoverageIncreaseRate}`
                            : ''
                    }`
                // 无效多方法
                : `${
                    processedSrcMethodCount
                        ? `已尝试为${processedSrcMethodCount}个被测方法生成单测用例`
                        : ''
                }`
                    + '未生成出有效用例，可继续编辑代码后尝试重新生成';
        default:
            return `共检测到${srcMethodCount}个被测方法`
                + `${
                    processedSrcMethodCount
                        ? `${srcMethodCount ? '，' : ''}已为${processedSrcMethodCount}个被测方法生成单测用例`
                        : ''
                }`
                + `${
                    generatedTestMethodCount
                        ? `${srcMethodCount ? '，' : ''}生成${generatedTestMethodCount}个单测用例`
                        : ''
                }`
                + `${coverage && coverage !== '0.00%' ? `${srcMethodCount ? '，' : ''}行覆盖率${coverage}` : ''}`;
    }
}
