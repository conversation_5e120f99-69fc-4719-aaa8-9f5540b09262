import {WorkflowStatus} from '../../../Composer/types.js';
import type {UTAgentEnhanceGenParams} from './generateUnitTestByCommand.js';

export type CommandAndEnv = Record<string, string | number>;

type AnyType = any;

export enum ReplyStatus {
    Process = 'inProgress',
    Finish = 'success',
    Fail = 'failed',
}

export interface Reply {
    status: ReplyStatus;
    fileListTitle?: string;
    startIntroduction?: string; // 转发内容
    analysisPanel?: {
        status: 'inProgress' | 'success';
        content: string[];
    }; // 分析内容
    content: string; // composer markdown 内容
    loadingMsg?: string; // 加载中文案
    filePathAndContentMapping?: Record<string, string>; // 文件路径和内容映射
    testbotMapping?: Record<string, TestbotBlockSection>; // testbot 标签内容内容
}

export enum AgentType {
    UTAgent = 'ut-agent',
}

export enum UnitTestTriggerEntry {
    EnhanceGenerate = 'enhance-generate',
    BatchGenerate = 'batch-generate',
    StopGenerate = 'stop-generate',
}

interface BaseAgentParams {
    agentId: AgentType;
}

interface UTAgentParams extends BaseAgentParams {
    agentId: AgentType.UTAgent;
    entry?: UnitTestTriggerEntry;
}

export type AgentParams<T extends AgentType> = T extends AgentType.UTAgent ? UTAgentParams
    : AnyType;

export interface UtFileResult {
    state: 'RUNNING' | 'SUCCEED';
    srcFilePath: string; // 被测文件路径
    testFilePath: string; // 单测文件路径
    testFileContent: string; // 单测用例内容
    testFileContentOnlySuccess: string; // 仅包含成功的单测用例内容
    meta?: {
        srcMethodCount: number; // 被测方法数
        generatedTestMethodCount: number; // 生成的被测方法数
        fileCoverage: string; // *%，覆盖率
        fileOldCoverage: string; // *%，原覆盖率
    };
    bugs?: Array<{ // 用例疑似bug
        caseName: string; // 用例名
        startLine: number;
        endLine: number;
        bug: {
            bugFocusline: number;
            bugMsg: string;
        };
    }>;
}

export interface TaskSummary {
    srcMethodCount: number;
    processedSrcMethodCount: number;
    succeedSrcMethodCount: number;
    generatedTestMethodCount: number;
    coverage: string; // *%
    oldCoverage: string; // *%
    increaseRate: string; // *%
    incCoverageIncreaseRate: string; // *%
}

export interface GenerateProcess {
    taskStart: string;
    loadingMsg?: string;
    srcFileScan?: { // 扫描的文件，单方法结构体时，为 null
        state: 'RUNNING' | 'SUCCEED';
        srcFilesName: string[];
        summary: string;
        tableHeader?: string;
    };
    firstGenerate?: { // 首次生成，先展示
        state: 'RUNNING' | 'SUCCEED';
        testFilePath: string; // 单测文件路径用于跳转
        testFileContent: string;
        caseName: string[];
        summary: string;
    };
    envCheck?: {
        envState: 'USABLE' | 'UNUSABLE' | 'RUNNING'; // 验证中 RUNNING，环境可用 USABLE、环境不可用 UNUSABLE
        errorMsg: {
            msg: string;
            repairSuggest: string;
        };
    };
}

export interface AgentResult {
    process?: GenerateProcess;
    utFileResult?: UtFileResult[];
    taskSummary?: TaskSummary;
    message: string; // 其中 SKIPPED 显示的正确的 message 信息；FAILED 和 ABORT 显示的错误信息，这些错误信息和 plan.errorMsg 相同
    status: 'RUNNING' | 'SUCCEED' | 'FAILED' | 'SKIPPED' | 'ABORT';
}

export interface ExecutePayload {
    entry: UnitTestTriggerEntry;
    params: UTAgentEnhanceGenParams;
    env: Record<string, string>;
    extra?: Record<string, any>;
    action?: 'user-guide' | string; // 进入用户引导功能，请求单测服务获取文案
    query?: string; // 用户输入内容
}

export interface TestbotBlockSection {
    absolutePath: string;
    content: string | string[];
    code?: string;
    status?: WorkflowStatus;
    uuid?: string; // 用来数据统计
    extraBadge?: {
        text: string;
        style: 'warning' | 'error' | 'default';
    };
    buttons?: Array<{
        style: 'error' | 'warning' | 'default';
        buttonText: string;
        clickMethodMessages: Array<{
            title: string;
            content: string;
        }>;
    }>;
}
