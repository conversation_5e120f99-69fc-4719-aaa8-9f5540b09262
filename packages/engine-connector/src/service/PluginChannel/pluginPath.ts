import fs from 'node:fs/promises';
import path from 'node:path';
import {fileURLToPath} from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export const PLUGIN_PATH = path.resolve(__dirname, process.env.PLUGIN_PATH || '../../plugins');

const DefaultEnabledPluginKeys = ['security'];

/**
 * 返回enabled状态的plugin路径
 * @param allConfig
 * @returns
 */
export async function getEnabledPluginPaths(allConfig: Record<string, {enabled: boolean}>) {
    const pluginNames = await fs.readdir(PLUGIN_PATH);
    const allPluginDirectories = pluginNames.map(name => path.resolve(PLUGIN_PATH, name));
    const enabledPluginKeys = Object.keys(allConfig).filter(key => allConfig[key].enabled);
    const pluginKeys = new Set([...enabledPluginKeys, ...DefaultEnabledPluginKeys]);
    const pluginDirectories = allPluginDirectories.filter(
        pluginDirectory => pluginKeys.has(path.basename(pluginDirectory))
    );
    return pluginDirectories;
}
