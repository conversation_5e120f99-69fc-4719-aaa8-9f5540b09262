// import crypto from 'node:crypto';
import {ChildProcess} from 'node:child_process';
import {
    ChannelGroupManager,
    PluginSetupContext,
    PluginChannel,
    PluginGroup,
    forkPluginProcess,
    invokePluginSetupToProcess,
    PluginGroupValue,
} from '@comate/plugin-engine';
import {LoggerRoleInstance} from '../Logger.js';
import {Mediator} from '../Mediator.js';
import {URI} from 'vscode-uri';
// import {ACTION_START_BACKGROUND_SERVICE} from '@comate/plugin-shared-internals';
import {PluginMetas} from '../PluginConfig/pluginMeta.js';
import {isInternal} from '../../utils/isSaaS.js';
import {isDevMod, isVscode} from '../../utils/checkIDE.js';

const MAX_RETRY = 5;

export interface ChildProcessGroupValue extends PluginGroupValue {
    process: ChildProcess;
    failRetry: number;
}

export class EngineProcessChannelGroupManager extends ChannelGroupManager<ChildProcessGroupValue> {
    private readonly logger: LoggerRoleInstance;
    private readonly mediator: Mediator;

    constructor(logger: LoggerRoleInstance, mediator: Mediator) {
        super();
        this.logger = logger;
        this.mediator = mediator;
    }

    protected getGroupKey(context: PluginSetupContext): string {
        // 要监听调试的时候，它必须是一个独立进程，不然挂不上调试器
        if (context.inspectPort) {
            return context.description.name;
        }

        // 特殊需要单独进程的可以做以下处理
        const pluginMeta = PluginMetas[context.description.name];
        return pluginMeta?.independentProcess ? context.description.name : 'cluster';
    }

    protected createGroupValue(groupKey: string, inspectPort?: number) {
        const child = forkPluginProcess(
            groupKey,
            {
                nodeOptions: inspectPort ? [`--inspect=${inspectPort}`] : [],
                onPluginSetup: pluginName => {
                    const group = this.groups.get(groupKey);

                    if (!group) {
                        return;
                    }

                    const plugin = group.plugins.get(pluginName);
                    plugin?.onSetup?.(group.value.channel);
                    isInternal() && this.startPrivilegedBackgroundService(pluginName);
                },
                onUnexpectedExit: (code: number) => {
                    try {
                        const group = this.groups.get(groupKey);

                        if (!group) {
                            return;
                        }

                        for (const plugin of group.plugins.values()) {
                            plugin.onExit?.();
                        }

                        this.recover(groupKey);

                        isDevMod
                            ? this.logger.error(
                                'GroupProcessonError',
                                `${groupKey} onUnexpectedExit: ${code}`
                            )
                            : this.logger.logUploader?.logError(
                                'GroupProcessonUnexpectedExit',
                                `${groupKey} onUnexpectedExit: ${code}`
                            );
                    }
                    catch (ex) {
                        isDevMod
                            ? this.logger.error(
                                'GroupProcessonError',
                                `${groupKey} onUnexpectedExit: ${code}, ex: ${ex}`
                            )
                            : this.logger.logUploader?.logError(
                                'GroupProcessonError',
                                `${groupKey} onUnexpectedExit: ${code}, ex: ${ex}`
                            );
                    }
                },
                // 只打日志就好 exit处理了this.recover 不再重复启动
                onUnexpectedError: (error: Error) => {
                    isDevMod
                        ? this.logger.error(
                            'GroupProcessonErrorUnexpectedError',
                            `${groupKey} onUnexpectedError: ${error}`
                        )
                        : this.logger.logUploader?.logError(
                            'GroupProcessonErrorUnexpectedError',
                            `${groupKey} onUnexpectedError: ${error}`
                        );
                },
            }
        );
        const pluginPid: number | undefined = child.pid;
        this.logger.info('pluginPid', groupKey, pluginPid);
        if (isVscode) {
            this.mediator.sendToIde('PLUGINS_PID_CHANGE_MESSAGE', {
                data: {name: groupKey, pid: pluginPid},
            });
        }

        const channel = new PluginChannel(child);
        // 已经有catch 和 onUnexpectedError 的处理
        // TODO 考虑本地开本地文件日志，线上关闭本地
        // child.stdout?.on('data', data => this.logger.verbose('stdout: ', data.toString()));
        // child.stderr?.on('data', error => this.logger.error('stderr: ', error.toString()));
        channel.start();
        return {
            failRetry: 0,
            process: child,
            channel,
        };
    }

    protected destroyGroup(group: PluginGroup<ChildProcessGroupValue>) {
        group.value.process.kill();
    }

    protected async onPluginJoinGroup(group: PluginGroup<ChildProcessGroupValue>, setupContext: PluginSetupContext) {
        await invokePluginSetupToProcess(
            group.value.process,
            {
                name: setupContext.description.name,
                directory: setupContext.directory,
            }
        );
    }

    protected async onPluginExitGroup() {
        return Promise.resolve();
    }

    private isRecoverable(group: PluginGroup<ChildProcessGroupValue>) {
        return group.plugins.size > 0 && group.value.failRetry <= MAX_RETRY;
    }

    private recover(groupKey: string) {
        const current = this.groups.get(groupKey);

        if (current && this.isRecoverable(current)) {
            current.value.failRetry++;
            const group = this.createGroup(groupKey, current.inspectPort);
            this.groups.set(groupKey, group);
        }
    }

    private async startPrivilegedBackgroundService(pluginName: string) {
        const pluginMeta = PluginMetas[pluginName];
        // 下线安全后台扫描，保留后台执行逻辑看之后需求
        // if (pluginMeta?.enableBackgroundService) {
        //     const payload = {
        //         pluginName,
        //         systemInfo: {
        //             userId: userDetail.uid,
        //             userDetail: {
        //                 name: userDetail.name,
        //                 displayName: userDetail.displayName,
        //                 email: userDetail.email,
        //             },
        //             cwd,
        //         },
        //     };
        //     const sessionId = crypto.randomUUID();
        //     this.mediator.launchSendToPlugin(
        //         sessionId,
        //         {action: ACTION_START_BACKGROUND_SERVICE, payload}
        //     );
        // }
        if (pluginMeta?.enableDiagnostic) {
            this.mediator.diagnosticCache.getGray().then(async (scanEnabled: boolean) => {
                const userDetail = this.mediator.userDetail();
                const workspaceFolders = await this.mediator.getWorkspaceFolders();
                const workspaceFolder = workspaceFolders?.at(0)?.uri;
                const cwd = workspaceFolder ? URI.parse(workspaceFolder).fsPath : '';
                const payload = {
                    pluginName,
                    systemInfo: {
                        userId: userDetail.uid,
                        userDetail: {
                            name: userDetail.name,
                            displayName: userDetail.displayName,
                            email: userDetail.email,
                        },
                        cwd,
                    },
                    capabilityName: pluginMeta.diagnosticCapabilityName,
                    diagnosticScanId: Date.now().toString(),
                };
                if (scanEnabled) {
                    this.mediator.diagnosticCache.start(payload);
                }
            });
        }
    }
}
