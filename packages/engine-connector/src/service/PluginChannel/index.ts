/* eslint-disable complexity */
import {EventEmitter} from 'node:events';
import {EngineInit, PluginEngine} from '@comate/plugin-engine';
import {
    ACTION_CHAT_TASK_PROGRESS,
    ACTION_LOG,
    ACTION_SESSION_FINISH,
    ACTION_ASK_LLM,
    ACTION_GET_PLUGI<PERSON>_CONFIG,
    ACTION_REQUEST_PERMISSION,
    ACTION_INFORMATION_QUERY,
    TaskProgressPayload,
    FunctionDefinition,
    LlmType,
    InformationQueryType,
    KnowledgeSetBase,
    KnowledgeOptions,
    ACTION_COMATE_PLUS_DRAW_CHAT_FINISH,
    TextModel,
    Execution,
    ACTION_ASK_LLM_STREAMING,
    QuerySelectorPayload,
    PermissionType,
    ModelOptions,
    ACTION_DIAGNOSTIC_SCAN_TASK_PROGRESS,
    DiagnosticScanTaskProgressPayload,
    ACTION_SECUBOT_TASK_PROGRESS,
    ACTION_DEBUG_TASK_PROCESS,
    DebugAgentPayload,
    ACTION_ASK_RAG,
    ACTION_CODE_SEARCH,
    ACTION_COMATE_PLUS_CODE_SEARCH,
    ACTION_BATCH_ACCEPT,
    ACTION_COMATE_PLUS_BATCH_ACCEPT,
    ACTION_DIAGNOSTIC_SCAN_TASK_COUNT,
    ACTION_ISCAN_RESULT,
    ACTION_ISCAN_JOB_BUILD_ID,
    CodeSearchPayload,
    ACTION_COMATE_PLUS_RECREATE_INDEX,
    ACTION_SA_SCAN_DIAGNOSTIC_RESULT,
} from '@comate/plugin-shared-internals';
import {LoggerRoleInstance} from '../Logger.js';
import {Mediator} from '../Mediator.js';
import {EventQueue} from './EventQueue.js';
import {getEnabledPluginPaths} from './pluginPath.js';
import {isJetbrains} from '../../utils/checkIDE.js';
import {sendMessageToIde} from '../IdeChannel/ideListener.js';
import {EngineProcessChannelGroupManager} from './group.js';
import {extractCodeBlocks} from '../../utils/markdown.js';
import {AutoDebugFixAgent} from '../AgentConversation/DebugBotConversation/AutoDebugFixAgent.js';
import {isSaaS} from '../../utils/isSaaS.js';

// TODO 不同的 action 有不同的 payload
interface Payload extends TaskProgressPayload {
    level?: string;
    content?: string;
    pluginName?: string;
    type?: string | number;
    functions?: FunctionDefinition[];
    promptTemplate?: string;
    args?: Record<string, unknown>;
    code?: string;
    filePath?: string;
    key?: string;
    query?: string;
    informationList: KnowledgeSetBase[];
    options?: KnowledgeOptions;
    model?: TextModel;
    modelOptions?: ModelOptions;
    debugAgentPayload?: DebugAgentPayload;
    codeSearchPayload?: CodeSearchPayload;
}

export interface MessageEvent {
    messageId?: number;
    sessionId: string;
    execution?: Execution;
    data: {
        payload: Payload;
        action:
            | typeof ACTION_CHAT_TASK_PROGRESS
            | typeof ACTION_LOG
            | typeof ACTION_SESSION_FINISH
            | typeof ACTION_REQUEST_PERMISSION
            | typeof ACTION_ASK_LLM
            | typeof ACTION_ASK_LLM_STREAMING
            | typeof ACTION_GET_PLUGIN_CONFIG
            | typeof ACTION_INFORMATION_QUERY
            | typeof ACTION_DIAGNOSTIC_SCAN_TASK_PROGRESS
            | typeof ACTION_DIAGNOSTIC_SCAN_TASK_COUNT
            | typeof ACTION_SECUBOT_TASK_PROGRESS
            | typeof ACTION_DEBUG_TASK_PROCESS
            | typeof ACTION_ASK_RAG
            | typeof ACTION_CODE_SEARCH
            | typeof ACTION_BATCH_ACCEPT
            | typeof ACTION_ISCAN_RESULT
            | typeof ACTION_ISCAN_JOB_BUILD_ID
            | typeof ACTION_SA_SCAN_DIAGNOSTIC_RESULT;
    };
}

export class PluginChannel {
    private readonly events: EventEmitter;
    private readonly eventQueues: Map<string, EventQueue<MessageEvent>>;
    private readonly logger: LoggerRoleInstance;
    private readonly mediator: Mediator;
    private engine: PluginEngine | undefined = undefined;

    constructor(mediator: Mediator, logger: LoggerRoleInstance) {
        this.mediator = mediator;
        this.logger = logger;
        this.events = new EventEmitter();
        this.eventQueues = new Map();
    }

    async init() {
        const localConfig = await this.mediator.getLocalConfig();
        const allConfig = await this.mediator.getPluginConfigAll();
        const pluginDirectories = await getEnabledPluginPaths(allConfig);

        const init: EngineInit = {
            channelGroupManager: new EngineProcessChannelGroupManager(this.logger, this.mediator),
            pluginDirectories,
            channel: this.channel(),
            localConfig,
        };
        const engine = new PluginEngine(init);
        await engine.start();
        this.engine = engine;
    }

    // 发送给插件，不需要等待返回
    launchSend(sessionId: string, data: any, execution?: Execution) {
        this.events.emit('message', {sessionId, data, execution});
    }

    // TODO 类型约束
    async *send(sessionId: string, data: any, execution?: Execution) {
        this.events.emit('message', {sessionId, data, execution});
        const eventQueue = new EventQueue<MessageEvent>();
        this.eventQueues.set(sessionId, eventQueue);
        eventQueue.setExtraData(data);
        for await (const task of eventQueue) {
            yield task;
        }
    }

    // TODO 还需要一个可以返回迭代器的方法
    sendToIde = async (method: string, message: MessageEvent) => {
        if (!this.mediator) {
            throw new Error('ideChannel is undefined');
        }
        return this.mediator.sendToIde(method, message);
    };

    getPluginCapabilityInfo() {
        return this.engine?.listAllCapabilities() ?? [];
    }

    getSuggestCapabilitiesByMatch(payload: QuerySelectorPayload) {
        return this.engine?.suggestCapabilitiesByMatch(payload);
    }

    channel() {
        return {
            on: (eventName: string, listener: (...args: any[]) => void) => {
                this.events.on(eventName, listener);
            },
            // eslint-disable-next-line max-statements
            send: async (message: MessageEvent) => {
                const {sessionId, data: {payload, action}} = message;
                switch (action) {
                    case ACTION_CHAT_TASK_PROGRESS: {
                        this.eventQueues.get(sessionId)?.push(message);
                        isJetbrains && await sendMessageToIde(this.logger, this.mediator, message);
                        return;
                    }
                    case ACTION_SECUBOT_TASK_PROGRESS: {
                        this.eventQueues.get(sessionId)?.push(message);
                        // isJetbrains && await sendMessageToIde(this.logger, this.mediator, message);
                        return;
                    }
                    case ACTION_DIAGNOSTIC_SCAN_TASK_PROGRESS: {
                        this.mediator.diagnosticCache.put(
                            (payload as unknown as DiagnosticScanTaskProgressPayload).chunk
                        );
                        return;
                    }
                    case ACTION_DIAGNOSTIC_SCAN_TASK_COUNT:
                    case ACTION_ISCAN_RESULT:
                    case ACTION_ISCAN_JOB_BUILD_ID:
                    case ACTION_SA_SCAN_DIAGNOSTIC_RESULT: {
                        this.eventQueues.get(sessionId)?.push(message);
                        return;
                    }
                    case ACTION_LOG:
                        this.logger.info(payload);
                        return;
                    case ACTION_SESSION_FINISH:
                        const eventQueue = this.eventQueues.get(sessionId);
                        eventQueue?.stop();
                        this.eventQueues.delete(sessionId);
                        if (isJetbrains) {
                            const data = eventQueue?.getExtraData();
                            this.mediator.sendToIde(ACTION_COMATE_PLUS_DRAW_CHAT_FINISH, {
                                messageId: data.payload.input.messageId,
                            });
                        }
                        // 准备下线助理模式
                        // this.sendToIde(ACTION_SESSION_FINISH, message);
                        return;
                    case ACTION_REQUEST_PERMISSION: {
                        let data;
                        const {enterpriseConfig} = this.mediator.userDetail();
                        // 获取代码安全权限
                        if (message.data.payload.type === PermissionType.CodeSecurity) {
                            data = {
                                payload: {
                                    ...message.data.payload,
                                    granted: !!enterpriseConfig?.enableCodeSecurity,
                                },
                                action: message.data.action,
                            };
                        }
                        // 安全已授权情况，跳过其他授权
                        else if (enterpriseConfig?.enableCodeSecurity) {
                            data = {
                                payload: {
                                    ...message.data.payload,
                                    granted: true,
                                },
                                action: message.data.action,
                            };
                        }
                        else {
                            const permissionResult = await this.sendToIde(action, message);
                            data = permissionResult.payload.data;
                        }
                        this.events.emit(
                            'message',
                            {
                                sessionId: message.sessionId,
                                data,
                                execution: message.execution,
                            }
                        );
                        break;
                    }
                    case ACTION_ASK_LLM: {
                        try {
                            // TODO 不同类型不同处理
                            let res = null;
                            const {
                                type,
                                args,
                                promptTemplate,
                                code,
                                filePath,
                                functions,
                                pluginName,
                                model,
                                modelOptions,
                            } = payload;
                            switch (type) {
                                case LlmType.Function:
                                    res = await this.mediator.llmApi.askForFunction({
                                        functions: functions!,
                                        promptTemplate: promptTemplate!,
                                        pluginName: pluginName!,
                                        args,
                                        model,
                                    });
                                    break;
                                case LlmType.ExplainCode:
                                    res = await this.mediator.llmApi.explainCode(
                                        code!,
                                        filePath!,
                                        pluginName!
                                    );
                                    break;
                                case LlmType.DocCommentForCode:
                                    res = await this.mediator.llmApi.getDocCommentForCode(
                                        code!,
                                        filePath!,
                                        pluginName!
                                    );
                                    break;
                                case LlmType.CommentForCode:
                                    res = await this.mediator.llmApi.addCommentForCode(
                                        code!,
                                        filePath!,
                                        pluginName!
                                    );
                                    break;
                                case LlmType.Code:
                                    // res = await this.mediator.llmApi.askForCode(
                                    //     promptTemplate!,
                                    //     args
                                    // );
                                    res = await this.mediator.llmApi.askForText({
                                        promptTemplate: promptTemplate!,
                                        pluginName: pluginName!,
                                        args,
                                        model,
                                        modelOptions,
                                    });
                                    res = extractCodeBlocks(res);
                                    break;
                                default:
                                    res = await this.mediator.llmApi.askForText({
                                        promptTemplate: promptTemplate!,
                                        pluginName: pluginName!,
                                        args,
                                        model,
                                        modelOptions,
                                    });
                            }

                            const data = {
                                action: ACTION_ASK_LLM,
                                payload: {
                                    pluginName,
                                    type,
                                    result: res,
                                },
                            };
                            this.events.emit(
                                'message',
                                {
                                    sessionId: message.sessionId,
                                    data,
                                    execution: message.execution,
                                }
                            );
                        }
                        catch (error) {
                            await this.logger.error(`${ACTION_ASK_LLM} error: ${JSON.stringify(error)}`);
                        }

                        break;
                    }
                    case ACTION_ASK_LLM_STREAMING: {
                        try {
                            const {args, promptTemplate, pluginName, model, modelOptions, capabilityName} = payload;
                            const chunks = await this.mediator.llmApi.askForTextStreaming({
                                promptTemplate: promptTemplate!,
                                pluginName: pluginName!,
                                capabilityName,
                                args,
                                model,
                                modelOptions,
                            });

                            let segmentOrder: number = -1;
                            let result: string = '';

                            for await (const chunk of chunks) {
                                segmentOrder++;
                                if (modelOptions?.enableMultiturnDialogue) {
                                    const conversationId = this.mediator.conversationBufferMemory.getConversationId(
                                        modelOptions.enableMultiturnDialogue,
                                        pluginName!,
                                        capabilityName!
                                    );
                                    // 如果开启多轮对话，则把结果缓存起来
                                    result += chunk.result;
                                    if (chunk.end) {
                                        this.mediator.conversationBufferMemory.putAssistant(
                                            conversationId,
                                            result
                                        );
                                    }
                                }
                                this.events.emit(
                                    'message',
                                    {
                                        sessionId: message.sessionId,
                                        data: {
                                            action: ACTION_ASK_LLM_STREAMING,
                                            payload: {
                                                pluginName,
                                                type: LlmType.Text,
                                                result: chunk.result,
                                            },
                                        },
                                        execution: {
                                            ...message.execution,
                                            segmentOrder,
                                            done: chunk.end,
                                        },
                                    }
                                );
                            }
                        }
                        catch (error) {
                            await this.logger.error(`${ACTION_ASK_LLM_STREAMING} error: ` + error);
                        }
                        break;
                    }
                    case ACTION_GET_PLUGIN_CONFIG: {
                        const {pluginName, key} = payload;
                        // this.logger.info('ACTION_GET_PLUGIN_CONFIG:' + JSON.stringify(message));
                        const pluginConfig = (await this.mediator.getPluginConfig(pluginName!))?.config;
                        const data = {
                            action: ACTION_GET_PLUGIN_CONFIG,
                            payload: {
                                pluginName,
                                key,
                                value: pluginConfig?.[key!] || undefined,
                            },
                        };
                        this.events.emit(
                            'message',
                            {
                                sessionId: message.sessionId,
                                data,
                                execution: message.execution,
                            }
                        );
                        break;
                    }
                    case ACTION_INFORMATION_QUERY: {
                        const {pluginName, options} = payload;
                        const res = await this.mediator.pluginConfigApi.getKnowledgeQueryResult(
                            payload.query!,
                            payload.type as InformationQueryType,
                            // 兼容jetbrins 可能没有uuid
                            payload.informationList.map(v => ({uuid: v.uuid || v.id, type: v.type})),
                            options
                        );
                        // this.logger.info('ACTION_INFORMATION_QUERY res:' + JSON.stringify(res));
                        const data = {
                            action: ACTION_INFORMATION_QUERY,
                            payload: {
                                pluginName,
                                result: res.chunks.map(v =>
                                    options?.withSource
                                        ? ({
                                            content: v.content,
                                            source: v.source,
                                        })
                                        : v.content
                                ),
                            },
                        };
                        this.events.emit(
                            'message',
                            {
                                sessionId: message.sessionId,
                                data,
                                execution: message.execution,
                            }
                        );
                        break;
                    }
                    case ACTION_DEBUG_TASK_PROCESS: {
                        const {pluginName, debugAgentPayload} = payload;
                        // this.logger.info('ACTION_DEBUG_PROCESS:' + JSON.stringify(message));
                        const userDetail = this.mediator.userDetail();
                        const debugAgent = new AutoDebugFixAgent(
                            // 这里的 id 是原本的 conversationId，如果不对再看下需要插件传还是新建一个
                            message.sessionId,
                            isSaaS() ? userDetail.license : userDetail.name,
                            debugAgentPayload,
                            userDetail,
                            async (event: string, data: any) => {
                                return this.mediator.sendToIde(event, data);
                            }
                        );
                        const chunks = debugAgent.generateCode();
                        let segmentOrder: number = -1;
                        let result;
                        for await (const chunk of chunks) {
                            segmentOrder++;
                            result = chunk;
                            this.events.emit(
                                'message',
                                {
                                    sessionId: message.sessionId,
                                    data: {
                                        action: ACTION_DEBUG_TASK_PROCESS,
                                        payload: {
                                            pluginName,
                                            result: chunk.result,
                                        },
                                    },
                                    execution: {
                                        ...message.execution,
                                        segmentOrder,
                                        done: chunk.end,
                                    },
                                }
                            );
                        }
                        break;
                    }
                    case ACTION_ASK_RAG: {
                        try {
                            const {pluginName} = payload;
                            const res = await this.sendToIde(ACTION_ASK_RAG, message);
                            const data = {
                                action: ACTION_ASK_LLM,
                                payload: {
                                    pluginName,
                                    result: res,
                                },
                            };
                            this.events.emit(
                                'message',
                                {
                                    sessionId: message.sessionId,
                                    data,
                                    execution: message.execution,
                                }
                            );
                        }
                        catch (error) {
                            this.logger.error(`${ACTION_ASK_RAG} error: ${JSON.stringify(error)}`);
                        }
                        break;
                    }
                    case ACTION_CODE_SEARCH: {
                        try {
                            const {pluginName, codeSearchPayload} = payload;
                            if (codeSearchPayload?.needRecreateIndex) {
                                const timeoutSeconds = codeSearchPayload.maxDuration || 60;
                                const timeoutPromise = new Promise<string[]>((_, reject) => {
                                    setTimeout(() => reject(new Error('recreate timeout')), timeoutSeconds * 1000);
                                });
                                await Promise.race([
                                    this.sendToIde(ACTION_COMATE_PLUS_RECREATE_INDEX, message),
                                    timeoutPromise,
                                ]).catch(error => {
                                    this.logger.warn(`Recreate index timed out: ${error.message}`);
                                });
                            }
                            const res = await this.sendToIde(ACTION_COMATE_PLUS_CODE_SEARCH, message);
                            const data = {
                                action: ACTION_CODE_SEARCH,
                                payload: {
                                    pluginName,
                                    result: res.codeChunks,
                                },
                            };
                            this.events.emit(
                                'message',
                                {
                                    sessionId: message.sessionId,
                                    data,
                                    execution: message.execution,
                                }
                            );
                        }
                        catch (error) {
                            this.logger.error(`${ACTION_CODE_SEARCH} error: ${JSON.stringify(error)}`);
                        }
                        break;
                    }
                    case ACTION_BATCH_ACCEPT: {
                        try {
                            await this.sendToIde(ACTION_COMATE_PLUS_BATCH_ACCEPT, message);
                        }
                        catch (error) {
                            this.logger.error(`${ACTION_BATCH_ACCEPT} error: ${JSON.stringify(error)}`);
                        }
                        break;
                    }
                    default:
                        // this.logger.info(`${sessionId}`);
                }
            },
        };
    }

    // 虽然暂时没用，为后续做好准备
    dispose() {
        // 清理所有事件监听器
        this.events.removeAllListeners();
        // 清理所有事件队列
        for (const queue of this.eventQueues.values()) {
            queue.stop();
        }
        this.eventQueues.clear();
        // 清理引擎实例
        if (this.engine) {
            this.engine = undefined;
        }
        this.logger.info('PluginChannel disposed');
    }
}
