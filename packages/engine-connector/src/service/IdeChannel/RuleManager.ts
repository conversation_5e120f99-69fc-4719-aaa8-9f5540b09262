import {basename, dirname, join} from 'node:path';
import _ from 'lodash';
import fs from 'node:fs/promises';
import chokidar from 'chokidar';
import {
    DEFAULT_RULE_CONFIG_FOLDER,
    DEFAULT_WORKSPACE_CONFIG_FOLDER,
    RulePanelActionParamsMap,
    RulePanelAction,
    UPDATE_RULE_PANEL_EVENT,
} from '@comate/plugin-shared-internals';
import {kernel} from '@comate/kernel-shared';
import {VirtualEditor} from '@comate/plugin-shared-internals';
import {ensureDirectoryExist} from '@comate/plugin-shared-internals';
import {
    COMATE_RULE_FILE_SUFFIX,
    CURSOR_RULE_FILE_SUFFIX,
    getWorkspaceRules,
    isValidRuleFilename,
    metadataStringify,
    parseRuleContent,
} from '../AgentConversation/Zulu/utils/rules.js';
import {isFileExist} from '../../utils/fs.js';
import {LoggerRoleInstance} from '../Logger.js';
import {hasFilesMatchingPatterns} from '../AgentConversation/Zulu/utils/fs.js';
import {Mediator} from '../Mediator.js';

const debounce = _.debounce;

export type RulePanelActionEventParams = {
    [K in RulePanelAction]: {
        action: K;
        payload: RulePanelActionParamsMap[K];
    }
}[RulePanelAction];

interface RulePanelActionSuccessResult {
    success: true;
    data?: any;
}

interface RulePanelActionFailureResult {
    success: false;
    message: string;
}

type RulePanelActionResult = RulePanelActionSuccessResult | RulePanelActionFailureResult;

/**
 * RuleManager 类用于管理规则相关逻辑
 * 提供了获取规则列表、创建规则、删除规则和更新规则的功能
 */
export class RuleManager {
    private rootPath: string = '';
    private initialized = false;

    constructor(
        private readonly virtualEditor: VirtualEditor,
        private readonly mediator: Mediator,
        private readonly logger: LoggerRoleInstance
    ) {

    }

    /**
     * 初始化拿到 workspace rootPath 同时监听文件变化
     * 触发 handleRulePanelAction 才会初始化，保证 workspace 一定是存在的
     * 同时可以避免没用过规则相关功能时也监听文件的情况
     */
    init() {
        this.initialized = true;
        const rootUri = kernel.env?.workspaceInfo?.rootUri;
        if (!rootUri) {
            return;
        }
        this.rootPath = kernel.uri.parse(rootUri).fsPath;

        // 初始化完成后，开始监听规则文件变化
        this.watchRuleFiles();
    }

    private async fileChangeHandler(path: string): Promise<void> {
        const filename = basename(path);
        if (isValidRuleFilename(filename, COMATE_RULE_FILE_SUFFIX)
            || filename.endsWith(CURSOR_RULE_FILE_SUFFIX)
        ) {
            this.mediator.sendToIde(UPDATE_RULE_PANEL_EVENT, {action: 'onRuleFileChange'});
        }
    }

    private watchRuleFiles(): void {
        if (!this.rootPath) {
            return;
        }

        try {
            // 监听 .comate/rules 和 .cursor/rules 目录
            const comateRulesPath = join(
                this.rootPath,
                DEFAULT_WORKSPACE_CONFIG_FOLDER,
                DEFAULT_RULE_CONFIG_FOLDER
            );
            const cursorRulesPath = join(this.rootPath, '.cursor', 'rules');

            // 重命名时可能先删除后创建，所以加个 debounce
            const debouncedFileChangeHandler = debounce(
                (path: string) => {
                    this.fileChangeHandler(path);
                },
                100
            );
            const watcher = chokidar.watch([comateRulesPath, cursorRulesPath], {
                persistent: true,
                ignoreInitial: true,
                ignorePermissionErrors: true,
                awaitWriteFinish: {
                    stabilityThreshold: 300,
                    pollInterval: 1000
                }
            });

            watcher
                .on('add', async (path: string) => {
                    debouncedFileChangeHandler(path);
                })
                .on('change', async (path: string) => {
                    debouncedFileChangeHandler(path);
                })
                .on('unlink', async (path: string) => {
                    debouncedFileChangeHandler(path);
                });
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            this.logger.error(`[RuleManager] watchRuleFiles error: ${errorMessage}`);
        }
    }

    async handleRulePanelAction(params: RulePanelActionEventParams): Promise<RulePanelActionResult> {
        try {
            if (!this.initialized) {
                this.init();
            }
            if (!this.rootPath) {
                return {success: false, message: 'noWorkspace'};
            }
            const result = await this.resolveRulePanelAction(params);
            if (!result.success && result.message) {
                this.logger.error(`[RuleManager] Failed to handle rule panel action: ${result.message}`);
            }
            return result;
        }
        catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            this.logger.error(`[RuleManager] Failed to handle rule panel action: ${errorMessage}`);
            return {success: false, message: errorMessage};
        }
    }

    private resolveRulePanelAction(params: RulePanelActionEventParams): Promise<RulePanelActionResult> {
        const {action, payload} = params;
        switch (action) {
            case 'openRuleFile': {
                return this.openRuleFile(payload);
            }
            case 'getRuleList': {
                return this.getRuleList();
            }
            case 'createRule': {
                return this.createRule(payload);
            }
            case 'deleteRule': {
                return this.deleteRule(payload);
            }
            case 'updateRule': {
                return this.updateRule(payload);
            }
            case 'checkFilePatternMatch': {
                return this.checkFilePatternMatch(payload);
            }
            default: {
                return Promise.resolve({success: false, message: 'unsupportedAction'});
            }
        }
    }

    private async openRuleFile(params: RulePanelActionParamsMap['openRuleFile']): Promise<RulePanelActionResult> {
        const {path} = params;
        const absolutePath = this.getAbsolutePath(path);
        if (!await isFileExist(absolutePath)) {
            return {success: false, message: 'fileNotExist'};
        }
        await this.doOpenRuleFile(absolutePath);
        return {success: true};
    }

    private async checkFilePatternMatch(
        params: RulePanelActionParamsMap['checkFilePatternMatch']
    ): Promise<RulePanelActionResult> {
        const {globs = ''} = params;
        const filePatterns = globs
            .split(',')
            .map(g => g.trim())
            .filter(Boolean)
            .map(g => `**/${g}`);
        const matched = await hasFilesMatchingPatterns(this.rootPath, filePatterns);
        return {success: true, data: matched};
    }

    private async getRuleList(): Promise<RulePanelActionResult> {
        const rules = await getWorkspaceRules(this.rootPath, this.virtualEditor);
        const result = rules.map(rule => ({
            name: rule.name,
            metadata: rule.metadata,
            firstLine: rule.content.trim().split('\n')[0] ?? '',
            path: rule.path,
        }));
        return {success: true, data: result};
    }

    private async createRule(
        params: RulePanelActionParamsMap['createRule']
    ): Promise<RulePanelActionResult> {
        const {name, metadata = {}} = params;

        const folder = this.getRuleFolder();
        await ensureDirectoryExist(folder);

        const fileName = this.getRuleFileName(name);
        const absolutePath = join(folder, fileName);

        if (await isFileExist(absolutePath)) {
            return {success: false, message: 'ruleExist'};
        }
        const metadataText = metadataStringify(metadata);
        await this.virtualEditor.saveDocumentWithReplaceContent({
            absolutePath,
            content: metadataText + '\n',
            source: 'rule',
        });
        await this.doOpenRuleFile(absolutePath);
        return {success: true};
    }

    private async deleteRule(params: RulePanelActionParamsMap['deleteRule']): Promise<RulePanelActionResult> {
        const {path} = params;
        const absolutePath = this.getAbsolutePath(path);
        if (!await isFileExist(absolutePath)) {
            return {success: false, message: 'fileNotExist'};
        }

        await fs.unlink(absolutePath);
        return {success: true};
    }

    private async updateRule(
        params: RulePanelActionParamsMap['updateRule']
    ): Promise<RulePanelActionResult> {
        const {path, name, metadata} = params;
        const absolutePath = this.getAbsolutePath(path);
        const {existed, content = ''} = await this.virtualEditor.getDocument({absolutePath});
        if (!existed) {
            return {success: false, message: 'fileNotExist'};
        }

        const {content: currentTextContent} = parseRuleContent(content);
        const ruleContent = metadataStringify(metadata);

        await this.virtualEditor.saveDocumentWithReplaceContent({
            absolutePath,
            content: `${ruleContent}\n${currentTextContent}`,
            source: 'rule',
        });

        const oldFileName = basename(path);
        const newFileName = this.getRuleFileName(name);
        if (oldFileName !== newFileName) {
            const folder = dirname(absolutePath);
            const newFilePath = join(folder, newFileName);
            await fs.rename(absolutePath, newFilePath);
            await this.virtualEditor.closeDocument({absolutePath});
            await this.doOpenRuleFile(newFilePath);
        }
        return {success: true};
    }

    /**
     * 获取规则文件夹路径
     * @private
     */
    private getRuleFolder(): string {
        return join(this.rootPath, DEFAULT_WORKSPACE_CONFIG_FOLDER, DEFAULT_RULE_CONFIG_FOLDER);
    }

    /**
     * 获取规则文件名
     * @param name 规则名称
     * @private
     */
    private getRuleFileName(name: string): string {
        if (name.endsWith(COMATE_RULE_FILE_SUFFIX)) {
            return name;
        }
        return `${name}${COMATE_RULE_FILE_SUFFIX}`;
    }

    private getAbsolutePath(relativePath: string): string {
        return join(this.rootPath, relativePath);
    }

    private async doOpenRuleFile(absolutePath: string): Promise<RulePanelActionResult> {
        const selection = {
            start: {line: Number.MAX_SAFE_INTEGER, column: Number.MAX_SAFE_INTEGER},
            end: {line: Number.MAX_SAFE_INTEGER, column: Number.MAX_SAFE_INTEGER},
        };
        await this.virtualEditor.openDocument({absolutePath, selection});
        return {success: true};
    }
}
