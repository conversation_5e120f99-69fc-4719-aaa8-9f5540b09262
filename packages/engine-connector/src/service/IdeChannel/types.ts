import {
    ACTION_COMATE_PLUS_CHAT_QUERY,
    ACTION_COMATE_PLUS_CUSTOM_COMMAND,
    ACTION_COMATE_PLUS_WEBVIEW_INIT_DATA,
    ACTION_UPDATE_ENGINE_CONFIG,
    ACTION_QUERY_SELECTOR,
    ACTION_COMATE_PLUS_DIAGNOSTIC_SCAN,
    ACTION_GENERATE_MESSAGE,
    ACTION_GENERATE_MESSAGE_REPORT,
    ACTION_BRANCH_CHANGE,
    ACTION_COMATE_SMART_APPLY,
    ACTION_CHAT_SESSION_LIST,
    ACTION_CHAT_SESSION_SAVE,
    ACTION_CHAT_SESSION_DELETE,
    ACTION_CHAT_SESSION_FIND,
    ACTION_MOCK_VIRTUAL_EDITOR_EVENT,
    ACTION_V8_SNAP_SHOT,
    ACTION_COMATE_ADD_CACHE,
    ACTION_COMATE_GET_CACHE,
    ACTION_PROMPTTEMPLATE_LIST,
    ACTION_PROMPTTEMPLATE_CREATE,
    ACTION_PROMPTTEMPLATE_DELETE,
    ACTION_PROMPTTEMPLATE_UPDATE,
    ACTION_SCAN_CACHE_COUNT,
    ACTION_COMATE_PLUS_CHAT_CANCEL,
    ACTION_START_ISCAN_AND_GET_SEC_RESULT,
    ACTION_COMATE_PLUS_SA_SCAN_DIAGNOSTIC,
    ACTION_COMATE_PLUS_SA_SCAN_INIT,
    RULE_PANEL_ACTION_EVENT,
    CustomCommandPayload,
    KnowledgeSetBase,
    SystemInfoParsed,
} from '@comate/plugin-shared-internals';
import {RulePanelActionEventParams} from './RuleManager.js';

export interface QueryParams {
    scanId: string;
    scanType: string;
    // TODO: messageId 在client的类型声明中是个string，不一致会导致问题
    messageId: number;
    pluginName: string;
    capability: string;
    query: string;
    data?: any;
    customCommandPayload?: CustomCommandPayload;
    knowledgeList: KnowledgeSetBase[];
    sessionUuid: string;
    context: {
        query: string;
        selectedCode: string;
        selectedRange: [{line: number, character: number}, {line: number, character: number}] | [];
        activeFileContent: string;
        activeFileLineContent: string;
        activeFilePath: string;
        activeFileName: string;
        activeFileLanguage: string;
    };
    systemInfo: SystemInfoParsed;
    agentPayload: any;
    chatCancleToken?: string;
    isRegenerated?: boolean;
}

/** 这里的类型后面涉及到的自个改吧 */
export interface QueryParamsMap {
    [RULE_PANEL_ACTION_EVENT]: RulePanelActionEventParams;
    [ACTION_COMATE_PLUS_CHAT_QUERY]: QueryParams;
    [ACTION_COMATE_PLUS_CHAT_CANCEL]: QueryParams;
    [ACTION_COMATE_PLUS_CUSTOM_COMMAND]: QueryParams;
    [ACTION_COMATE_PLUS_WEBVIEW_INIT_DATA]: QueryParams;
    [ACTION_COMATE_PLUS_DIAGNOSTIC_SCAN]: QueryParams;
    [ACTION_SCAN_CACHE_COUNT]: QueryParams;
    [ACTION_UPDATE_ENGINE_CONFIG]: QueryParams;
    [ACTION_GENERATE_MESSAGE]: QueryParams;
    [ACTION_GENERATE_MESSAGE_REPORT]: QueryParams;
    [ACTION_BRANCH_CHANGE]: QueryParams;
    [ACTION_COMATE_SMART_APPLY]: QueryParams;
    [ACTION_CHAT_SESSION_LIST]: QueryParams;
    [ACTION_CHAT_SESSION_SAVE]: QueryParams;
    [ACTION_CHAT_SESSION_DELETE]: QueryParams;
    [ACTION_CHAT_SESSION_FIND]: QueryParams;
    [ACTION_PROMPTTEMPLATE_LIST]: QueryParams;
    [ACTION_PROMPTTEMPLATE_CREATE]: QueryParams;
    [ACTION_PROMPTTEMPLATE_UPDATE]: QueryParams;
    [ACTION_PROMPTTEMPLATE_DELETE]: QueryParams;
    [ACTION_MOCK_VIRTUAL_EDITOR_EVENT]: QueryParams;
    [ACTION_COMATE_ADD_CACHE]: QueryParams;
    [ACTION_COMATE_GET_CACHE]: QueryParams;
    [ACTION_V8_SNAP_SHOT]: QueryParams;
    [ACTION_START_ISCAN_AND_GET_SEC_RESULT]: QueryParams;
    [ACTION_COMATE_PLUS_SA_SCAN_DIAGNOSTIC]: QueryParams;
    [ACTION_COMATE_PLUS_SA_SCAN_INIT]: QueryParams;
    [ACTION_QUERY_SELECTOR]: QueryParams;
    'COMATE_AGENT_SET_FOREGROUND_CONVERSATION': QueryParams;
    'COMATE_AGENT_NEW_MESSAGE': QueryParams;
    'COMATE_AGENT_GET_CONVERSATIONS': QueryParams;
    'COMATE_AGENT_START_NEW_CHAT': QueryParams;
}

export type IdeListenerEventPayload = {
    [K in keyof QueryParamsMap]: {
        method: K;
        params: QueryParamsMap[K];
    }
}[keyof QueryParamsMap];

export interface KnowledgeList {
    id: string;
    name: string;
    type: 'NORMAL' | 'SYSTEM' | 'FILE' | 'TEMP';
}

export interface AgentList {
    name: string;
    displayName?: string;
    description?: string;
    icon?: string;
}

export interface AgentListWithId extends AgentList {
    id: string;
}

export interface CommandListWithId {
    displayName?: string; // 中文名称
    name: string; // 英文名称
    owner: AgentList;
    type?: string; // 'Skill' | 'Prompt' | 'Fallback'
    placeholder?: string;
    defaultUserMessage?: string;
    displayTag?: string;
    enableSmartApply?: boolean;
}
