import {
    createConnection,
    ProposedFeatures,
    _Connection,
    StreamMessageReader,
    StreamMessageWriter,
    InitializeParams,
    InitializeResult,
    TextDocumentSyncKind,
    DidChangeConfigurationNotification,
    WorkspaceFolder,
} from 'vscode-languageserver/node.js';
import {LoggerRoleInstance} from '../Logger.js';
import {Mediator} from '../Mediator.js';
import {ideListener} from './ideListener.js';
import {
    ACTION_COMATE_PLUS_INITIALIZED,
    VirtualEditor,
} from '@comate/plugin-shared-internals';
import {isInternal} from '../../utils/isSaaS.js';
import {kernel, kernelService, KernelService} from '@comate/kernel-shared';
import {IdeListenerEventPayload} from './types.js';
import {RuleManager} from './RuleManager.js';

// TODO Capability 的判断，初始化成功的判断
export class IdeChannel {
    private readonly connect: _Connection;
    private readonly logger: LoggerRoleInstance;
    private readonly mediator: Mediator;
    public kernelService: KernelService;
    private readonly ruleManager: RuleManager;

    constructor(mediator: Mediator, private readonly virtualEditor: VirtualEditor, logger: LoggerRoleInstance) {
        this.logger = logger;
        this.mediator = mediator;
        this.connect = createConnection(
            ProposedFeatures.all,
            new StreamMessageReader(process.stdin as NodeJS.ReadableStream),
            new StreamMessageWriter(process.stdout as NodeJS.WritableStream)
        );
        this.connect.onExit(() => {
            this.logger.error('connect onExit');
            // todo
        });
        this.connect.onShutdown(() => {
            this.logger.error('connect onShutdown');
            // todo
        });
        this.kernelService = kernelService.init(this.connect);
        this.ruleManager = new RuleManager(this.virtualEditor, this.mediator, this.logger);
    }

    async init() {
        return new Promise<void>(resolve => {
            let hasConfigurationCapability = false;
            let hasWorkspaceFolderCapability = false;
            // let hasDiagnosticRelatedInformationCapability = false;
            this.connect.onInitialize((params: InitializeParams) => {
                this.logger.info('onInitialize:', params);
                const capabilities = params.capabilities;
                this.kernelService.setInitializeParams(params);

                hasConfigurationCapability = !!(
                    capabilities.workspace && !!capabilities.workspace.configuration
                );
                hasWorkspaceFolderCapability = !!(
                    capabilities.workspace && !!capabilities.workspace.workspaceFolders
                );

                const result: InitializeResult = {
                    capabilities: {
                        textDocumentSync: TextDocumentSyncKind.Incremental,
                    },
                };
                if (hasWorkspaceFolderCapability) {
                    result.capabilities.workspace = {
                        workspaceFolders: {
                            supported: true,
                        },
                    };
                }
                return result;
            });
            this.connect.onInitialized(() => {
                this.logger.setLogInstance(this.connect.console);
                if (hasConfigurationCapability) {
                    // Register for all configuration changes.
                    this.connect.client.register(DidChangeConfigurationNotification.type, undefined);
                }
                if (hasWorkspaceFolderCapability) {
                    // eslint-disable-next-line @typescript-eslint/no-unused-vars
                    this.connect.workspace.onDidChangeWorkspaceFolders(_event => {
                        this.connect.console.log('Workspace folder change event received.');
                    });
                }
                resolve();
            });
            // 非vscode 没有 onInitialized
            this.connect.onRequest(async (method: string, params: any) => {
                this.logger.info('---------> ENGINE REC:', method, params);

                try {
                    return ideListener(
                        {method, params} as IdeListenerEventPayload,
                        this.mediator,
                        this.logger,
                        this.virtualEditor,
                        this.ruleManager
                    );
                }
                catch (e) {
                    this.logger.error('ENGINE EXCUTE ERROR', e);
                }
            });
            this.connect.onShutdown((...args) => {
                this.logger.warn('engine connection onShutdown', ...args);
            });
            this.connect.onExit((...args) => {
                this.logger.warn('engine connection onExit', ...args);
            });
            this.connect.listen();
            if (isInternal() && process.env.COMATE_ENGINE_APP_NAME === 'icoding') {
                this.setupIdleTimeout();
            }
        });
    }

    async send(type: string, params: any, opts?: {skipWriteLog?: boolean}): Promise<any> {
        if (!opts?.skipWriteLog) {
            this.logger.info('<--------- ENGINE SEND:', type, params);
        }

        try {
            return await this.connect.sendRequest(type, params);
        }
        catch (e) {
            this.logger.error('failed to send to ide connection', type, params, e);
            return {};
        }
    }

    async sendNotification(type: string, params: any): Promise<void> {
        this.logger.info('<--------- ENGINE sendNotification:', type, params);
        return this.connect.sendNotification(type, params);
    }

    async getWorkspaceFolders(): Promise<WorkspaceFolder[]> {
        // TODO 兼容
        return kernel.env.workspaceInfo.workspaceFolders as WorkspaceFolder[];
    }

    async initialized() {
        return this.send(ACTION_COMATE_PLUS_INITIALIZED, {});
    }

    setupIdleTimeout() {
        let lastMessageTime = Date.now();
        const ONE_HOUR = 3 * 60 * 60 * 1000;

        // 检查空闲时间
        process.stdin.on('data', () => {
            lastMessageTime = Date.now();
        });

        // 每30分钟检查一次空闲状态
        setInterval(() => {
            const timeSinceLastMessage = Date.now() - lastMessageTime;

            if (timeSinceLastMessage >= ONE_HOUR) {
                process.exit(0);
            }
        }, 30 * 60 * 1000);

        // 每分钟检查一次是否到达凌晨3点
        setInterval(() => {
            const now = new Date();
            if (now.getHours() === 3 && now.getMinutes() === 0) {
                process.exit(0);
            }
        }, 60 * 1000);
    }

    close() {
        this.connect.dispose();
    }
}
