import {homedir} from 'node:os';
import {McpServerConfig, SseMcpServerConfig, StdioMcpServerConfig} from './types.js';

/* MCP Server 连接超时设置，单位秒，默认 60 秒，最小 1 秒 */
export const DEFAULT_MCP_TIMEOUT_SECONDS = 30;
export const MIN_MCP_TIMEOUT_SECONDS = 1;

export const dropNilValue = <T extends Record<string, any>>(obj: T) => {
    const result: Partial<T> = {};
    for (const key in obj) {
        if (obj[key] !== undefined && obj[key] !== null) {
            result[key] = obj[key];
        }
    }
    return result;
};

export const processServerConfig = (name: string, config: any): McpServerConfig => {
    if (!name) {
        throw new Error(`Server name 不能为空`);
    }
    if (config.url === undefined && config.command === undefined) {
        throw new Error(`Server ${name} 必须配置 url 或 command`);
    }
    if (config.url !== undefined && config.command !== undefined) {
        throw new Error(`Server ${name} 只能配置 url 或 command`);
    }
    if (config.url !== undefined) {
        if (!config.url) {
            throw new Error(`Server ${name} 的 url 不能为空`);
        }
        return dropNilValue({
            transportType: config.transportType || 'sse',
            url: config.url,
            timeout: config.timeout || DEFAULT_MCP_TIMEOUT_SECONDS,
            disabled: config.disabled,
        }) as SseMcpServerConfig;
    }
    if (!config.command) {
        throw new Error(`Server ${name} 的 command 不能为空`);
    }

    return dropNilValue({
        transportType: 'stdio',
        command: config.command,
        env: config.env,
        args: config.args,
        cwd: config.cwd,
        timeout: config.timeout || DEFAULT_MCP_TIMEOUT_SECONDS,
        disabled: config.disabled,
    }) as StdioMcpServerConfig;
};

/**
 * 删除当前进程env中的 Comate 相关变量
 * @param env
 * @returns
 */
export function filterComateEnv(env?: Record<string, string>): Record<string, string> {
    return Object.fromEntries(
        Object.entries(env || process.env).filter(([key, value]) => !key.startsWith('COMATE_'))
    ) as Record<string, string>;
}

/**
 * 将 spawn 的 args 参数中的路径参数及中的波浪号(~)替换为用户的主目录路径。
 *
 * @param p 包含波浪号的路径字符串。
 * @returns 替换后的路径字符串。
 */
export function expandTilde(p: string): string {
    return p.replace(/^~(?=$|\/|\\)/, homedir());
}

export function secondsToMs(seconds: number): number {
    return seconds * 1000;
}

export function isEmptyObject(obj?: Record<string, any>): boolean {
    return !obj || Object.keys(obj).length === 0;
}
