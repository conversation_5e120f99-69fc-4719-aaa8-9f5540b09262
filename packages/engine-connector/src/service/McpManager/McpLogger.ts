import dayjs from 'dayjs';
import {homedir} from 'node:os';
import path from 'node:path';
import fs from 'node:fs';
import {kernel, KERNEL_MCP_OUTPUT_CHANNEL_CREATE, KERNEL_MCP_OUTPUT_CHANNEL_MESSAGE} from '@comate/kernel-shared';
import {Mediator} from '../Mediator.js';
import {formatWin32PathSep} from '../Composer/utils/index.js';
import {isJetbrains, isVscode} from '../../utils/checkIDE.js';

const getCurrentTime = () => {
    return dayjs().format('YYYY-MM-DD HH:mm:ss');
};

type LogType = 'INFO' | 'WARN' | 'ERROR';

export default class McpLogger {
    mediator: Mediator;
    logFilePath: string = '';
    private stream: fs.WriteStream | null = null;

    constructor(mediator: Mediator) {
        this.mediator = mediator;
        this.init();
    }

    /**
     * @description 获取日志文件的绝对路径，如果是JetBrains则转换Windows的路径分隔符。
     */
    get normalizedAbsolutedPath() {
        return isJetbrains ? formatWin32PathSep(this.logFilePath) : this.logFilePath;
    }

    init() {
        if (isVscode) {
            kernel.connect.sendNotification(KERNEL_MCP_OUTPUT_CHANNEL_CREATE);
        }
        else {
            const folder = path.join(homedir(), '.comate-engine', 'mcp-log');
            fs.mkdirSync(folder, {recursive: true});
            this.logFilePath = path.join(homedir(), '.comate-engine', 'mcp-log', 'mcp.log');
            this.stream = fs.createWriteStream(this.logFilePath, {flags: 'w'});
        }
    }

    log(type: LogType, texts: string[]) {
        const logMsg = `[${getCurrentTime()}][${type}] ${texts.join(' ')}`;
        if (isVscode) {
            kernel.connect.sendNotification(KERNEL_MCP_OUTPUT_CHANNEL_MESSAGE, logMsg);
        }
        else {
            try {
                if (this.stream?.writable) {
                    this.stream?.write(logMsg + '\n');
                }
            }
            catch (e) {
                kernel.logger.error(`MCP 日志写入错误:`, e);
            }
        }
    }

    info(...texts: string[]) {
        this.log('INFO', texts);
    }
    warn(...texts: string[]) {
        this.log('WARN', texts);
    }
    error(...texts: string[]) {
        this.log('ERROR', texts);
    }
    dispose() {
        if (this.stream) {
            this.stream.end();
        }
    }
}
