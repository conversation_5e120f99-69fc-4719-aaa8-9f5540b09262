type ConnectionTask = () => Promise<void>;

export default class ConcurrentPool {
    maxConcurrent: number;
    running: number;
    pending: number;
    queue: ConnectionTask[];
    completionListeners: Array<() => void> = [];

    constructor(maxConcurrent: number = 5) {
        this.maxConcurrent = maxConcurrent;
        this.queue = [];
        this.running = 0;
        this.pending = 0;
    }

    add(task: ConnectionTask) {
        this.pending++;
        return new Promise((resolve, reject) => {
            this.queue.push(() => {
                return Promise
                    .resolve(task())
                    .then(resolve)
                    .catch(reject)
                    .finally(() => {
                        this.running--;
                        this.pending--;
                        this.next();
                        this.checkAllTasksDone();
                    });
            });
            this.next();
        });
    }

    next() {
        while (this.running < this.maxConcurrent && this.queue.length > 0) {
            const task = this.queue.shift() as ConnectionTask;
            this.running++;
            task();
        }
    }

    checkAllTasksDone() {
        if (this.pending === 0) {
            // 触发所有回调并清空列表
            this.completionListeners.forEach(resolve => resolve());
            this.completionListeners = [];
        }
    }

    allTasksDone() {
        return new Promise<void>(resolve => {
            if (this.pending === 0) {
                resolve();
            }
            else {
                this.completionListeners.push(resolve);
            }
        });
    }
}
