import {<PERSON><PERSON><PERSON>o<PERSON><PERSON><PERSON>, MC<PERSON>mageContent, MCPTextContent, MCPResourceContent} from '@comate/plugin-shared-internals';
import {Client} from '@modelcontextprotocol/sdk/client/index.js';
import {SSEClientTransport} from '@modelcontextprotocol/sdk/client/sse.js';
import {StdioClientTransport} from '@modelcontextprotocol/sdk/client/stdio.js';
import {StreamableHTTPClientTransport} from '@modelcontextprotocol/sdk/client/streamableHttp.js';

export interface StdioMcpServerConfigRaw {
    command: string;
    args?: string[];
    env?: Record<string, string>;
    cwd?: string;
    disabled?: boolean;
}

export interface SseMcpServerConfigRaw {
    url: string;
    disabled?: boolean;
}

export type McpServerConfigRaw = StdioMcpServerConfigRaw | SseMcpServerConfigRaw;

export interface McpServerBaseConfig {
    autoApprove?: boolean;
    timeout?: number;
}

export interface StdioMcpServerConfig extends StdioMcpServerConfigRaw, McpServerBaseConfig {
    transportType: 'stdio';
}

export interface SseMcpServerConfig extends SseMcpServerConfigRaw, McpServerBaseConfig {
    transportType: 'sse';
}

export interface StreamableHttpMcpServerConfig extends SseMcpServerConfigRaw, McpServerBaseConfig {
    transportType: 'streamableHttp';
}

export type McpServerConfig = StdioMcpServerConfig | SseMcpServerConfig | StreamableHttpMcpServerConfig;

export interface McpConnection {
    server: McpServer;
    client: Client;
    transport: StdioClientTransport | SSEClientTransport | StreamableHTTPClientTransport;
}

export interface McpServer {
    name: string;
    config: string;
    status: 'connected' | 'connecting' | 'disconnected';
    error?: string;
    tools?: McpTool[];
    disabled?: boolean;
    timeout?: number;
    logPath: string;
}

export interface McpTool {
    name: string;
    description?: string;
    inputSchema?: object;
    autoApprove?: boolean;
}

export interface McpToolCallResponse {
    _meta?: Record<string, any>;
    content: Array<MCPTextContent | MCPImageContent | MCPAudioContent | MCPResourceContent>;
    isError?: boolean;
}

export interface McpSettingsSnapshot {
    content?: string;
    error?: string;
    servers?: Record<string, McpServerConfigRaw>;
}
