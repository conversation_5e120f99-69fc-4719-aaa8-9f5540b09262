import path from 'node:path';
import chokidar, {FSWatcher} from 'chokidar';
import jsonSourcemap from '@mischnic/json-sourcemap';
import {
    kernel,
    PT_KERNEL_MCP_CONFIG_UPDATED,
    PT_WEBVIEW_MCP_CONFIG_FETCH,
    PT_WEBVIEW_MCP_CONFIG_UPDATE,
    PT_WEBVIEW_MCP_CONNECTION_CREATE_CONFIG,
} from '@comate/kernel-shared';
import {VirtualEditor} from '@comate/plugin-shared-internals';

import {isFileExist} from '../../utils/fs.js';
import {McpServerConfig, McpServerConfigRaw, McpSettingsSnapshot} from './types.js';
import {isEmptyObject, processServerConfig} from './utils.js';
import McpLogger from './McpLogger.js';

const MCP_SETTINGS_FILE_PATH = '.comate/mcp.json';
const DEFAULT_SETTINGS_JSON = {
    mcpServers: {},
};

export class McpSettingsWatcher {
    private logger: McpLogger;
    private readonly virtualEditor: VirtualEditor;
    private watcher: FSWatcher | undefined;
    private mcpSettingsFilePath: string = '';
    /* serverSettings 是直接从文件里边解析的内容，始终与文件内容保持一致 */
    private serverSettings: Record<string, McpServerConfigRaw> | undefined;
    /* mcpSettingsError 是配置文件错误的错误信息 */
    private mcpSettingsError: string | undefined;
    /* mcpSettingsSnapshot 是启动 MCP Server 列表的配置快照 */
    private mcpSettingsSnapshot: McpSettingsSnapshot | undefined;

    constructor(logger: McpLogger, virtualEditor: VirtualEditor) {
        this.logger = logger;
        this.virtualEditor = virtualEditor;
        this.initMcpSettingsFilePath();
        this.watchMcpSettingsFile();
        kernel.connect.onWebviewMessage(
            PT_WEBVIEW_MCP_CONNECTION_CREATE_CONFIG,
            this.createMcpServerConfig.bind(this)
        );
        kernel.connect.onWebviewMessage(
            PT_WEBVIEW_MCP_CONFIG_FETCH,
            this.fetchMcpServerConfig.bind(this)
        );
        kernel.connect.onWebviewMessage(
            PT_WEBVIEW_MCP_CONFIG_UPDATE,
            this.updateServerSettings.bind(this)
        );
    }

    private initMcpSettingsFilePath(): void {
        const rootPath = kernel.env.workspaceInfo.rootPath;
        if (!rootPath) {
            throw new Error('未打开工作区，请打开工作区');
        }
        this.mcpSettingsFilePath = path.join(
            rootPath,
            MCP_SETTINGS_FILE_PATH
        );
    }

    private processServerConfigs(fileContent: string): Record<string, McpServerConfig> {
        try {
            const parsedConfig = JSON.parse(fileContent);

            if (!parsedConfig.mcpServers) {
                throw new Error('缺少 "mcpServers" 字段');
            }
            if (typeof parsedConfig.mcpServers !== 'object') {
                throw new Error('"mcpServers" 字段应为对象');
            }

            this.serverSettings = parsedConfig.mcpServers;

            const serverConfigs: Record<string, McpServerConfig> = {};
            for (const [name, config] of Object.entries(parsedConfig.mcpServers)) {
                const serverConfig: McpServerConfig = processServerConfig(name, config);
                serverConfigs[name] = serverConfig;
            }
            this.mcpSettingsError = undefined; // 清除错误信息
            return serverConfigs;
        }
        catch (error) {
            this.serverSettings = {};
            this.mcpSettingsError = (error as Error).message;
            this.logger.error(`解析 MCP 配置失败: ${(error as Error).message}`);
            throw error;
        }
    }

    /**
     * 获取 MCP 配置文件内容
     * @returns
     */
    async getServerSettings(): Promise<Record<string, McpServerConfig>> {
        try {
            if (!await isFileExist(this.mcpSettingsFilePath)) {
                this.mcpSettingsSnapshot = {
                    servers: {},
                };
                return {};
            }

            const content = await kernel.fs.readFile(this.mcpSettingsFilePath, 'utf-8');

            if (!content?.trim()) {
                this.logger.warn('MCP 配置文件为空');
                this.mcpSettingsSnapshot = {
                    content: content,
                    servers: {},
                };
                return {};
            }

            const settings = this.processServerConfigs(content);
            this.mcpSettingsSnapshot = {
                content: content,
                servers: {
                    ...this.serverSettings,
                },
            };
            return settings;
        }
        catch (error) {
            this.logger.error(`读取 MCP 配置失败: ${(error as Error).message}`);
            throw error;
        }
    }
    private async fileContentChangeHandler(path: string, event: 'add' | 'change'): Promise<void> {
        const settingsPath = this.mcpSettingsFilePath;
        this.logger.info(`MCP 设置文件 ${event} 事件 `, settingsPath, path);
        if (path === settingsPath) {
            const mcpSettings = await kernel.fs.readFile(settingsPath, 'utf-8');
            try {
                this.processServerConfigs(mcpSettings);

                const changed = (event === 'add' && isEmptyObject(
                        this
                            .mcpSettingsSnapshot
                            ?.servers
                    )
                        && isEmptyObject(this.serverSettings))
                    ? false
                    : mcpSettings !== this.mcpSettingsSnapshot?.content;
                kernel.connect.sendWebviewMessage(
                    PT_KERNEL_MCP_CONFIG_UPDATED,
                    {
                        config: this.serverSettings,
                        changed,
                        error: this.mcpSettingsError,
                    }
                );
            }
            catch (error) {
                this.logger.error('MCP 设置文件解析失败', (error as Error).message);
                kernel.connect.sendWebviewMessage(
                    PT_KERNEL_MCP_CONFIG_UPDATED,
                    {
                        config: this.serverSettings,
                        error: this.mcpSettingsError,
                    }
                );
            }
        }
    }

    /**
     * 监听配置文件更新，通知webview刷新
     * @returns void
     */
    private async watchMcpSettingsFile(): Promise<void> {
        try {
            const settingsPath = this.mcpSettingsFilePath;
            const dirPath = path.dirname(settingsPath);
            this.watcher = chokidar.watch(dirPath, {
                persistent: true,
                ignoreInitial: true,
            });
            this
                .watcher
                .on('addDir', (path: string) => {
                    // .comate 文件夹及其子文件夹创建事件
                })
                .on('add', async (path: string) => {
                    // .comate 文件夹创建文件事件
                    // 因为 kernel.fs.writeFile 操作未出现的文件时只会触发 add 事件but不会触发 change 事件
                    // 所以这里需要绑定 change 事件的 handler
                    await this.fileContentChangeHandler(path, 'add');
                })
                .on('change', async (path: string) => {
                    await this.fileContentChangeHandler(path, 'change');
                })
                .on('unlink', (path: string) => {
                    // .comate 文件夹删除文件事件
                    if (path === settingsPath) {
                        this.serverSettings = {};
                        kernel.connect.sendWebviewMessage(PT_KERNEL_MCP_CONFIG_UPDATED, {
                            changed: !isEmptyObject(this.mcpSettingsSnapshot?.servers),
                        });
                    }
                })
                .on('unlinkDir', (path: string) => {
                    // .comate 文件夹及其子文件夹删除事件
                    if (dirPath === path) {
                        this.watcher?.add(dirPath);
                    }
                });
        }
        catch (error) {
            this.logger.error('获取 MCP 设置文件失败', (error as Error).message);
            return;
        }
    }

    /**
     * 创建 MCP 设置文件，如果已经存在则不操作
     * @returns {string} 设置文件路径
     * @throws {Error} 创建文件失败
     * @description 如果文件不存在，则创建一个默认的 MCP 设置文件
     */
    async createMcpServerConfig(): Promise<string> {
        try {
            const isConfigExist = await isFileExist(this.mcpSettingsFilePath);
            if (!isConfigExist) {
                const dir = path.dirname(this.mcpSettingsFilePath);
                await kernel.fs.mkdir(dir, {recursive: true}); // 确保目录存在
                await kernel.fs.writeFile(
                    this.mcpSettingsFilePath,
                    JSON.stringify(DEFAULT_SETTINGS_JSON, null, 4)
                );
            }
            // 写相对路径方便于webview的openlink事件打开
            return MCP_SETTINGS_FILE_PATH;
        }
        catch (error) {
            this.logger.error('创建 MCP 设置文件失败', (error as Error).message);
            return '';
        }
    }

    /**
     * 获取 MCP 设置文件的行数
     * @param {string} serverName - 服务器名称
     * @returns {Promise<{filePath: string, startLine: number, endLine: number, config?: Record<string, McpServerConfigRaw}>}
     * filePath: mcp.json 相对工作区的相对路径
     * startLine: 起始行数
     * endLine: 结束行数
     * config: mcpServers 完整配置
     * @throws {Error} 获取文件失败
     */
    async fetchMcpServerConfig(serverName?: string): Promise<{
        filePath: string;
        startLine: number;
        endLine: number;
        config?: Record<string, McpServerConfigRaw>;
    }> {
        let startLine = 0;
        let endLine = 0;
        try {
            const filePath = MCP_SETTINGS_FILE_PATH;
            if (!this.serverSettings || !serverName || !this.serverSettings[serverName]) {
                return {filePath, startLine, endLine, config: this.serverSettings};
            }
            const content = JSON.stringify({mcpServers: this.serverSettings}, null, 4);
            const {pointers} = jsonSourcemap.parse(content);
            const pointer = pointers[`/mcpServers/${serverName}`];
            if ('key' in pointer) {
                startLine = pointer.key?.line || 0;
            }
            endLine = pointer?.valueEnd?.line || 0;
            return {filePath, startLine, endLine};
        }
        catch (error) {
            this.logger.error('获取 MCP 设置文件失败', (error as Error).message);
            throw error;
        }
    }

    /**
     * 更新 MCP 设置文件
     * @param {string} serverName - 服务器名称
     * @param {'add' | 'delete' | 'update'} type - 操作类型
     * @param {McpServerConfigRaw} config - 服务器配置
     * @returns {Promise<{filePath: string, startLine: number, endLine: number, config?: Record<string, McpServerConfigRaw}>}
     * filePath: mcp.json 相对工作区的相对路径
     * startLine: 起始行数
     * endLine: 结束行数
     * config: mcpServers 完整配置
     * @throws {Error} 更新文件失败
     */
    async updateServerSettings({
        serverName,
        type,
        config,
    }: {
        serverName: string;
        type: 'add' | 'delete' | 'update';
        config?: McpServerConfigRaw;
    }): Promise<{
        filePath: string;
        startLine: number;
        endLine: number;
        config?: Record<string, McpServerConfigRaw>;
    }> {
        const filePath = await this.createMcpServerConfig();
        if (!filePath) {
            throw new Error('未找到 MCP 设置文件');
        }
        try {
            let startLine = 0;
            let endLine = 0;
            if (type === 'add') {
                if (!this.serverSettings) {
                    this.serverSettings = {};
                }
                this.serverSettings = {
                    [serverName]: config as McpServerConfig,
                    ...this.serverSettings,
                };
            }
            else if (type === 'delete') {
                if (this.serverSettings && this.serverSettings[serverName]) {
                    delete this.serverSettings[serverName];
                }
            }
            else if (type === 'update') {
                if (this.serverSettings && this.serverSettings[serverName]) {
                    const curConfig = this.serverSettings[serverName];
                    this.serverSettings[serverName] = {
                        ...curConfig,
                        ...config,
                    } as McpServerConfig;
                }
            }
            const content = JSON.stringify({mcpServers: this.serverSettings}, null, 4);
            await this.virtualEditor.saveDocumentWithReplaceContentAndOpen({
                absolutePath: this.mcpSettingsFilePath,
                content,
            });
            await this.virtualEditor.openDocument({absolutePath: this.mcpSettingsFilePath});
            if (type === 'delete' || type === 'add') {
                return {filePath, startLine, endLine, config: this.serverSettings};
            }
            // 被修改的 Server 高亮
            const {pointers} = jsonSourcemap.parse(content);
            const pointer = pointers[`/mcpServers/${serverName}`];
            if ('key' in pointer) {
                startLine = pointer.key?.line || 0;
            }
            endLine = pointer?.valueEnd?.line || 0;
            return {filePath, startLine, endLine, config: this.serverSettings};
        }
        catch (error) {
            this.logger.error('更新 MCP 设置文件失败', (error as Error).message);
            throw error;
        }
    }

    async dispose() {
        this.watcher?.close();
        this.watcher = undefined;
        this.serverSettings = undefined;
        this.mcpSettingsSnapshot = undefined;
    }
}
