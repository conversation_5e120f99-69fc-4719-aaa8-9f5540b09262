import {Client} from '@modelcontextprotocol/sdk/client/index.js';
import {StdioClientTransport} from '@modelcontextprotocol/sdk/client/stdio.js';
import {SSEClientTransport} from '@modelcontextprotocol/sdk/client/sse.js';
import {StreamableHTTPClientTransport} from '@modelcontextprotocol/sdk/client/streamableHttp.js';
import {
    CallToolResultSchema,
    ListToolsResultSchema,
} from '@modelcontextprotocol/sdk/types.js';
import {setTimeout as setTimeoutPromise} from 'node:timers/promises';
import deepEqual from 'fast-deep-equal';
import {
    kernel,
    PT_KERNEL_MCP_CONNECTION_LIST_DID_CHANGE,
    PT_WEBVIEW_MCP_CONNECTION_LIST_FETCH,
    PT_WEBVIEW_MCP_CONNECTION_LIST_RELOAD,
} from '@comate/kernel-shared';
import {MCPServerForWebview, VirtualEditor} from '@comate/plugin-shared-internals';
import {Mediator} from '../Mediator.js';
import {LoggerRoleInstance} from '../Logger.js';
import {McpSettingsWatcher} from './McpSettingsWatcher.js';
import {
    McpServerConfig,
    McpServer,
    McpTool,
    McpToolCallResponse,
    McpConnection,
} from './types.js';
import {DEFAULT_MCP_TIMEOUT_SECONDS, expandTilde, filterComateEnv, secondsToMs} from './utils.js';
import ConcurrentPool from './ConcurrentPool.js';
import McpLogger from './McpLogger.js';
import {decodeBuffer} from '../../utils/encoding.js';

const DEFAULT_REQUEST_TIMEOUT_MS = 5000;

export class McpManager {
    mediator: Mediator;
    mcpLogger: McpLogger;
    private settingsWatcher: McpSettingsWatcher;
    /* 记录 MCP Config 文件是否合法，当文件不合法时，不会更新connections */
    error?: {message: string, path: string};
    connections: McpConnection[] = [];
    isConnecting: boolean = false;
    isReady: boolean = false;
    private pool?: ConcurrentPool;

    constructor(mediator: Mediator, virtualEditor: VirtualEditor, private readonly logger: LoggerRoleInstance) {
        this.mediator = mediator;
        this.mcpLogger = new McpLogger(mediator);
        this.settingsWatcher = new McpSettingsWatcher(this.mcpLogger, virtualEditor);
        this.initializeMcpServers();
        kernel.connect.onWebviewMessage(
            PT_WEBVIEW_MCP_CONNECTION_LIST_RELOAD,
            this.reloadMcpServers.bind(this)
        );
        kernel.connect.onWebviewMessage(
            PT_WEBVIEW_MCP_CONNECTION_LIST_FETCH,
            async () => {
                return {
                    servers: this.getConnectionsForWebview(),
                    error: this.error,
                    isReady: this.isReady,
                };
            }
        );
    }

    getServers(): McpServer[] {
        return this.connections.map(conn => conn.server);
    }

    /**
     * 初始化 MCP Servers
     */
    private async initializeMcpServers(): Promise<void> {
        try {
            await kernel.connect.webviewInitialized;
            const mcpServers = await this.settingsWatcher.getServerSettings();
            if (Object.keys(mcpServers).length) {
                this.error = undefined;
                await this.updateServerConnections(mcpServers);
                return;
            }
        }
        catch (error: any) {
            // 初始化 MCP Servers 失败，如果是文件不存在，则不写入也不抛出错误
            // 因为用户可能还没有配置 MCP Server，这种情况不属于错误
            if (error?.code !== 'ENOENT') {
                // 其他类型错误，则抛出错误并通知用户
                this.error = {
                    message: `MCP Config 解析失败, ${(error as Error).message}`,
                    path: this.mcpLogger.normalizedAbsolutedPath,
                };
                this.mcpLogger.error(this.error.message);
            }
        }
        this.mcpLogger.info('推送 MCP Servers 列表到 Webview');
        await this.notifyWebviewOfServerChanges();
    }

    /**
     * webview 刷新 MCP Servers 列表
     * @returns {error?: string; servers?: MCPServerForWebview[]}
     * @description 主要用于 webview 获取 MCP Servers 列表
     * @example
     */
    private async reloadMcpServers(id: number): Promise<void> {
        try {
            this.error = undefined;
            const mcpServers = await this.settingsWatcher.getServerSettings();
            await this.updateServerConnections(mcpServers, id);
        }
        catch (error) {
            const errorMessage = `更新 MCP Servers 失败: ${(error as Error).message}`;
            this.mcpLogger.error(errorMessage);
            this.error = {
                message: errorMessage,
                path: this.mcpLogger.normalizedAbsolutedPath,
            };
            await this.notifyWebviewOfServerChanges(id);
        }
    }

    /**
     * 连接到 MCP Server
     * @param name Server 名称
     * @param config Server 配置
     */
    private async connectToServer(name: string, config: McpServerConfig): Promise<void> {
        this.connections = this.connections?.filter(conn => conn.server.name !== name);

        try {
            const client = new Client(
                {
                    name: 'comate',
                    version: '1.0.0',
                },
                {
                    capabilities: {},
                }
            );

            let transport: StdioClientTransport | SSEClientTransport | StreamableHTTPClientTransport;

            if (config.transportType === 'streamableHttp') {
                transport = new StreamableHTTPClientTransport(new URL(config.url), {});
            }
            else if (config.transportType === 'sse') {
                transport = new SSEClientTransport(new URL(config.url), {});
            }
            else {
                transport = new StdioClientTransport({
                    command: config.command,
                    args: config.args?.map(expandTilde),
                    cwd: config.cwd || kernel.env.workspaceInfo.rootPath,
                    env: {
                        ...filterComateEnv(),
                        ...config.env,
                    },
                    stderr: 'pipe',
                });
            }

            transport.onerror = async error => {
                this.mcpLogger.error(`Transport error for "${name}":`, (error as Error).message);
                const connection = this.connections?.find(conn => conn.server.name === name);
                if (connection) {
                    connection.server.status = 'disconnected';
                    this.appendErrorMessage(connection, error.message);
                }
                await this.notifyWebviewOfServerChanges();
            };

            transport.onclose = async () => {
                const connection = this.connections?.find(conn => conn.server.name === name);
                if (connection) {
                    connection.server.status = 'disconnected';
                }
                await this.notifyWebviewOfServerChanges();
            };

            const connection: McpConnection = {
                server: {
                    name,
                    config: JSON.stringify(config),
                    status: 'connecting',
                    disabled: config.disabled,
                    logPath: this.mcpLogger.normalizedAbsolutedPath,
                },
                client,
                transport,
            };
            this.connections.push(connection);

            if (config.transportType === 'stdio') {
                // transport.stderr is only available after the process has been started. However we can't start it separately from the .connect() call because it also starts the transport. And we can't place this after the connect call since we need to capture the stderr stream before the connection is established, in order to capture errors during the connection process.
                // As a workaround, we start the transport ourselves, and then monkey-patch the start method to no-op so that .connect() doesn't try to start it again.
                await transport.start();
                const stderrStream = (transport as StdioClientTransport).stderr;
                if (stderrStream) {
                    stderrStream.on('data', async (data: Buffer) => {
                        const output = decodeBuffer(data); // 自动判断并解码
                        this.mcpLogger.error(`Server "${name}" stderr:`, output);
                        const connection = this.connections?.find(conn => conn.server.name === name);
                        if (connection) {
                            this.appendErrorMessage(connection, output);
                        }
                    });
                }
                else {
                    this.mcpLogger.error(`No stderr stream for ${name}`);
                }
                transport.start = async () => {};
            }

            // Connect
            await client.connect(transport);

            connection.server.status = 'connected';
            connection.server.error = '';

            connection.server.tools = await this.fetchToolsList(name);
        }
        catch (error) {
            const connection = this.connections?.find(conn => conn.server.name === name);
            if (connection) {
                connection.server.status = 'disconnected';
                this.appendErrorMessage(connection, error instanceof Error ? error.message : String(error));
            }
            throw error;
        }
    }

    private appendErrorMessage(connection: McpConnection, error: string) {
        const newError = connection.server.error ? `${connection.server.error}\n${error}` : error;
        connection.server.error = newError;
    }

    private async fetchToolsList(serverName: string): Promise<McpTool[]> {
        try {
            const connection = this.connections?.find(conn => conn.server.name === serverName);

            if (!connection) {
                throw new Error(`Server ${serverName} 未连接，请检查一下连接状态`);
            }

            const response = await connection.client.request(
                {method: 'tools/list'},
                ListToolsResultSchema,
                {timeout: DEFAULT_REQUEST_TIMEOUT_MS}
            );

            const tools = (response?.tools || []).map(tool => ({
                ...tool,
                // autoApprove 本期不支持，此处暂时设置为false。后续根据实际需求调整。
                autoApprove: false,
            }));
            return tools;
        }
        catch (error) {
            this.mcpLogger.error(`获取Server ${serverName} tools 列表失败:`, (error as Error).message);
            return [];
        }
    }

    async deleteConnection(name: string): Promise<void> {
        const connection = this.connections?.find(conn => conn.server.name === name);
        if (connection) {
            try {
                await connection.transport.close();
                await connection.client.close();
            }
            catch (error) {
                this.mcpLogger.error(`Server ${name} 连接关闭失败:`, (error as Error).message);
            }
            this.connections = this.connections.filter(conn => conn.server.name !== name);
        }
    }

    private async updateServerConnections(newServers: Record<string, McpServerConfig>, id?: number): Promise<void> {
        this.isConnecting = true;
        const currentNames = new Set(this.connections.map(conn => conn.server.name));
        const newNames = new Set(Object.keys(newServers));

        this.pool = new ConcurrentPool(5);

        for (const name of currentNames) {
            if (!newNames.has(name)) {
                this.pool.add(async () => {
                    await this.deleteConnection(name);
                    this.mcpLogger.info(`删除Server ${name} 成功`);
                });
            }
        }

        for (const [name, config] of Object.entries(newServers)) {
            const currentConnection = this.connections?.find(conn => conn.server.name === name);

            if (!currentConnection) {
                this.pool.add(async () => {
                    try {
                        await this.connectToServer(name, config);
                    }
                    catch (error) {
                        this.mcpLogger.error(`连接Server ${name} 失败: ${(error as Error).message}`);
                        this.error = {
                            message: '',
                            path: this.mcpLogger.normalizedAbsolutedPath,
                        };
                    }
                });
            }
            else if (!deepEqual(JSON.parse(currentConnection.server.config), config)) {
                this.pool.add(async () => {
                    try {
                        await this.deleteConnection(name);
                        await this.connectToServer(name, config);
                        this.mcpLogger.info(`重新连接Server ${name} 成功`);
                    }
                    catch (error) {
                        this.mcpLogger.error(`重新连接Server ${name} 失败: ${(error as Error).message}`);
                        this.error = {
                            message: '',
                            path: this.mcpLogger.normalizedAbsolutedPath,
                        };
                    }
                });
            }
        }
        await this.pool.allTasksDone();
        this.pool = undefined;
        this.isReady = true;
        await this.notifyWebviewOfServerChanges(id);
        this.isConnecting = false;
    }

    /**
     * 重新连接 MCP Server
     * 暂时没用到，后续根据需求调整
     * @param serverName Server 名称
     */
    async restartConnection(serverName: string): Promise<void> {
        this.isConnecting = true;
        const connection = this.connections?.find(conn => conn.server.name === serverName);
        const config = connection?.server.config;
        if (config) {
            connection.server.status = 'connecting';
            connection.server.error = '';
            await this.notifyWebviewOfServerChanges();
            await setTimeoutPromise(500);
            try {
                await this.deleteConnection(serverName);
                await this.connectToServer(serverName, JSON.parse(config));
            }
            catch (error) {
                this.mcpLogger.error(`重新连接Server ${serverName} 失败: ${(error as Error).message}`);
            }
        }

        await this.notifyWebviewOfServerChanges();
        this.isConnecting = false;
    }

    getConnectionsForWebview(): MCPServerForWebview[] {
        return this.connections.map(conn => ({
            name: conn.server.name,
            disabled: conn.server.disabled,
            status: conn.server.status,
            error: conn.server.error,
            logPath: conn.server.logPath,
            tools: conn.server.tools,
        }));
    }

    private async notifyWebviewOfServerChanges(id?: number): Promise<void> {
        const servers = this.getConnectionsForWebview();
        await kernel.connect.sendWebviewMessage(
            PT_KERNEL_MCP_CONNECTION_LIST_DID_CHANGE,
            {error: this.error, servers, isReady: this.isReady, id}
        );
    }

    async sendLatestMcpServers() {
        await this.notifyWebviewOfServerChanges();
    }

    async callTool(
        serverName: string,
        toolName: string,
        toolArguments?: Record<string, unknown>
    ): Promise<McpToolCallResponse> {
        const uuid = crypto.randomUUID();
        this.logger.logUploader?.logUserAction({
            category: 'mcp',
            action: 'callToolStart',
            label: uuid,
            content: `serverName: ${serverName} toolName: ${toolName}, toolArguments: ${JSON.stringify(toolArguments)}`,
        });

        const connection = this.connections?.find(
            conn => conn.server.name === serverName && conn.server.status === 'connected'
        );
        if (!connection) {
            throw new Error(
                `Server ${serverName} 未连接. 请检查 Server ${serverName} 连接状态.`
            );
        }

        let timeout = secondsToMs(DEFAULT_MCP_TIMEOUT_SECONDS);

        try {
            const config = JSON.parse(connection.server.config);
            timeout = config.timeout ? secondsToMs(config.timeout as number) : timeout;
        }
        catch (error) {
            this.mcpLogger.error(`解析Server ${serverName} 超时设置失败: ${(error as Error).message}`);
        }

        const result = await connection.client.request(
            {
                method: 'tools/call',
                params: {
                    name: toolName,
                    arguments: toolArguments,
                },
            },
            CallToolResultSchema,
            {
                timeout,
            }
        );

        this.logger.logUploader?.logUserAction({
            category: 'mcp',
            action: 'callToolEnd',
            label: uuid,
            content: `error: ${result?.isError}`,
        });
        return result;
    }

    async dispose(): Promise<void> {
        for (const connection of this.connections) {
            try {
                await this.deleteConnection(connection.server.name);
            }
            catch (error) {
                this.mcpLogger.error(`关闭Server ${connection.server.name} 失败:`, (error as Error).message);
            }
        }
        this.connections = [];
        this.settingsWatcher.dispose();
        this.mcpLogger.dispose();
    }
}
