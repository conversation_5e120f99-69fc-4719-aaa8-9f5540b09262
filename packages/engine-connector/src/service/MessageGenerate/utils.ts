import fs from 'node:fs/promises';
import {constants as fsConstants} from 'node:fs';
import * as git from 'isomorphic-git';
import * as path from 'path';
import {createPatch} from 'diff';
import {isVscode} from '../../utils/checkIDE.js';

export function formatPrompt(promptTemplate: string, args?: Record<string, unknown>) {
    if (args) {
        return promptTemplate.replace(
            /\{\{(\w+)\}\}/g,
            (match, key) => {
                const replacement = args[key];
                return replacement === undefined ? match : String(replacement);
            }
        );
    }

    return promptTemplate;
}

export const getReadmeContent = async (rootPath: string): Promise<string> => {
    try {
        const readmePath = path.join(rootPath, 'README.md');
        await fs.access(readmePath, fsConstants.F_OK);
        const stats = await fs.stat(readmePath);
        // 处理文件大小超过限制的情况
        const maxFileSize = 1024 * 1024; // 设置读取文件大小限制为1MB
        if (stats.size > maxFileSize) {
            throw new Error(`File size of ${readmePath} (${stats.size} bytes) exceeds maximum size of ${maxFileSize} bytes. Skipping...`);
        }
        const data = await fs.readFile(readmePath, 'utf8');
        return data;
    }
    catch (error) {
        throw new Error(`Error accessing file or directory: ${error}`);
    }
};

const formatDiffBlock = (text: string) => {
    return text
        .split('\n')
        .filter(line => line.startsWith('+') || line.startsWith('-'))
        .map(line => line.replace(/^(---|\+\+\+) /, '').replace(/^[+-] */, ''));
};

const ignoreFiles = ['yarn.lock'];

const generateFileDiff = async (cwd: string, filepath: string): Promise<string> => {

    try {
        const currentCommit = await git.resolveRef({fs, dir: cwd, ref: 'HEAD'})

        const {blob} = await git.readBlob({
            fs,
            dir: cwd,
            oid: currentCommit,
            filepath,
        });

        const stagedContent = await fs.readFile(
            path.join(cwd, filepath),
            'utf8'
        );

        return createPatch(
            filepath,
            Buffer.from(blob).toString('utf8'),
            stagedContent
        );
    }
    catch (error) {
        try {
            // 新增文件的处理
            const stagedContent = await fs.readFile(
                path.join(cwd, filepath),
                'utf8'
            );

            return createPatch(
                filepath,
                '',
                stagedContent
            );
        }
        catch (error) {
            // 文件不存在 即删除场景
            return `+ ${filepath} has been deleted`;
        }
    }
};

export const getStagedDiffContent = async (cwd: string, paths: string[] = []): Promise<string> => {
    const FILE = 0, HEAD = 1, STAGE = 3
    // 对于 VSCode 环境，处理 staged 文件
    if (isVscode) {
        const stagedFiles = (await git.statusMatrix({fs, dir: cwd}))
            .filter(row => row[HEAD] !== row[STAGE])
            .map(row => row[FILE]);

        const diffPromises = stagedFiles.map(file => generateFileDiff(cwd, file));
        const diffs = await Promise.all(diffPromises);
        return diffs.join('\n');
    }
    // 对于非 VSCode 环境，获取传入路径的diff内容
    else {
        const relativePaths = paths.map(p => path.relative(cwd, p));
        const diffPromises = relativePaths.map(file => generateFileDiff(cwd, file));
        const diffs = await Promise.all(diffPromises);
        return diffs.join('\n');
    }
};

const getDefaultLog = async (cwd: string): Promise<string> => {
    try {
        const commits = await git.log({fs, dir: cwd, depth: 10});
        return commits.map(commit => commit.commit.message.replace(/\n\nChange-Id:.*/, '')).join('\n');
    }
    catch {
        return '';
    }
};

export const getLog = async (cwd: string): Promise<string> => {
    try {
        const username = await git.getConfig({fs, dir: cwd, path: 'user.name'});

        const commits = await git.log({fs, dir: cwd, depth: 30});
        const filteredByUserCommits = username
        ? commits.filter(commit =>
            commit.commit.author.name === username ||
            commit.commit.committer.name === username
        )
        : commits;

        if (filteredByUserCommits.length === 0) {
            const history = await getDefaultLog(cwd);
            return history;
        }
        return filteredByUserCommits.map(commit =>
            commit.commit.message.replace(/\n\nChange-Id:.*/, '')
        ).join('\n');
    }
    catch (e) {
        const history = await getDefaultLog(cwd);
        return history;
    }
};

export const getCommittedDiff = async (cwd: string, paths?: string[]) => {
    try {
        const stagedDiff = await getStagedDiffContent(cwd, paths);
        if (!stagedDiff) {
            return {diffStatus: 'success', diff: ''};
        }
        const list = stagedDiff
            .split(/^diff --git /)
            .map(formatDiffBlock)
        // 过滤掉无用的文件，减少diff的长度，暂时只有 yarn.lock ，
            .filter(v => v.length && ignoreFiles.every(file => !v[0].endsWith(file)))
            .map(v => v.join(' '));
        const diffText = list.join(' ').slice(0, 10000);
        if (diffText.length === 0) {
            return {diffStatus: 'error', diff: '', errorMessage: 'Unable to retrieve diff. Staged changes format is incorrect.'};
        }
        return {diffStatus: 'success', diff: diffText};
    }
    catch (ex) {
        return {diffStatus: 'fail', diff: '', errorMessage: (ex as Error).message};
    }
};

export const extractJsonFromString = (input: string): string | null => {
    const jsonBlockRegex = /```json\n([\s\S]*?)\n```/;
    const match = jsonBlockRegex.exec(input);
    return match ? match[1] : input;
};

export const formatIssueMessage = (message: string) => {
    const jsonString = extractJsonFromString(message);
    if (jsonString) {
        try {
            const value = JSON.parse(jsonString);
            return value?.result;
        }
        catch {
            throw new Error('format issue message error.');
        }
    }
    return null;
};

export function extractIdAndTitle(text: string) {
    const regex = /\[(.*?)\](.*)/;
    const match = regex.exec(text);
    if (match) {
        const id = match[1].trim();
        const title = match[2].trim();
        return {id, title};
    }

    return {id: '', title: text};
}
