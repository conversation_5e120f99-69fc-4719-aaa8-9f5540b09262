// import path from 'node:path';
import {distance} from 'fastest-levenshtein';
import {isVscode} from '../../utils/checkIDE.js';
import {isInternal} from '../../utils/isSaaS.js';
import {LoggerRoleInstance} from '../Logger.js';
import {getIssuesByQuery, apiInferCardScoreByCommittedDiff, getSpaceDetailByHistory, SpaceDetail} from '../../api/issueGenerate.js';
import {Mediator} from '../Mediator.js';
import {extractIdAndTitle, getCommittedDiff, getLog, formatIssueMessage, getReadmeContent} from './utils.js';
import {internalMessagePrompt, saasMessagePrompt, descriptionPrompt} from './prompt.js';

export class MessageGenerate {
    private readonly logger: LoggerRoleInstance;
    private readonly mediator: Mediator;
    private readonly cwd: string;
    private readonly pluginName: string = 'generateMessage';
    private projectDescription: string | undefined;
    private lastGenerateData = new Map<string, {issueId?: string, message: string}>();
    private spaceDetails: SpaceDetail | undefined;
    constructor(mediator: Mediator, logger: LoggerRoleInstance, cwd: string) {
        this.logger = logger;
        this.mediator = mediator;
        this.cwd = cwd;
    }

    async initProjectDescription() {
        try {
            if (!this.cwd || this.cwd.length === 0) {
                return;
            }
            const readme = await getReadmeContent(this.cwd);

            const prompt = {
                promptTemplate: descriptionPrompt,
                args: {projectName: this.cwd, readme},
                pluginName: this.pluginName,
            };
            const projectDescription = await this.mediator.llmApi.askForText(prompt);
            this.projectDescription = projectDescription;
        }
        catch (ex) {
            this.logger.error('initProjectDescription error:', (ex as Error).message);
        }
    }

    async getSpaceDetail() {
        try {
            if (!isInternal()) {
                return;
            }
            if (!this.cwd || this.cwd.length === 0) {
                this.logger.info('cwd is undefined when get space detail');
                return;
            }
            this.logger.info('spaceDetail cwd:', this.cwd);
            const hisMsgs = await getLog(this.cwd);
            const userDetail = this.mediator.userDetail();
            const username = userDetail?.username || '';
            const data = await getSpaceDetailByHistory({
                task_engine_userId: username,
                hisMsg: hisMsgs,
            });
            this.logger.info('spaceDetail data:', data);
            this.spaceDetails = data.spaceDetails;
        }
        catch (ex) {
            this.logger.error('getSpaceDetail error:', (ex as Error).message);
        }
    }

    init() {
        this.initProjectDescription();
        this.getSpaceDetail();
    }

    async generateInternalPrompt(repo: string, diff: string, history: string) {
        try {
            // 兼容厂内非icode开发场景
            if (!repo.includes('baidu')) {
                return this.generateSaasPrompt(repo, diff, history);
            }
            const userDetail = this.mediator.userDetail();
            const username = userDetail?.username || '';
            const {content = []} = await getIssuesByQuery({
                query: '最近14天我负责的未完成的卡片',
                // eslint-disable-next-line camelcase
                task_engine_userId: username,
                codeModule: repo,
                hisMsg: history,
                source: '2',
                from: isVscode ? 'vscode' : 'jetbrains',
                spaceDetails: this.spaceDetails,
            });
            this.logger.info('generateInternalPrompt username:', username);
            this.logger.info('generateInternalPrompt initialIssues:', content);
            if (!content || content.length === 0) {
                return this.generateSaasPrompt(repo, diff, history);
            }
            const titles = content.map(item => item.cardIdAndTile);
            const response = await apiInferCardScoreByCommittedDiff({
                diff,
                titles,
            });
            const generateIssue = content[response.result.rank.findIndex(value => value === 0) || 0];
            const {id, title} = extractIdAndTitle(generateIssue.cardIdAndTile);
            const prompt = {
                promptTemplate: internalMessagePrompt,
                args: {history, id, title, type: generateIssue.cardType, modifications: diff},
                pluginName: this.pluginName,
            };
            return {issueId: id, prompt};
        }
        catch (ex) {
            this.logger.error('generateInternalPrompt error:', (ex as Error).message);
            return this.generateSaasPrompt(repo, diff, history);
        }
    }

    generateSaasPrompt(repo: string, diff: string, history: string) {
        const userDetail = this.mediator.userDetail();
        const language = userDetail?.language === 'en' ? '英文' : '中文';
        const prompt = {
            promptTemplate: saasMessagePrompt,
            args: {
                projectName: repo,
                projectDescription: this.projectDescription,
                modifications: diff,
                history,
                language,
            },
            pluginName: this.pluginName,
            device: userDetail?.device,
        };
        return {issueId: undefined, prompt};
    }

    async report(message: string, cwd?: string) {
        const currentCwd = cwd || this.cwd;
        if (!currentCwd || currentCwd.length === 0) {
            return;
        }
        const data = this.lastGenerateData.get(currentCwd);
        this.logger.info('generateReport lastGenerateData:', data);
        if (data && data.message) {
            const generateMessage = data.message;
            // 计算message和data.message的字符串编辑距离
            const editDistance = distance(message, generateMessage);
            const maxLength = Math.max(message.length, generateMessage.length);
            const acceptanceRate = ((1 - (editDistance / maxLength)) * 100).toFixed(2);
            const issueAccept = data.issueId ? generateMessage.includes(data.issueId) : undefined;
            const acceptData = {
                issueAccept,
                acceptanceRate,
                issueId: data.issueId,
                commitMessage: message,
                generateMessage: generateMessage,
            };
            this.logger.info('generateReport acceptData:', acceptData);
            this.logger.logUploader?.logUserAction({
                category: 'issueGenerate',
                label: 'generateReport',
                content: acceptData,
            });
        }
    }

    async generateCommitMessage(paths?: string[], cwd?: string) {
        try {
            this.logger.info('generateCommitMessage paths:', paths);
            this.logger.info('generateCommitMessage this.cwd:', this.cwd);
            this.logger.info('generateCommitMessage cwd:', cwd);
            const currentCwd = cwd || this.cwd;
            const userDetail = this.mediator.userDetail();
            const isEnglish = userDetail?.language === 'en';
            if (!currentCwd || currentCwd.length === 0) {
                return {
                    message: '',
                    status: 'error',
                    errorMessage: isEnglish ? 'Please open a valid project directory' : '未打开有效的工程目录',
                };
            }
            if (cwd !== this.cwd) {
                // init的cwd和生成的cwd不一致时，需要重新生成
                this.init();
            }
            const {diff, diffStatus, errorMessage} = await getCommittedDiff(currentCwd, paths);
            this.logger.info(
                'generateCommitMessage diff:',
                diff,
                'diffStatus',
                diffStatus,
                'errorMessage',
                errorMessage
            );

            if (diffStatus !== 'success') {
                return {
                    message: '',
                    status: 'error',
                    errorMessage: isEnglish
                        ? `Failed to retrieve diff content: ${errorMessage}`
                        : `获取变更内容失败：${errorMessage}`,
                };
            }
            if (diff.length === 0) {
                return {
                    message: '',
                    status: 'error',
                    errorMessage: isEnglish ? 'Failed to detect any content changes.' : '未检测到变更内容',
                };
            }
            const history = await getLog(currentCwd);
            this.logger.info('generateCommitMessage history:', history);
            const {issueId, prompt} = isInternal()
                ? await this.generateInternalPrompt(currentCwd, diff, history)
                : this.generateSaasPrompt(currentCwd, diff, history);
            const message = await this.mediator.llmApi.askForText(prompt);
            this.logger.info('generateCommitMessage original message:', message);
            const formatMessage = formatIssueMessage(message);
            if (!formatMessage || formatMessage.length === 0) {
                return {
                    message: '',
                    status: 'error',
                    errorMessage: isEnglish ? 'Failed to generate commit message.' : '未能生成commit message',
                };
            }
            this.lastGenerateData.set(currentCwd, {issueId, message: formatMessage});
            this.logger.logUploader?.logUserAction({
                category: 'issueGenerate',
                label: 'sourceControlGenerateMessage',
                content: {commitMessage: formatMessage, diff, repo: currentCwd},
            });
            return {message: formatMessage, status: 'success'};
        }
        catch (ex) {
            this.logger.error('generateCommitMessage error:', (ex as Error).message);
            return {message: '', status: 'error', errorMessage: (ex as Error).message};
        }
    }
}
