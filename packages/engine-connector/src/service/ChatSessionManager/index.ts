import {ChatSession, ChatSessionDetail} from '@comate/plugin-shared-internals';
import {LoggerRoleInstance} from '../Logger.js';
import {Mediator} from '../Mediator.js';
import {ArrayStore} from '../Store/ArrayStore.js';
import {ObjectStore} from '../Store/ObjectStore.js';

export interface ListingOptions {
    offset?: number;
    limit?: number;
    source?: ChatSessionDetail['source'];
}

export class ChatSessionManager {
    private sessionsStore: ArrayStore<ChatSession>;
    private sessionStoreMap = new Map<string, ObjectStore<ChatSessionDetail>>();

    private getStorePrefix() {
        // vscode 保持原来的存储方式，不加前缀
        if (process.env.IDE === 'vscode') {
            return '';
        }
        return `${process.env.IDE || 'default'}_`;
    }

    /**
     * Mediator的构造函数，接收两个参数：mediator和logger。
     * mediator是一个Mediator实例，用于处理中介者角色；
     * logger是一个LoggerRoleInstance实例，用于记录日志。
     *
     * @param mediator Mediator实例，处理中介者角色
     * @param logger LoggerRoleInstance实例，记录日志
     */
    constructor(
        private readonly mediator: Mediator,
        private readonly rootPath: string,
        private readonly logger: LoggerRoleInstance
    ) {
        // 初始化带有 IDE 前缀的 sessionsStore
        const prefix = this.getStorePrefix();
        this.sessionsStore = new ArrayStore<ChatSession>(`${prefix}chat_sessions`, []);
    }

    getSessionStore = (sessionUuid: string, defaultData?: ChatSessionDetail) => {
        if (this.sessionStoreMap.has(sessionUuid)) {
            return this.sessionStoreMap.get(sessionUuid)!;
        }
        const store = new ObjectStore<ChatSessionDetail>(
            `chat_session_${sessionUuid}`,
            {
                messages: [],
                title: '',
                workspaceDirectory: '',
                sessionUuid: sessionUuid,
                ...defaultData,
            } as ChatSessionDetail
        );

        this.sessionStoreMap.set(sessionUuid, store);
        return store;
    };

    /**
     * @description
     * 获取聊天会话列表，支持分页和排序。
     *
     * @param options {ListingOptions} - 可选参数，包含以下属性：
     * - limit {number} - 限制返回的结果数量，默认不限制；
     * - offset {number} - 从第几个结果开始返回，默认为0；
     * - source {string} - 是否只返回一种来源的会话
     *
     * @returns {ChatSession[]} - 返回一个包含所有聊天会话信息的数组，按照时间降序排序；如果文件不存在或无法解析，则返回空数组。
     */
    async list(options: ListingOptions = {}): Promise<ChatSession[]> {
        let sessions: ChatSession[] = await this.sessionsStore.get();
        // 非对话类历史，仅展示workspaceDirectory一致的会话
        sessions = sessions.filter(session => {
            if (!session.source || session.source === 'Chat') {
                return true;
            }
            return session.workspaceDirectory === this.rootPath;
        });

        if (options.source !== undefined) {
            sessions = sessions.filter(session => session.source === options.source);
        }

        if (options.limit) {
            const offset = options.offset || 0;
            sessions = sessions.slice(offset, offset + options.limit);
        }

        return sessions.sort((a: ChatSession, b: ChatSession) => b.utime - a.utime);
    }

    async delete(sessionUuid: string) {
        try {
            await this.sessionsStore.delete(session => session.sessionUuid === sessionUuid);
            await this.getSessionStore(sessionUuid).drop();
            // 清理 sessionStoreMap
            this.sessionStoreMap.delete(sessionUuid);
        }
        catch (error) {
            this.logger.error(
                `Delete chat session failed. Error: ${error}`
            );
        }
    }

    async find(sessionUuid: string): Promise<ChatSessionDetail> {
        const store = await this.getSessionStore(sessionUuid);
        await store.read();
        return store.get();
    }

    async save(session: ChatSessionDetail) {
        try {
            session.title = typeof session.title === 'object' ? JSON.stringify(session.title) : session.title;

            // 更新详情信息
            const sessionStore = this.getSessionStore(session.sessionUuid);
            if (session.messages.length > 0) {
                session.utime = session.utime || Date.now();
                sessionStore.set(session);
            }
            else {
                sessionStore.drop();
                // 清理空会话的 store
                this.sessionStoreMap.delete(session.sessionUuid);
            }

            // 更新列表信息
            const sessionsStore = this.sessionsStore;

            if (session.messages.length > 0) {
                sessionsStore._update(
                    data => {
                        const existSession = data.find(s => s.sessionUuid === session.sessionUuid);

                        if (existSession) {
                            return data.map(
                                s => {
                                    if (s.sessionUuid === session.sessionUuid) {
                                        const {messages, ...rest} = session;
                                        return rest;
                                    }
                                    return s;
                                }
                            );
                        }
                        else {
                            const {messages, ...rest} = session;
                            return [...data, rest];
                        }
                    }
                );
            }
            else {
                sessionsStore.delete(s => s.sessionUuid === session.sessionUuid);
            }
        }
        catch (error) {
            this.logger.error(
                `Save chat session failed. Error: ${error}`
            );
        }
    }
}
