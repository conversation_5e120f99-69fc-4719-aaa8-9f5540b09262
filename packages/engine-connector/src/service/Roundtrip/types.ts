import {
    AgentComposerTask,
    AgentMessageStatus,
    AgentTextElement,
    WorkflowStatus,
} from '@comate/plugin-shared-internals';
import {WorkflowResult} from './Workflow/WorkflowBase.js';
export {AgentMessageStatus as MessageStatus} from '@comate/plugin-shared-internals';

export enum MessageType {
    Text = 'TEXT',
    ToolCall = 'TOOL_CALL',
    REASON = 'REASON',
    UserRequest = 'USER_REQUEST',
}

export interface UserMessagePayload {
    id?: string;
    query: string;
    code?: string;
    defaultContent?: string;
}

export type ToolCallEventType =
    | 'TOOL_CALL_END'
    | 'TOOL_CALL_ABORT'
    | 'TOOL_CALL_START'
    | 'TOOL_CALL_CREATE'
    | 'TOOL_CALL_PARAMS_MERGE'
    | 'TOOL_CALL_PARAMS_APPEND';

export type ToolCallEventParams = Record<string, string | number>;

export interface ToolCallEvent {
    eventType: ToolCallEventType;
    name?: string;
    params?: ToolCallEventParams;
}

export interface TextMessageSnapshot extends AgentTextElement {
    originalContent: string;
}

export interface ReasonMessageSnapshot {
    type: 'REASON';
    id: number;
    text: string;
    status: AgentMessageStatus;
    startTime: number;
    lastModifiedTime: number;
}

export interface ToolCallMessageSnapshot {
    id: string;
    type: 'TOOL_CALL';
    status: AgentMessageStatus;
    composer: {
        markdown: string;
        tasks: AgentComposerTask[];
    };
    toolName: string;
    workflowSnapshot: WorkflowSnapshot;
    params: ToolCallEventParams;
}

export interface WorkflowSnapshot {
    result: WorkflowResult | null;
    status: WorkflowStatus;
}
