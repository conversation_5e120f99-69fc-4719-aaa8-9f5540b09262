import * as fs from 'node:fs/promises';
import {isFileExist} from '../../../utils/fs.js';
import {assertParameterString} from '../utils/common.js';
import {WorkflowBase, WorkflowType} from './WorkflowBase.js';
import {WorkflowStatus} from '@comate/plugin-shared-internals';

const MAX_LINES = 1000;

export class ReadFileWorkflow extends WorkflowBase {
    includeInNextRequest = true;
    type = WorkflowType.ReadFile;

    get displayParams() {
        const {path} = this.parseParams();
        return {path: this.toRelativePath(path)};
    }

    get displayResult() {
        const result = this.result?.result;
        const lineCount = result && typeof result.end_line === 'number' && typeof result.start_line === 'number'
            ? result.end_line - result.start_line + 1
            : 0;

        const {path} = this.parseParams();
        const readFile = path && typeof result?.start_line === 'number' && typeof result?.end_line === 'number'
            ? {
                path: this.toAbsolutePath(path),
                startLine: result.start_line,
                endLine: result.end_line,
            }
            : undefined;
        return {
            lineCount,
            readFile,
        };
    }

    get paths() {
        const {path} = this.parseParams();
        if (path) {
            return [path];
        }
        return [];
    }

    async run() {
        const {path, start_line, end_line} = this.parseParams();
        const absolutePath = this.toAbsolutePath(assertParameterString('path', path));
        const exist = await isFileExist(absolutePath);
        if (!exist) {
            this.fail('File not exist', WorkflowStatus.SUCCESS);
            return null;
        }
        const fullText = await fs.readFile(absolutePath);
        const lines = fullText.toString().split(/\r?\n/);
        const targetLines = lines.slice(Math.max(0, start_line - 1), end_line).slice(0, MAX_LINES);
        return {
            content: targetLines.join('\n'),
            line_count: lines.length,
            start_line: start_line,
            end_line: (targetLines.length > 0 ? targetLines.length - 1 : 0) + start_line,
        };
    }

    private parseParams() {
        const {start_line, end_line} = this.params;
        const {path} = this.extractStringFromParams(['path']);

        return {
            path,
            start_line: Number(start_line) || 1,
            end_line: Number(end_line) || Number.MAX_SAFE_INTEGER,
        };
    }
}
