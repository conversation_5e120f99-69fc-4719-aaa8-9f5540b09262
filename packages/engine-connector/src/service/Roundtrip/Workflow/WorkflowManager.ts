import {AskFollowupQuestionWorkflow} from './AskFollowupQuestionWorkflow.js';
import {AttemptCompletionWorkflow} from './AttempCompletionWorkflow.js';
import {PreviewPageWorkflow} from './PreviewPageWorkflow.js';
import {KeywordSearchWorkflow} from './KeywordSearchWorkflow.js';
import {ListFilesWorkflow} from './ListFilesWorkflow.js';
import {ReadFileWorkflow} from './ReadFileWorkflow.js';
import {RunCommandWorkflow} from './RunCommandWorkflow.js';
import {SearchFilesWorkflow} from './SearchFilesWorkflow.js';
import {UnknownWorkflow} from './UnknownWorkflow.js';
import {WorkflowContext, WorkflowType} from './WorkflowBase.js';
import {ListCodeDefinitionNamesWorkflow} from './ListCodeDefinitionNamesWorkflow.js';
import {WriteFileWorkflow} from './WriteFileWorkflow.js';
import {DeleteFileWorkflow} from './DeleteFileWorkflow.js';
import {PatchFileWorkflow} from './PatchFileWorkflow.js';
import {RunDebugCommandWorkflow} from './RunDebugCommandWorkflow.js';
import {McpToolWorkflow} from './McpToolWorkflow.js';
import {ReadImageWorkflow} from './ReadImageWorkflow.js';
import {F2cToolWorkflow} from './F2cToolWorkflow.js';
import {Repo} from '../../AgentConversation/Zulu/types/knowledge.js';

const getWorkflowType = (name: string): WorkflowType => {
    switch (name) {
        case 'read_file':
            return WorkflowType.ReadFile;
        case 'read_image':
            return WorkflowType.ReadImage;
        case 'search_files':
            return WorkflowType.SearchFiles;
        case 'list_files':
            return WorkflowType.ListFiles;
        case 'keyword_search':
            return WorkflowType.KeywordSearch;
        case 'run_command':
            return WorkflowType.RunCommand;
        case 'preview_page':
            return WorkflowType.PreviewPage;
        case 'ask_followup_question':
            return WorkflowType.AskFollowupQuestion;
        case 'attempt_completion':
            return WorkflowType.AttemptCompletion;
        case 'list_code_definition_names':
            return WorkflowType.ListCodeDefinitionNames;
        case 'write_file':
            return WorkflowType.WriteFile;
        case 'delete_file':
            return WorkflowType.DeleteFile;
        case 'patch_file':
            return WorkflowType.PatchFile;
        case 'run_debug_command':
            return WorkflowType.RunDebugCommand;
        case 'use_mcp_tool':
            return WorkflowType.McpTool;
        case 'f2c_tool':
            return WorkflowType.F2cTool;
        default:
            return WorkflowType.Unknown;
    }
};

export function createWorkflow(context: WorkflowContext, name: string, extra?: Record<string, any>) {
    const type = getWorkflowType(name);
    switch (type) {
        case WorkflowType.ReadFile:
            return new ReadFileWorkflow(context, name, extra);
        case WorkflowType.ReadImage:
            return new ReadImageWorkflow(context, name, extra);
        case WorkflowType.SearchFiles:
            return new SearchFilesWorkflow(context, name, extra);
        case WorkflowType.ListFiles:
            return new ListFilesWorkflow(context, name, extra);
        case WorkflowType.KeywordSearch:
            return new KeywordSearchWorkflow(context, name, extra);
        case WorkflowType.RunCommand:
            return new RunCommandWorkflow(context, name, extra);
        case WorkflowType.PreviewPage:
            return new PreviewPageWorkflow(context, name, extra);
        case WorkflowType.AskFollowupQuestion:
            return new AskFollowupQuestionWorkflow(context, name, extra);
        case WorkflowType.AttemptCompletion:
            return new AttemptCompletionWorkflow(context, name, extra);
        case WorkflowType.ListCodeDefinitionNames:
            return new ListCodeDefinitionNamesWorkflow(context, name, extra);
        case WorkflowType.WriteFile:
            return new WriteFileWorkflow(context, name, extra);
        case WorkflowType.DeleteFile:
            return new DeleteFileWorkflow(context, name, extra);
        case WorkflowType.PatchFile:
            return new PatchFileWorkflow(context, name, extra);
        case WorkflowType.RunDebugCommand:
            return new RunDebugCommandWorkflow(context, name, extra);
        case WorkflowType.McpTool:
            return new McpToolWorkflow(context, name, extra);
        case WorkflowType.F2cTool:
            return new F2cToolWorkflow(context, name, extra);
        default:
            return new UnknownWorkflow(context, name, extra);
    }
}

export function createWorkflowFromSnapshot(repo: Repo, snapshot: any) {
    return null;
}
