import {WorkflowBase, WorkflowType} from './WorkflowBase.js';

export class AskFollowupQuestionWorkflow extends WorkflowBase {
    includeInNextRequest = false;
    type = WorkflowType.AskFollowupQuestion;

    get displayParams() {
        const {question} = this.parseParams();
        return {
            followupQuestion: question,
        };
    }

    get displayResult() {
        return {};
    }

    get paths() {
        return [];
    }

    async run() {
        const {question} = this.parseParams();
        return {
            followupQuestion: question,
        };
    }

    private parseParams() {
        return this.extractStringFromParams(['question']);
    }
}
