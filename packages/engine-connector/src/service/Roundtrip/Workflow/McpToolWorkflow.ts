import {MCPTextContent} from '@comate/plugin-shared-internals';
import {WorkflowBase, WorkflowType} from './WorkflowBase.js';

export class McpToolWorkflow extends WorkflowBase {
    type = WorkflowType.McpTool;
    includeInNextRequest = true;
    autoRun: boolean = false;

    /* 现在没有实际的意义，只是因为 abstract */
    get paths() {
        return [];
    }

    get displayParams() {
        const params = this.extractStringFromParams(['server_name', 'tool_name', 'arguments']);
        return {
            serverName: params.server_name,
            toolName: params.tool_name,
            args: params.arguments,
        };
    }

    get displayResult() {
        return {
            mcpResult: {
                content: this.result?.result,
                errorMsg: this.result?.success ? '' : this.result?.message,
            },
        };
    }

    private parseParams() {
        const params = this.extractStringFromParams(['server_name', 'tool_name', 'arguments']);
        if (!params.server_name) {
            throw new Error('MCP Server name 为空');
        }
        if (!params.tool_name) {
            throw new Error('MCP Server tool name 为空');
        }
        if (!params.arguments) {
            return {
                serverName: params.server_name,
                toolName: params.tool_name,
            };
        }
        try {
            const args = JSON.parse(params.arguments);
            return {
                serverName: params.server_name,
                toolName: params.tool_name,
                args: args,
            };
        }
        catch (error) {
            throw new Error('MCP Server tool 调用参数为非法 JSON 格式');
        }
    }

    async run(approve?: boolean) {
        if (!approve) {
            this.reject('User declined to execute the command.');
            return null;
        }
        const {serverName, toolName, args} = this.parseParams();
        const res = await this.context.mcpManager?.callTool(serverName, toolName, args);
        if (!res) {
            throw new Error('MCP Server tool 调用失败');
        }
        if (res.isError) {
            const errorMessage = res
                .content
                .filter(item => item.type === 'text')
                .map(item => (item as MCPTextContent).text)
                .join('\n');
            throw new Error(errorMessage);
        }
        return res.content;
    }
}
