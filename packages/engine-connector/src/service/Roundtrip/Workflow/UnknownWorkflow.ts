import {WorkflowBase, WorkflowType} from './WorkflowBase.js';

export class UnknownWorkflow extends WorkflowBase {
    includeInNextRequest = true;
    type = WorkflowType.Unknown;

    get displayParams() {
        return {};
    }

    get displayResult() {
        return {};
    }
    get paths() {
        return [];
    }

    async run(): Promise<null> {
        throw new Error('Unknown tool');
    }
}
