import {WorkflowStatus} from '@comate/plugin-shared-internals';
import {WorkflowBase, WorkflowType} from './WorkflowBase.js';

export class AttemptCompletionWorkflow extends WorkflowBase {
    includeInNextRequest = false;
    type = WorkflowType.AttemptCompletion;
    private commandResult: {status: WorkflowStatus} = {status: WorkflowStatus.UNREADY};

    get displayParams() {
        const {path, command, result} = this.parseParams();
        return {
            path: this.toDisplayPath(path),
            command,
            summary: result,
        };
    }

    get paths() {
        return [];
    }

    get displayResult() {
        return {
            commandResult: this.commandResult,
        };
    }

    async run() {
        this.commandResult = {status: WorkflowStatus.READY};
        return null;
    }

    protected async onAccept() {
        const {command, path} = this.parseParams();
        if (!command) {
            return null;
        }
        this.commandResult = {status: WorkflowStatus.RUNNING};
        const absolutePath = this.toAbsolutePath(path ?? '');
        const result = await Promise.race([
            this.virtualEditor.executeTerminalShell({
                cmd: command,
                cwd: absolutePath,
                duration: 3 * 60 * 1000,
                run: true,
            }),
            // 多加 5 秒，防止通信延迟导致超时
            new Promise<null>(resolve => setTimeout(() => resolve(null), 3 * 60 * 1000 + 5 * 1000)),
        ]);
        this.commandResult = {
            status: result ? WorkflowStatus.SUCCESS : WorkflowStatus.FAILED,
        };
        this.context.updateWebviewMessages();
    }

    protected onReject(): void {
        this.commandResult = {
            status: WorkflowStatus.CANCELLED,
        };
    }

    protected customData() {
        return {commandResult: this.commandResult};
    }

    protected rebuildCustomData(customCacheData?: Record<string, any>) {
        this.commandResult = customCacheData?.commandResult ?? {status: WorkflowStatus.READY};
    }

    private parseParams() {
        return this.extractStringFromParams(['command', 'path', 'result']);
    }
}
