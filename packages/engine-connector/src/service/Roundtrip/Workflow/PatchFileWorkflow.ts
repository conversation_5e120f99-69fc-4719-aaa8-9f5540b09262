import {SearchReplacePatchError} from '@comate/plugin-shared-internals';
import {isFileExist} from '../../../utils/fs.js';
import {SearchReplaceComposerTask} from '../../Composer/tasks/SearchReplaceComposerTask.js';
import {WorkflowStatus} from '../../Composer/types.js';
import {sleep} from '../../Composer/utils/index.js';
import {extractStringValues} from '../utils/common.js';
import {DiagnosticTelemetry} from '../utils/DiagnosticTelemetry.js';
import {WorkflowBase, WorkflowContext, WorkflowType} from './WorkflowBase.js';

export class PatchFileWorkflow extends WorkflowBase {
    includeInNextRequest = true;
    type = WorkflowType.PatchFile;

    composerTask: SearchReplaceComposerTask;

    fileNotExists = false;
    diagnosticTelemetry: DiagnosticTelemetry;

    constructor(context: WorkflowContext, name: string, extra?: Record<string, any>) {
        super(context, name, extra);
        this.composerTask = this.context.composer.createTask(
            this.context.virtualEditor,
            this.context.repo,
            'searchReplace',
            ''
        );
        /** 收集修改前后的 diagnostics 后面不要了去掉 */
        this.diagnosticTelemetry = new DiagnosticTelemetry(
            this.composerTask,
            this.virtualEditor,
            WorkflowType.PatchFile,
            this.context
        );
    }

    get displayParams() {
        const {path} = this.parseParams();
        return {path: this.toDisplayPath(path)};
    }

    get paths() {
        const {path} = this.parseParams();
        if (path) {
            return [path];
        }
        return [];
    }

    get displayResult() {
        return {};
    }

    async run(): Promise<any> {
        const {path, content} = this.parseParams();
        if (this.composerTask.status === WorkflowStatus.UNREADY) {
            if (!path) {
                throw new Error('parameter \'path\' is required and must be a string');
            }
            // 都开始启动 workflow 了 content 还是 undefined，说明参数有问题，直接报错让上层处理
            if (content === undefined) {
                throw new Error('parameter \'content\' is required and must be a string');
            }
            await this.startComposerTask(path, content);
        }

        if (this.fileNotExists) {
            throw new Error('file not exists');
        }

        try {
            // 因为update自身带了100ms的debounce，加这个保证上一次一定执行完了
            await sleep(200);
            await this.composerTask.immediateUpdate(content ?? '', true);
            this.diagnosticTelemetry.afterEdit(content ?? '');
            return this.getCallbackResult(path as string);
        }
        catch (error) {
            const result = this.getCallbackResult(path as string);
            if (error instanceof SearchReplacePatchError) {
                return {error, ...result, ...error.result};
            }
            return {error, ...result};
        }
    }

    private getCallbackResult(path: string) {
        return {
            failedPatchIndex: -1,
            fileName: path,
            beforeChangeFileContent: this.composerTask.originalContent,
            afterChangeFileContent: this.composerTask.content,
        };
    }

    protected onCancel(): void {
        if (this.composerTask.finish !== true) {
            this.composerTask.cancel();
        }
    }

    protected async onUpdateParams(newParams: Record<string, any>) {
        if (this.fileNotExists) {
            return;
        }

        const {path, content} = extractStringValues(newParams, ['path', 'content']);
        // 提前更新路径，虽然任务没开始，但界面上能看到
        if (this.composerTask.status === WorkflowStatus.UNREADY) {
            this.composerTask.setPath(path ?? '');
        }

        if (content && this.composerTask.status === WorkflowStatus.RUNNING) {
            await this.composerTask.update(content, false);
            return;
        }

        // 正常情况下应该是路径在内容出现前就给了，这时候直接开始任务，不然就等消息结束后再开始
        const hasPath = path && path === this.params.path;
        if (hasPath && content && this.composerTask.status === WorkflowStatus.UNREADY) {
            await this.startComposerTask(path, content);
        }
    }

    private async startComposerTask(path: string, content: string) {
        this.composerTask.setPath(path ?? '');
        this.composerTask.setForeground(this.foreground);
        // 判断文档是否存在是不是应该由composer来判断读取是否成功
        const fileExist = await isFileExist(this.composerTask.absolutePath);
        if (!fileExist) {
            this.fileNotExists = true;
            return this.composerTask;
        }
        this.diagnosticTelemetry.beforeEdit();
        await this.composerTask.start(content, true);
        return this.composerTask;
    }

    private parseParams() {
        const {path, content} = this.extractStringFromParams(['path', 'content']);
        return {
            path,
            content,
        };
    }
}
