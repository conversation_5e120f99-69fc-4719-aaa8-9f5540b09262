import {relative} from 'path';
import {isFileExist} from '../../../utils/fs.js';
import {execRipgrep, getRipgrepPath, SearchResult} from '../utils/ripgrep.js';
import {WorkflowBase, WorkflowType} from './WorkflowBase.js';
import {WorkflowStatus} from '@comate/plugin-shared-internals';

const DEFAULT_MAX_RESULTS = 300;

export async function regexSearchFiles(
    cwd: string,
    directoryPath: string,
    isRegExp: boolean,
    regexOrKeyword: string,
    filePattern?: string
) {
    const rgPath = getRipgrepPath();

    if (!rgPath) {
        throw new Error('Could not find ripgrep path');
    }

    const args = [
        '--json',
        isRegExp ? '-e' : '-P',
        regexOrKeyword,
        ...(filePattern ? ['--glob', filePattern] : []),
        '--context',
        '1',
        directoryPath,
    ];

    const results = await execRipgrep(rgPath, args, DEFAULT_MAX_RESULTS);

    return results.map(result => ({
        ...result,
        file: relative(cwd, result.file),
    }));
}

export class SearchFilesWorkflow extends WorkflowBase {
    includeInNextRequest = true;
    type = WorkflowType.SearchFiles;
    // 把搜到的原始信息存一下
    searchFiles: SearchResult[] = [];

    get displayParams() {
        const {path, regex, file_pattern} = this.parseParams();
        return {path: this.toDisplayPath(path), regex: regex ?? file_pattern};
    }

    get displayResult() {
        const searchFiles = this.searchFiles.map(fileInfo => ({
            path: fileInfo.file,
            startLine: fileInfo.startLine,
            endLine: fileInfo.endLine,
        }));
        return {searchFiles};
    }

    get paths() {
        return this.searchFiles.map(fileInfo => fileInfo.file);
    }

    async run() {
        const {path, regex, file_pattern} = this.parseParams();
        const absolutePath = this.toAbsolutePath(path ?? '');
        const exist = await isFileExist(absolutePath);
        if (!exist) {
            // 给模型的结果是失败，界面展示的是成功
            this.fail('File or directory not exist', WorkflowStatus.SUCCESS);
            return null;
        }

        if (!regex && !file_pattern) {
            throw new Error(`parameter 'regex' or 'file_pattern' is required`);
        }

        const files = await regexSearchFiles(
            this.repo.rootPath,
            absolutePath,
            true,
            regex || '^.*$',
            file_pattern
        );
        this.searchFiles = files;
        return files.map(fileInfo => ({path: fileInfo.file, match: fileInfo.match}));
    }

    protected customData() {
        return {searchFiles: this.searchFiles};
    }

    protected rebuildCustomData(customCacheData?: Record<string, any>) {
        this.searchFiles = customCacheData?.searchFiles ?? [];
    }

    private parseParams() {
        return this.extractStringFromParams(['path', 'regex', 'file_pattern']);
    }
}
