import {isAbsolute, join, relative} from 'node:path';
import _ from 'lodash';
import {
    SearchReplacePatchError,
    ToolCallMessageDisplayParams,
    ToolCallMessageDisplayResult,
    VirtualEditor,
    WorkflowStatus,
} from '@comate/plugin-shared-internals';
import tildify from 'tildify';
import {Composer} from '../../Composer/Composer.js';
import {WorkflowSnapshot} from '../types.js';
import {extractStringValues} from '../utils/common.js';
import {formatWin32PathSep} from '../../Composer/utils/index.js';
import {McpManager} from '../../McpManager/index.js';
import {Repo} from '../../AgentConversation/Zulu/types/knowledge.js';

export enum WorkflowType {
    SearchFiles = 'search_files',
    ReadImage = 'read_image',
    ListFiles = 'list_files',
    KeywordSearch = 'keyword_search',
    PreviewPage = 'preview_page',
    AskFollowupQuestion = 'ask_followup_question',
    AttemptCompletion = 'attempt_completion',
    RunCommand = 'run_command',
    ReadFile = 'read_file',
    ListCodeDefinitionNames = 'list_code_definition_names',
    WriteFile = 'write_file',
    DeleteFile = 'delete_file',
    PatchFile = 'patch_file',
    RunDebugCommand = 'run_debug_command',
    McpTool = 'mcp_tool',
    F2cTool= 'f2c_tool',
    Unknown = 'unknown',
}

export type ToolCallResult = any;

export interface WorkflowResult {
    // 工具名称
    name: string;
    // 状态码，标识特殊状态
    code: number;
    // 是否成功
    success: boolean;
    // 提示信息，失败时的报错原因，成功时为空字符串
    message: string;
    // 请求时给的参数会原封不动返回
    params: Record<string, any>;
    // 工具执行结果
    result: ToolCallResult;
}

export interface WorkflowContext {
    conversationId: string;
    elementId: string;
    repo: Repo;
    composer: Composer;
    virtualEditor: VirtualEditor;
    updateWebviewMessages: () => void;
    mcpManager?: McpManager;
    requestInfo?: Record<string, string | number>;
}

export abstract class WorkflowBase {
    status: WorkflowStatus = WorkflowStatus.UNREADY;
    readonly autoRun: boolean = true;
    abstract type: WorkflowType;
    abstract includeInNextRequest: boolean;
    abstract paths: string[];
    private statusCode: number = -1;
    private promiseResolve: (accept: boolean) => void = () => {};
    protected foreground: boolean = false;

    // 展示给用户的参数，传给 webview 用的
    abstract displayParams: ToolCallMessageDisplayParams;
    // 展示给用户的结果，传给 webview 用的
    abstract displayResult: ToolCallMessageDisplayResult;

    result: WorkflowResult | null = null;
    // 这里的 params 和 ToolCallMessage 的 params 是同一个东西，能弄成一个吗
    protected params: Record<string, any> = {};

    workflowPromise: Promise<WorkflowResult | null> = Promise.resolve(null);

    constructor(
        protected readonly context: WorkflowContext,
        private readonly name: string,
        readonly extra?: Record<string, any>
    ) {
    }

    get baseResult() {
        return {name: this.name, ...this.extra, params: this.params};
    }

    get repo() {
        return this.context.repo;
    }

    get virtualEditor() {
        return this.context.virtualEditor;
    }

    get finish() {
        return this.status === WorkflowStatus.SUCCESS
            || this.status === WorkflowStatus.FAILED
            || this.status === WorkflowStatus.CANCELLED;
    }

    get isInitializing() {
        return this.status === WorkflowStatus.UNREADY;
    }

    async start() {
        this.status = WorkflowStatus.READY;
        this.beforeStart();
        this.workflowPromise = this.autoRun
            ? this.doRunWorkflow(true)
            : this.runWorkflowAfterApprove();
        return this.workflowPromise;
    }

    async setParams(params: Record<string, any>) {
        try {
            await this.onUpdateParams(params);
        }
        catch {
            // 这里的异常不用管，理论上 workflow 还没起来，因为参数不对导致的异常应该放到 runWorkflow 里边去报错
            // 这样能把结果扔给模型
        }
        this.params = params;
    }

    acceptToRunWorkflow() {
        this.promiseResolve(true);
        this.onAccept();
    }

    rejectToRunWorkflow() {
        this.promiseResolve(false);
        this.onReject();
    }

    setForeground(value: boolean) {
        this.foreground = value;
    }

    cancel() {
        this.onCancel();
        if (!this.finish) {
            this.status = WorkflowStatus.CANCELLED;
        }
    }

    rebuild(params: Record<string, any> = {}, snapshot: WorkflowSnapshot) {
        const {status, result, ...customCacheData} = snapshot ?? {};
        this.params = params;
        this.result = result;
        const isSuccessOrFailed = status && (status === WorkflowStatus.SUCCESS || status === WorkflowStatus.FAILED);
        this.status = isSuccessOrFailed ? status : WorkflowStatus.CANCELLED;
        this.rebuildCustomData(customCacheData);
    }

    toJSON(): WorkflowSnapshot {
        return {
            ...this.customData(),
            status: this.status,
            result: this.result,
        };
    }

    protected onAccept() {
        //
    }

    protected onReject() {
        //
    }

    protected onCancel() {
        //
    }

    protected async onUpdateParams(newParams: Record<string, any>) {
        //
    }

    protected customData() {
        return {};
    }

    protected rebuildCustomData(_customCacheData?: Record<string, any>) {
        //
    }

    protected beforeStart() {
        //
    }

    private async runWorkflowAfterApprove() {
        const approve = await new Promise<boolean>(resolve => {
            this.promiseResolve = resolve;
        });
        return this.doRunWorkflow(approve);
    }

    private async doRunWorkflow(approve: boolean = true) {
        try {
            this.status = WorkflowStatus.RUNNING;
            const result = await this.run(approve);
            if (result?.error instanceof SearchReplacePatchError) {
                this.fail(_.omit(result, 'error'));
            }
            else {
                this.success(result ?? null);
            }
        }
        catch (ex) {
            this.fail(ex instanceof Error ? ex.message : `${ex}`);
            this.onCancel();
        }
        // 检查下状态，到这了说明 workflow 已经结束了，还在运行中的任务都是异常而且没被取消的
        this.context.composer.tasks.forEach(task => task.finish !== true && task.cancel());
        return this.result;
    }

    protected success(result: ToolCallResult) {
        if (this.finish) {
            return;
        }
        this.result = {
            ...this.baseResult,
            success: true,
            message: '',
            // 没特殊情况状态成功的状态码默认为 0
            code: this.statusCode === -1 ? 0 : this.statusCode,
            result: result,
        };
        this.status = WorkflowStatus.SUCCESS;
    }

    protected fail(reason: string | object, status?: WorkflowStatus) {
        if (this.finish) {
            return;
        }

        this.result = {
            ...this.baseResult,
            success: false,
            message: typeof reason === 'string' ? reason : '',
            // 没特殊情况状态失败的状态码默认为 1
            code: this.statusCode === -1 ? 1 : this.statusCode,
            result: typeof reason === 'object' ? reason : null,
        };
        this.status = status ?? WorkflowStatus.FAILED;
    }

    protected reject(reason: string) {
        this.result = {
            ...this.baseResult,
            success: false,
            message: reason,
            // 没特殊情况拒绝执行工具的状态码默认为 3
            code: this.statusCode === -1 ? 3 : this.statusCode,
            result: null,
        };
        this.status = WorkflowStatus.CANCELLED;
    }

    protected toAbsolutePath(path: string) {
        const {repo} = this.context;
        return isAbsolute(path) ? path : join(repo.rootPath, path);
    }

    protected toDisplayPath(path?: string) {
        if (
            this.status === WorkflowStatus.UNREADY
            && (!path || typeof path !== 'string')
        ) {
            return '';
        }
        return formatWin32PathSep(tildify(this.toAbsolutePath(path ?? '')));
    }

    protected toRelativePath(path?: string) {
        if (!path) {
            return '';
        }
        return isAbsolute(path) ? relative(this.context.repo.rootPath, path) : path;
    }

    protected extractStringFromParams(keys: string[]) {
        return extractStringValues(this.params, keys);
    }

    protected setStatusCode(code: number) {
        this.statusCode = code;
    }

    abstract run(approve?: boolean): Promise<ToolCallResult>;
}
