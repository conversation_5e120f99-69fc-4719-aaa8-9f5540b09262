import {DeleteComposerTask} from '../../Composer/tasks/DeleteComposerTask.js';
import {assertParameterString} from '../utils/common.js';
import {WorkflowBase, WorkflowContext, WorkflowType} from './WorkflowBase.js';

export class DeleteFileWorkflow extends WorkflowBase {
    includeInNextRequest = true;
    type = WorkflowType.DeleteFile;
    composerTask: DeleteComposerTask;

    constructor(context: WorkflowContext, name: string, extra?: Record<string, any>) {
        super(context, name, extra);
        this.composerTask = this.context.composer.createTask(
            this.context.virtualEditor,
            this.context.repo,
            'delete',
            ''
        );
    }

    get displayParams() {
        const {path} = this.parseParams();
        return {path: this.toDisplayPath(path)};
    }

    get displayResult() {
        return {};
    }

    get paths() {
        return [];
    }

    async run() {
        const {path} = this.parseParams();
        const absolutePath = this.toAbsolutePath(assertParameterString('path', path));
        await this.startComposerTask(absolutePath).start();
        return null;
    }

    protected onCancel(): void {
        if (this.composerTask.finish !== true) {
            this.composerTask.cancel();
        }
    }

    private startComposerTask(path: string) {
        this.composerTask.setPath(path);
        this.composerTask.setForeground(this.foreground);
        return this.composerTask;
    }

    private parseParams() {
        const {path} = this.extractStringFromParams(['path']);
        return {
            path,
        };
    }
}
