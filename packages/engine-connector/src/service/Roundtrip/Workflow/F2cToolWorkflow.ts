import {axiosInstance, SSEProcessor} from '@comate/plugin-shared-internals';
import {WorkflowBase, WorkflowType} from './WorkflowBase.js';
import {formatWin32PathSep} from '../../Composer/utils/index.js';
import {assertParameterString} from '../utils/common.js';

export class F2cToolWorkflow extends WorkflowBase {
    includeInNextRequest = true;
    type = WorkflowType.F2cTool;
    data = '';

    get displayParams() {
        return {
            content: this.parseParams()['content'],
            url: this.parseParams()['url'],
            toolName: this.parseParams()['toolName'],
        };
    }

    get displayResult() {
        return {};
    }

    get paths() {
        return [];
    }

    async run() {
        switch (this.parseParams()['toolName']) {
            case '元素分析':
                return await this.resolveFigma();
            default:
                return null;
        }
    }

    async resolveFigma() {
        const figmaUrl = this.parseParams()['url'] as string;
        const figmaToken = this.parseParams()['figmaToken'] as string;
        const strategy = this.parseParams()['strategy'] as string;

        try {
            const res = await axiosInstance.post('/api/figma/processFigmaForComate', {
                figmaToken,
                figmaUrl,
                strategy,
            });
            return res.data.html;
        }
        catch (error) {
            this.fail('Failed to resolve figma');
            throw new Error('Failed to resolve figma');
        }
    }

    private parseParams() {
        return this.extractStringFromParams(['content', 'url', 'strategy', 'figmaToken', 'toolName', 'nodeId']);
    }

    onAccept() {
        const {url} = this.parseParams();
        const formatUrl = formatWin32PathSep(assertParameterString('url', url));
        this.virtualEditor.openUrlInEditorWebview(
            {url: formatUrl, title: '预览'}
        );
    }
}
