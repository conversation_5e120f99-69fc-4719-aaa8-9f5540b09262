import {WorkflowBase, WorkflowType} from './WorkflowBase.js';

const MAX_LINES = 2000;

export class ListCodeDefinitionNamesWorkflow extends WorkflowBase {
    includeInNextRequest = true;
    type = WorkflowType.ListCodeDefinitionNames;

    get displayParams() {
        return this.parseParams();
    }

    get displayResult() {
        return {};
    }

    get paths() {
        return [];
    }

    async run() {
        const {path} = this.parseParams();
        const absolutePath = this.toAbsolutePath(path ?? '');
        return null;
    }

    private parseParams() {
        return this.extractStringFromParams(['path']);
    }
}
