import {resolve, parse, relative, sep} from 'path';
import * as os from 'os';
import {globby, Options} from 'globby';
import {isFilePathEqual} from '../../Composer/utils/index.js';
import {isFileExist} from '../../../utils/fs.js';
import {assertParameterString, toBoolean} from '../utils/common.js';
import {WorkflowBase, WorkflowType} from './WorkflowBase.js';
import {WorkflowStatus} from '@comate/plugin-shared-internals';

type FileType = 'file' | 'folder';

interface FileInfo {
    path: string;
    type: FileType;
}

function formatFilesList(rootPath: string, files: string[]) {
    return files
        .map(file => ({
            path: relative(rootPath, file),
            type: file.endsWith('/') ? 'folder' : 'file' as FileType,
        }))
        .sort((a, b) => {
            const {type: aType, path: aPath} = a;
            const {type: bType, path: bPath} = b;
            const aParts = aPath.split(sep);
            const bParts = bPath.split(sep);
            if (aParts.length === bParts.length) {
                if (aType === 'folder' && bType === 'file') {
                    return -1;
                }
                else if (aType === 'file' && bType === 'folder') {
                    return 1;
                }
                else {
                    return aPath.localeCompare(bPath, undefined, {numeric: true, sensitivity: 'base'});
                }
            }
            return aParts.length - bParts.length;
        });
}

async function globbyLevelByLevel(limit: number, options?: Options) {
    let results: Set<string> = new Set();
    let queue: string[] = ['*'];

    const globbingProcess = async () => {
        while (queue.length > 0 && results.size < limit) {
            const pattern = queue.shift()!;
            const filesAtLevel = await globby(pattern, options);

            for (const file of filesAtLevel) {
                if (results.size >= limit) {
                    break;
                }
                results.add(file);
                if (file.endsWith('/')) {
                    queue.push(`${file}*`);
                }
            }
        }
        return Array.from(results).slice(0, limit);
    };

    // Timeout after 10 seconds and return partial results
    const timeoutPromise = new Promise<string[]>((_, reject) => {
        setTimeout(() => reject(new Error('Globbing timeout')), 10_000);
    });
    try {
        return await Promise.race([globbingProcess(), timeoutPromise]);
    }
    catch (error) {
        // console.warn("Globbing timed out, returning partial results");
        return Array.from(results);
    }
}

export async function listFiles(
    cwd: string,
    dirPath: string,
    recursive: boolean,
    limit: number
): Promise<[FileInfo[], boolean]> {
    const absolutePath = resolve(dirPath);
    // Do not allow listing files in root or home directory, which cline tends to want to do when the user's prompt is vague.
    const root = process.platform === 'win32' ? parse(absolutePath).root : '/';
    const isRoot = isFilePathEqual(absolutePath, root);
    if (isRoot) {
        return [[{path: root, type: 'folder'}], false];
    }
    const homeDir = os.homedir();
    const isHomeDir = isFilePathEqual(absolutePath, homeDir);
    if (isHomeDir) {
        return [[{path: homeDir, type: 'folder'}], false];
    }

    const dirsToIgnore = [
        'node_modules',
        '__pycache__',
        'env',
        'venv',
        'target/dependency',
        'build/dependencies',
        'dist',
        'out',
        'bundle',
        'vendor',
        'tmp',
        'temp',
        'deps',
        'pkg',
        'Pods',
        '.*', // '!**/.*' excludes hidden directories, while '!**/.*/**' excludes only their contents. This way we are at least aware of the existence of hidden directories.
    ]
        .map(dir => `**/${dir}/**`);

    const options = {
        cwd: dirPath,
        dot: true, // do not ignore hidden files/directories
        absolute: true,
        markDirectories: true, // Append a / on any directories matched (/ is used on windows as well, so dont use path.sep)
        gitignore: recursive, // globby ignores any files that are gitignored
        ignore: recursive ? dirsToIgnore : undefined, // just in case there is no gitignore, we ignore sensible defaults
        onlyFiles: false, // true by default, false means it will list directories on their own too
    };
    // * globs all files in one dir, ** globs files in nested directories
    const files = recursive
        ? await globbyLevelByLevel(limit, options)
        : (await globby('*', options)).slice(0, limit);

    return [
        formatFilesList(cwd, files),
        files.length >= limit,
    ];
}

export class ListFilesWorkflow extends WorkflowBase {
    includeInNextRequest = true;
    type = WorkflowType.ListFiles;

    get displayParams() {
        const {path} = this.parseParams();
        // 流式过程是 undefined 时，不显示路径
        return {path: this.toDisplayPath(path)};
    }

    get displayResult() {
        return {
            fileCount: this.result?.result?.length ?? 0,
        };
    }

    get paths() {
        return [];
    }

    async run() {
        const {path, recursive} = this.parseParams();
        const absolutePath = this.toAbsolutePath(assertParameterString('path', path));
        const exist = await isFileExist(absolutePath);
        if (!exist) {
            // 给模型的结果是失败，界面展示的是成功
            this.fail('File or directory not exist', WorkflowStatus.SUCCESS);
            return null;
        }
        const [files] = await listFiles(
            this.repo.rootPath,
            absolutePath,
            recursive,
            Number.MAX_SAFE_INTEGER
        );
        return files;
    }

    private parseParams() {
        const {recursive} = this.params;
        const {path} = this.extractStringFromParams(['path']);
        return {
            recursive: toBoolean(recursive),
            path,
        };
    }
}
