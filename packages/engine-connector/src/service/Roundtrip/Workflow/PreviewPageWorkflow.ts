import {formatWin32PathSep} from '../../Composer/utils/index.js';
import {assertParameterString} from '../utils/common.js';
import {WorkflowBase, WorkflowType} from './WorkflowBase.js';

export class PreviewPageWorkflow extends WorkflowBase {
    includeInNextRequest = true;
    type = WorkflowType.PreviewPage;

    get displayParams() {
        const {url} = this.parseParams();
        return {
            url: url ? formatWin32PathSep(url) : url,
        };
    }

    get displayResult() {
        return {};
    }

    get paths() {
        return [];
    }

    async run() {
        return null;
    }

    onAccept() {
        const {url} = this.parseParams();
        const formatUrl = formatWin32PathSep(assertParameterString('url', url));
        this.virtualEditor.openUrlInEditorWebview(
            {url: formatUrl, title: '预览'}
        );
    }

    private parseParams() {
        return this.extractStringFromParams(['url']);
    }
}
