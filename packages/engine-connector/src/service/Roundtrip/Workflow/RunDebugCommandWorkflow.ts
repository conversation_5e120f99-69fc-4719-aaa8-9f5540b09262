import {assertParameterString} from '../utils/common.js';
import {ToolCallResult, WorkflowBase, WorkflowType} from './WorkflowBase.js';

const DEFAULT_TIMEOUT = 3 * 60 * 1000;

export class RunDebugCommandWorkflow extends WorkflowBase {
    includeInNextRequest = true;
    type = WorkflowType.RunDebugCommand;

    displaySkipButton: boolean = false;

    userActionResolve: ((value: {status: 'aborted'}) => void) | null = null;

    get displayParams() {
        const {path, command} = this.parseParams();
        return {
            path: this.toDisplayPath(path),
            command,
            displaySkipButton: this.displaySkipButton,
        };
    }

    get displayResult() {
        return {};
    }

    get paths() {
        return [];
    }

    async run(): Promise<ToolCallResult> {
        const {command, path} = this.parseParams();
        const absolutePath = this.toAbsolutePath(path ?? '');

        const timer = setTimeout(
            () => {
                this.displaySkipButton = true;
                this.context.updateWebviewMessages();
            },
            5 * 1000
        );

        const result = await Promise.race([
            this.virtualEditor.executeDebugCommand(
                {
                    cmd: assertParameterString('command', command),
                    cwd: absolutePath,
                    duration: DEFAULT_TIMEOUT,
                }
            ),
            this.waitForUserAction(),
        ]);

        clearTimeout(timer);

        return result;
    }

    private waitForUserAction() {
        return new Promise<{status: 'aborted'}>(resolve => {
            this.userActionResolve = resolve;
        });
    }

    private parseParams() {
        const {command, path} = this.extractStringFromParams(['command', 'path']);
        return {command, path};
    }

    protected onAccept(): void {
        this.userActionResolve?.({status: 'aborted'});
    }
}