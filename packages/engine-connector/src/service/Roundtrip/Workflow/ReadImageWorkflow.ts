import {WorkflowBase, WorkflowType} from './WorkflowBase.js';

export class ReadImageWorkflow extends WorkflowBase {
    includeInNextRequest = true;
    type = WorkflowType.ReadFile;

    get displayParams() {
        const {file_name, image_id: id} = this.parseParams();
        return {path: file_name, id};
    }

    get displayResult() {
        return {};
    }

    get paths() {
        return [];
    }

    async run() {
        const {query, image_id, file_name} = this.parseParams();
        return {
            query,
            image_id,
            file_name,
        };
    }

    private parseParams() {
        const {image_id, file_name, query} = this.params;

        return {
            query,
            file_name,
            image_id: image_id,
        };
    }
}
