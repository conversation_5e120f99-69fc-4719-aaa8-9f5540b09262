import {assertParameterString} from '../utils/common.js';
import {SearchResult} from '../utils/ripgrep.js';
import {WorkflowBase, WorkflowType} from './WorkflowBase.js';
import {regexSearchFiles} from './SearchFilesWorkflow.js';

export class KeywordSearchWorkflow extends WorkflowBase {
    includeInNextRequest = true;
    type = WorkflowType.SearchFiles;
    // 把搜到的原始信息存一下
    searchFiles: SearchResult[] = [];

    get displayParams() {
        const {keyword, path} = this.parseParams();
        return {regex: keyword, path: this.toDisplayPath(path)};
    }

    get displayResult() {
        const searchFiles = this.searchFiles.map(fileInfo => ({
            path: fileInfo.file,
            startLine: fileInfo.startLine,
            endLine: fileInfo.endLine,
        }));
        return {searchFiles};
    }

    get paths() {
        return this.searchFiles.map(fileInfo => fileInfo.file);
    }

    async run() {
        const params = this.parseParams();
        const keyword = assertParameterString('keyword', params.keyword);
        const absolutePath = this.toAbsolutePath(params.path ?? '');
        const files = await regexSearchFiles(this.repo.rootPath, absolutePath, false, keyword, params.file_pattern);
        this.searchFiles = files;
        return files.map(fileInfo => ({path: fileInfo.file, match: fileInfo.match}));
    }

    protected onCancel(): void {
        // TODO 把search的promise给取消
    }

    protected customData() {
        return {searchFiles: this.searchFiles};
    }

    protected rebuildCustomData(customCacheData?: Record<string, any>) {
        this.searchFiles = customCacheData?.searchFiles ?? [];
    }

    private parseParams() {
        return this.extractStringFromParams(['keyword', 'path', 'file_pattern']);
    }
}
