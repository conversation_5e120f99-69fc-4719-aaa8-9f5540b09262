import {kernel} from '@comate/kernel-shared';
import {WorkflowStatus, parseShellCommandName} from '@comate/plugin-shared-internals';
import {assertParameterString, toBoolean} from '../utils/common.js';
import {containsServerReadySignal, SPECIAL_MATCH_COMMANDS} from '../utils/detector.js';
import {WorkflowBase, WorkflowType} from './WorkflowBase.js';

const DEFAULT_TIMEOUT = 3 * 60 * 1000;

interface UserActionResult {
    type: 'userAction';
    data: boolean;
}

interface TimeoutResult {
    type: 'timeout';
    data: null;
}

interface CommandResult {
    type: 'executeCommand';
    data: {output: string, failed?: boolean};
}

interface EarlyReturnResult {
    type: 'earlyReturn';
    data: {output: string, failed?: boolean};
}

interface CancelResult {
    type: 'cancel';
    data: null;
}

type Result = UserActionResult | TimeoutResult | CommandResult | EarlyReturnResult | CancelResult;

export class RunCommandWorkflow extends WorkflowBase {
    includeInNextRequest = true;
    type = WorkflowType.RunCommand;
    autoRun: boolean = false;

    displaySkipButton: boolean = false;

    cachedOutput: string = '';

    userActionResolve: ((value: UserActionResult) => void) | null = null;
    earlyReturnResolve: ((value: EarlyReturnResult) => void) | null = null;
    cancelResolve: ((value: CancelResult) => void) | null = null;

    get displayParams() {
        const {path, command} = this.parseParams();
        return {
            path: this.toDisplayPath(path),
            command,
            displaySkipButton: this.displaySkipButton,
        };
    }

    get displayResult() {
        return {};
    }

    get paths() {
        return [];
    }

    async run(approve?: boolean) {
        if (!approve) {
            this.reject('User declined to execute the command.');
            return null;
        }
        const {command, path} = this.parseParams();
        const absolutePath = this.toAbsolutePath(path ?? '');

        const {type, data} = await Promise.race<Result>([
            this.doExecuteCommand(command ?? '', absolutePath),
            this.waitForUserAction(),
            this.waitForTimeout(),
            this.waitForEarlyReturnSignal(command ?? ''),
            this.waitForCancel(),
        ]);
        this.clearPromise();

        if (type === 'cancel') {
            this.fail('User cancelled the execution.', WorkflowStatus.CANCELLED);
            return null;
        }
        else if (type === 'timeout') {
            this.fail('Command execution timed out after 3 minutes.', WorkflowStatus.SUCCESS);
            return null;
        }
        else if (type === 'userAction') {
            // this.fail('User chose to continue without waiting for terminal output.', WorkflowStatus.Success);
            return {output: this.cachedOutput};
        }

        if (data && data.failed === true) {
            this.fail(data.output || 'Failed to execute the command.');
            return null;
        }

        return data ? {output: data.output} : null;
    }

    private parseParams() {
        const {auto_approve = ''} = this.params;
        const {command, path} = this.extractStringFromParams(['command', 'path']);
        return {
            command,
            path,
            auto_approve: toBoolean(auto_approve),
        };
    }

    private async doExecuteCommand(command: string, absolutePath: string) {
        const result = await this.virtualEditor.executeTerminalShell(
            {
                cmd: assertParameterString('command', command),
                cwd: absolutePath,
                duration: DEFAULT_TIMEOUT,
                run: true,
                enableStreamOutput: true,
            },
            result => {
                this.cachedOutput = result.output;
                if (containsServerReadySignal(result.output)) {
                    this.earlyReturnResolve?.({type: 'earlyReturn', data: {output: result.output}});
                }
            }
        );

        return {type: 'executeCommand', data: result} as const;
    }

    private waitForTimeout() {
        return new Promise<TimeoutResult>(resolve => {
            setTimeout(
                () => resolve({type: 'timeout', data: null}),
                DEFAULT_TIMEOUT + 5 * 1000
            );
        });
    }

    private waitForUserAction() {
        return new Promise<UserActionResult>(resolve => {
            this.userActionResolve = resolve;
        });
    }

    private waitForEarlyReturnSignal(cmd: string) {
        return new Promise<EarlyReturnResult>(resolve => {
            this.earlyReturnResolve = resolve;
            setTimeout(
                () => {
                    if (SPECIAL_MATCH_COMMANDS.some(({match}) => match(cmd))) {
                        resolve({type: 'earlyReturn', data: {output: this.cachedOutput}});
                        return;
                    }
                    this.displaySkipButton = true;
                    this.context.updateWebviewMessages();
                },
                5 * 1000
            );
        });
    }

    private waitForCancel() {
        return new Promise<CancelResult>(resolve => {
            this.cancelResolve = resolve;
        });
    }

    private clearPromise() {
        this.earlyReturnResolve?.({type: 'earlyReturn', data: {output: this.cachedOutput}});
        this.userActionResolve?.({type: 'userAction', data: false});
    }

    protected onAccept(): void {
        this.userActionResolve?.({type: 'userAction', data: true});
    }

    protected onReject(): void {
        this.userActionResolve?.({type: 'userAction', data: false});
    }

    protected onCancel(): void {
        this.cancelResolve?.({type: 'cancel', data: null});
    }

    protected beforeStart(): void {
        const {command = ''} = this.parseParams();
        if (!kernel.config.zuluConfig) {
            this.autoRun = false;
            return;
        }
        const {
            terminalAutoExecution = 'off',
            terminalAutoExecutionAllowList = [],
            terminalAutoExecutionDenyList = [],
        } = kernel.config.zuluConfig;
        try {
            const commandNames = parseShellCommandName(command);
            if (terminalAutoExecution === 'guard') {
                const isAllow = commandNames.every(cmd => terminalAutoExecutionAllowList.includes(cmd));
                this.autoRun = isAllow;
                return;
            }
            else if (terminalAutoExecution === 'turbo') {
                const isDeny = terminalAutoExecutionDenyList.some(cmd => commandNames.includes(cmd));
                this.autoRun = !isDeny;
                return;
            }
        }
        catch {
            // 不确定解析命令会不会报错
        }
        this.autoRun = false;
    }
}
