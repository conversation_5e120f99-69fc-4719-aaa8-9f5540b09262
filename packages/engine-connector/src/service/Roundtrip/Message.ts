import crypto from 'node:crypto';
import {
    AgentConversationType,
    AgentToolCallElement,
    VirtualEditor,
    WorkflowStatus,
} from '@comate/plugin-shared-internals';
import {Composer} from '../Composer/Composer.js';
import {LoggerRoleInstance} from '../Logger.js';
import {
    MessageStatus,
    MessageType,
    ReasonMessageSnapshot,
    TextMessageSnapshot,
    ToolCallEventParams,
    ToolCallMessageSnapshot,
    UserMessagePayload,
} from './types.js';
import {WorkflowBase, WorkflowContext} from './Workflow/WorkflowBase.js';
import {createWorkflow} from './Workflow/WorkflowManager.js';
import {Repo} from '../AgentConversation/Zulu/types/knowledge.js';

abstract class MessageBase {
    protected _uuid: string = crypto.randomUUID();
    abstract readonly type: MessageType;
    abstract status: MessageStatus;

    get uuid() {
        return this._uuid;
    }

    markStatus(status: MessageStatus) {
        this.status = status;
    }
}

export class UserRequestMessage<MessageOperationPayload extends UserMessagePayload> extends MessageBase {
    readonly type: MessageType = MessageType.UserRequest;
    readonly status: MessageStatus = MessageStatus.Success;

    constructor(id: string = crypto.randomUUID(), readonly payload: MessageOperationPayload) {
        super();
        this.updateUuid(id);
    }

    updateUuid(uuid: string) {
        this._uuid = uuid;
    }

    toMessage() {
        return {
            id: this.uuid,
            role: 'user',
            status: this.status,
            content: this.payload?.defaultContent ?? (this.payload.query ?? ''),
            code: this.payload?.code,
            payload: this.payload,
        };
    }
}

export class TextMessage extends MessageBase {
    readonly type: MessageType = MessageType.Text;
    readonly composer: Composer;
    status: MessageStatus = MessageStatus.InProgress;
    originalContent = '';
    content: string = '';
    isErrorMessage: boolean = false;

    static createFromSnapshot(
        conversationId: string,
        virtualEditor: VirtualEditor,
        repo: Repo,
        logger: LoggerRoleInstance,
        snapshot: TextMessageSnapshot
    ) {
        const textMessage = new TextMessage(
            conversationId,
            virtualEditor,
            repo,
            logger
        );
        textMessage.rebuild(snapshot.originalContent, snapshot.content, snapshot.composer);
        return textMessage;
    }

    constructor(conversationId: string, virtualEditor: VirtualEditor, repo: Repo, logger: LoggerRoleInstance) {
        super();
        this.composer = new Composer(virtualEditor, repo, logger, AgentConversationType.E2EBotConversation, {
            inlineView: true,
            acceptPayload: {
                conversationId,
                elementId: this.uuid,
            },
        });
    }

    get inProgress() {
        return this.status === MessageStatus.InProgress;
    }

    appendText(chunk: string) {
        this.originalContent += chunk;
        this.content = this.originalContent;
    }

    replaceText(text: string) {
        this.originalContent = text;
        this.content = text;
    }

    async updateComposerContent() {
        this.content = await this.composer.update(this.originalContent);
    }

    async updateComposerStatus() {
        if (this.isErrorMessage) {
            return;
        }
        if (this.composer.tasks.length > 0) {
            this.content = await this.composer.safeToMarkdown();
        }
    }

    end() {
        if (this.inProgress) {
            this.markStatus(MessageStatus.Success);
        }
        // 检查下 composer 是否已经结束，没结束那就是那有问题了
        this.composer.tasks.forEach(task => task.finish !== true && task.cancel());
    }

    async cancel() {
        if (this.inProgress) {
            this.markStatus(MessageStatus.Cancelled);
        }
        this.composer.tasks.forEach(task => task.finish !== true && task.cancel());
    }

    fail() {
        if (this.inProgress) {
            this.markStatus(MessageStatus.Failed);
        }
        this.composer.tasks.forEach(task => task.finish !== true && task.cancel());
    }

    toMessage() {
        return {
            id: this.uuid,
            type: this.type,
            status: this.status,
            content: this.content,
            composer: this.composer,
            isErrorMessage: this.isErrorMessage,
        };
    }

    toJSON() {
        return {
            ...this.toMessage(),
            originalContent: this.originalContent,
        };
    }

    rebuild(originalContent: string, content: string, composer: any) {
        this.originalContent = originalContent;
        this.content = content;
        this.composer.createTaskFromSnapshot(composer);
    }
}

export class ReasonMessage extends MessageBase {
    type: MessageType = MessageType.REASON;
    status = MessageStatus.InProgress;

    protected text: string = '';
    protected startTime: number = Date.now();
    protected lastModifiedTime: number = Date.now();
    id: number;

    constructor(id: number, text: string, startTime: number, lastModifiedTime: number) {
        super();
        this.text = text;
        this.id = id;
        this.startTime = startTime;
        this.lastModifiedTime = lastModifiedTime;
    }

    get inProgress() {
        return this.status === MessageStatus.InProgress;
    }

    appendText(chunk: string) {
        this.lastModifiedTime = Date.now();
        this.text += chunk;
    }

    replaceText(text: string) {
        this.lastModifiedTime = Date.now();
        this.text = text;
    }

    markStatus(status: MessageStatus) {
        this.status = status;
        this.lastModifiedTime == Date.now();
    }

    toMessage() {
        return {
            id: this.id,
            type: this.type,
            text: this.text,
            status: this.status,
            startTime: this.startTime,
            lastModifiedTime: this.lastModifiedTime,
        };
    }

    toJSON() {
        return this.toMessage();
    }

    cancel() {
        this.markStatus(MessageStatus.Cancelled);
    }

    end() {
        this.markStatus(MessageStatus.Success);
    }

    fail() {
        this.markStatus(MessageStatus.Failed);
    }

    static createFromSnapshot(snapshot: ReasonMessageSnapshot) {
        const reasonMessage = new ReasonMessage(
            snapshot.id,
            snapshot.text,
            snapshot.startTime,
            snapshot.lastModifiedTime
        );
        return reasonMessage;
    }
}

export class ToolCallMessage extends MessageBase {
    readonly type: MessageType = MessageType.ToolCall;
    status = MessageStatus.InProgress;
    workflow: WorkflowBase;
    readonly composer: Composer;
    private params: ToolCallEventParams;

    static createFromSnapshot(
        conversationId: string,
        virtualEditor: VirtualEditor,
        repo: Repo,
        logger: LoggerRoleInstance,
        snapshot: ToolCallMessageSnapshot,
        updateWebviewMessages: () => void
    ) {
        const {toolName, params, status} = snapshot;
        // todo 需要还原task
        const context = {repo, virtualEditor, conversationId, updateWebviewMessages};
        const obj = new ToolCallMessage(
            conversationId,
            repo,
            logger,
            virtualEditor,
            context,
            toolName,
            params
        );
        obj.status = (status === MessageStatus.Success || status === MessageStatus.Failed)
            ? status
            : MessageStatus.Cancelled;
        obj.rebuild(snapshot);
        return obj;
    }

    constructor(
        readonly conversationId: string,
        readonly repo: Repo,
        readonly logger: LoggerRoleInstance,
        readonly virtualEditor: VirtualEditor,
        private readonly context: Omit<WorkflowContext, 'elementId' | 'composer'>,
        // 没有名称这个工具就是调用失败，但是要记录下来
        private readonly name: string = '',
        initParams: ToolCallEventParams = {},
        extraDataExposedToModel: Record<string, any> = {}
    ) {
        super();
        this.params = initParams;
        this.composer = new Composer(virtualEditor, repo, logger, AgentConversationType.E2EBotConversation, {
            inlineView: true,
            acceptPayload: {
                conversationId,
                elementId: this.uuid,
            },
        });
        this.workflow = createWorkflow(
            {...this.context, composer: this.composer, elementId: this.uuid},
            this.name,
            extraDataExposedToModel
        );
    }

    get inProgress() {
        return this.status === MessageStatus.InProgress;
    }

    async mergeParams(params?: ToolCallEventParams) {
        await this.updateParams({...this.params, ...params});
    }

    async appendParamContent(params?: ToolCallEventParams) {
        const newParams = this.params;
        for (const [key, value] of Object.entries(params ?? {})) {
            const current = newParams[key] ?? '';
            if (typeof current !== 'string' || typeof value !== 'string') {
                continue;
            }
            newParams[key] = current + value;
        }
        await this.updateParams(newParams);
    }

    end() {
        if (this.inProgress) {
            const status = this.workflow.status === WorkflowStatus.SUCCESS
                ? MessageStatus.Success
                : MessageStatus.Failed;
            this.markStatus(status);
        }
        // 检查下 composer 是否已经结束，没结束的就取消
        this.composer.tasks.forEach(task => task.finish !== true && task.cancel());
    }

    cancel() {
        if (this.inProgress) {
            this.markStatus(MessageStatus.Cancelled);
        }
        this.workflow.cancel();
        // 检查下 composer 是否都cancel了
        this.composer.tasks.forEach(task => task.finish !== true && task.cancel());
    }

    fail() {
        if (this.inProgress) {
            this.markStatus(MessageStatus.Failed);
        }
        this.workflow.cancel();
    }

    abort() {
        if (!this.inProgress) {
            return;
        }
        this.markStatus(MessageStatus.Cancelled);
    }

    startWorkflow() {
        this.workflow.start();
        return this.workflow;
    }

    toMessage() {
        return {
            id: this.uuid,
            type: this.type,
            status: this.status,
            toolName: this.name,
            composer: this.composer.toJSON(),
            workflowStatus: this.workflow.status,
            displayParams: this.workflow.displayParams,
            displayResult: this.workflow.displayResult,

            // TODO：下面这两个参数是 快照 用到的，渲染界面时没用到，后面看下是不是应该换成 toJSON
            workflowSnapshot: this.workflow.toJSON(),
            params: this.params,
        } as AgentToolCallElement;
    }

    rebuild(snapshot: ToolCallMessageSnapshot) {
        this.composer.createTaskFromSnapshot(snapshot.composer);
        this.workflow.rebuild(snapshot.params, snapshot.workflowSnapshot);
    }

    toJSON() {
        return {
            id: this.uuid,
            type: this.type,
            status: this.status,
            toolName: this.name,
            composer: this.composer.toJSON(),
            workflowSnapshot: this.workflow.toJSON(),
            params: this.params,
        } as ToolCallMessageSnapshot;
    }

    private async updateParams(newParams: Record<string, any>) {
        this.params = newParams;
        await this.workflow.setParams(newParams);
    }
}
