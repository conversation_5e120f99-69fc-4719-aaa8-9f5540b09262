import crypto from 'node:crypto';
import {createPatch} from 'diff';
import _ from 'lodash';
import {VirtualEditor, replacePathTextInMarkdown} from '@comate/plugin-shared-internals';
import {AssistantMessage, UserMessage} from '../AgentConversation/AbstractConversation.js';
import {AcceptState, WorkflowStatus} from '../Composer/types.js';
import {LoggerRoleInstance} from '../Logger.js';
import {ReplaceComposerTask} from '../Composer/tasks/ReplaceComposerTask.js';
import {EditComposerTask} from '../Composer/tasks/EditComposerTask.js';
import {isFilePathEqual} from '../Composer/utils/index.js';
import {McpManager} from '../McpManager/index.js';
import {ReasonMessage, TextMessage, ToolCallMessage, UserRequestMessage} from './Message.js';
import {
    MessageType,
    ToolCallEvent,
    UserMessagePayload,
    TextMessageSnapshot,
    ToolCallMessageSnapshot,
    MessageStatus,
    ReasonMessageSnapshot,
} from './types.js';
import {WorkflowResult} from './Workflow/WorkflowBase.js';
import {Repo} from '../AgentConversation/Zulu/types/knowledge.js';

const {findLast} = _;

export enum RoundtripStatus {
    Failed = 'failed',
    Completed = 'completed',
    Cancelled = 'cancelled',
    Analyzing = 'analyzing',
    Generating = 'generating',
}

interface RoundtripSnapshot {
    content?: any;
    id: string;
    status?: RoundtripStatus;
    payload?: UserMessagePayload;
    elements?: Array<TextMessageSnapshot | ToolCallMessageSnapshot | ReasonMessageSnapshot>;
}

export const isToolCallMessage = (v: TextMessage | ToolCallMessage | ReasonMessage): v is ToolCallMessage =>
    v instanceof ToolCallMessage;
export const isTextMessage = (v: TextMessage | ToolCallMessage | ReasonMessage): v is TextMessage =>
    v instanceof TextMessage;

export class Roundtrip<MessageOperationPayload extends UserMessagePayload = UserMessagePayload> {
    private foreground: boolean = false;
    /** 请求一轮接口后新添加的消息，在请求下一轮接口前需要处理完，拿到结果给下一轮请求后会清空 */
    private newAddedMessages: Array<TextMessage | ToolCallMessage | ReasonMessage> = [];
    private multiSuggestions: Array<
        {path: string, row: string, col: string, generatedContent: string, accepted: boolean, id: string}
    > = [];
    private readonly responses: Array<TextMessage | ToolCallMessage | ReasonMessage> = [];
    status: RoundtripStatus = RoundtripStatus.Analyzing;
    readonly uuid: string;
    readonly request: UserRequestMessage<MessageOperationPayload>;
    // 传给 webview 的除了 markdown 和工具调用的其他数据
    assistantMessageContent: Record<string, any> = {};
    // 请求前一些分析过程的数据可以存这
    context: Record<string, string | number> = {};

    /** 专门用于上报数据的 id 和 messageId 区分开，roundtrip 结束后才生成，中间过程没有就不要上报 */
    reportedId: string = '';

    static extractPayloadFromSnapshot(snapshot: RoundtripSnapshot) {
        return {
            ...snapshot.payload,
            id: snapshot.id,
            query: snapshot.payload?.query ?? '',
        };
    }

    constructor(
        private readonly conversationId: string,
        private readonly repo: Repo,
        private readonly logger: LoggerRoleInstance,
        private readonly virtualEditor: VirtualEditor,
        readonly updateWebviewMessages: (forceUpdate?: boolean) => void,
        payload: MessageOperationPayload
    ) {
        this.request = new UserRequestMessage(payload.id, payload);
        this.uuid = crypto.randomUUID();
    }

    get inProgress() {
        return this.status === RoundtripStatus.Generating || this.status === RoundtripStatus.Analyzing;
    }

    get textMessages() {
        return this.responses.filter((message): message is TextMessage => message instanceof TextMessage);
    }

    get toolCallMessages() {
        return this.responses.filter((message): message is ToolCallMessage => message instanceof ToolCallMessage);
    }

    /**
     * 追加文本消息，文本不做任何特殊处理
     * 添加后不会立即更新 webview，需要调用 updateWebviewMessages 方法手动更新
     * @param text 文本消息内容
     * @returns 无
     */
    appendTextResponse(text: string, opts?: {replace?: boolean}) {
        if (!this.inProgress || !text) {
            return;
        }

        const methodName = opts?.replace ? 'replaceText' : 'appendText';

        const latestTextMessage = this.getLatestProgressTextMessage();
        if (latestTextMessage) {
            latestTextMessage[methodName](text);
            return;
        }
        const newTextMessage = this.createNewTextMessage();
        newTextMessage[methodName](text);
    }

    pop() {
        this.responses.pop();
    }

    async appendReasonResponse(messageId: number, text: string, end: boolean, incremental: boolean) {
        if (this.status !== RoundtripStatus.Generating) {
            return;
        }
        // TODO 将增量更新和全量更新拆成两个方法
        if (!text && incremental && end) {
            const latestTextMessage = this.getLatestReasonMessage(messageId);
            if (latestTextMessage && latestTextMessage.inProgress) {
                latestTextMessage.end();
                this.updateWebviewMessages();
            }
            return;
        }

        if (!text) {
            return;
        }

        const latestTextMessage = this.getLatestReasonMessage(messageId);
        if (latestTextMessage) {
            if (latestTextMessage.inProgress) {
                if (incremental) {
                    latestTextMessage.appendText(text);
                }
                else {
                    latestTextMessage.replaceText(text);
                }
                if (end) {
                    latestTextMessage.end();
                }
                this.updateWebviewMessages();
            }
            return;
        }
        const textMessage = new ReasonMessage(messageId, '', Date.now(), Date.now());
        this.addNewMessage(textMessage);
        if (incremental) {
            textMessage.appendText(text);
        }
        else {
            textMessage.replaceText(text);
        }
        this.updateWebviewMessages();
    }

    /**
     * 追加工具调用消息, 是个同步方法，当前工具没处理完以前不能跳过去处理下一个
     * 添加后不会立即更新 webview，需要调用 updateWebviewMessages 方法手动更新
     * @param toolCallEvent 工具调用事件
     * @param messageContext 处理这条消息时的上下文信息，新建 ToolCallMessage 时会存起来
     * @returns 无
     */
    async appendToolCallResponse(
        toolCallEvent: ToolCallEvent,
        messageContext?: {mcpManager?: McpManager, requestInfo?: Record<string, number | string>}
    ) {
        if (this.status !== RoundtripStatus.Generating) {
            return;
        }

        const {eventType, ...toolCallEventData} = toolCallEvent;
        const latestToolCallMessage = this.getLatestProgressToolCallMessage();

        switch (eventType) {
            case 'TOOL_CALL_END': {
                latestToolCallMessage?.startWorkflow();
                break;
            }
            case 'TOOL_CALL_START': {
                await this.createNewToolCallMessage(toolCallEventData, messageContext);
                break;
            }
            case 'TOOL_CALL_ABORT': {
                latestToolCallMessage?.abort();
                break;
            }
            case 'TOOL_CALL_CREATE': {
                const newMessage = await this.createNewToolCallMessage(toolCallEventData, messageContext);
                newMessage.startWorkflow();
                break;
            }
            case 'TOOL_CALL_PARAMS_MERGE': {
                await latestToolCallMessage?.mergeParams(toolCallEvent.params);
                break;
            }
            case 'TOOL_CALL_PARAMS_APPEND': {
                await latestToolCallMessage?.appendParamContent(toolCallEvent.params);
                break;
            }
        }
    }

    setAssistantMessageContent(content: Record<string, any>) {
        this.assistantMessageContent = content;
    }

    getLatestMessage(): TextMessage | ToolCallMessage | ReasonMessage | undefined {
        return this.responses[this.responses.length - 1];
    }

    toMessages() {
        const userMessage = {
            ...this.request.toMessage() as UserMessage,
            // 这个 context 是用户多轮会话时缓存的一些上下文，主要是历史消息恢复时用到
            // 如果后面历史存的是 toJSON 返回的话这个不需要了
            context: this.context,
        };
        const assistantMessage = {
            // roundtrip 的 uuid 就是 assistantId 是通过接口生成的
            id: this.uuid,
            userMessageId: this.request.uuid,
            role: 'assistant',
            status: this.status,
            content: this.assistantMessageContent,
            elements: this.responses.map(message => message.toMessage()),
        } as AssistantMessage<any>;

        return [
            userMessage,
            assistantMessage,
        ];
    }

    toJSON() {
        return {
            payload: this.request.payload,
            elements: this.responses.map(message => message.toJSON()),
        };
    }

    rebuild(snapshot: RoundtripSnapshot) {
        const messages = snapshot.elements ?? [];
        for (const message of messages) {
            if (message.type === MessageType.Text) {
                const textMessage = TextMessage.createFromSnapshot(
                    this.conversationId,
                    this.virtualEditor,
                    this.repo,
                    this.logger,
                    message
                );
                this.responses.push(textMessage);
            }
            else if (message.type === MessageType.ToolCall) {
                const toolCallMessage = ToolCallMessage.createFromSnapshot(
                    this.conversationId,
                    this.virtualEditor,
                    this.repo,
                    this.logger,
                    message,
                    () => this.updateWebviewMessages()
                );
                this.responses.push(toolCallMessage);
            }
            else if (message.type === MessageType.REASON) {
                const reasonMessage = ReasonMessage.createFromSnapshot(message);
                // 不走markStatus，因为它会导致变更lastModifiedTime
                reasonMessage.status = message.status === 'success' ? message.status : MessageStatus.Cancelled;
                this.responses.push(reasonMessage);
            }
        }
        this.status = snapshot.status === RoundtripStatus.Completed || RoundtripStatus.Failed === snapshot.status
            ? snapshot.status
            : RoundtripStatus.Cancelled;
        this.setAssistantMessageContent(snapshot.content ?? {});
    }

    complete() {
        this.ensureAllMessageFinished(true);
        if (this.status === RoundtripStatus.Generating) {
            this.status = RoundtripStatus.Completed;
        }
        this.updateWebviewMessages();
    }

    cancel() {
        if (this.status === RoundtripStatus.Generating || this.status === RoundtripStatus.Analyzing) {
            this.status = RoundtripStatus.Cancelled;
        }
        for (const message of this.responses) {
            message.cancel();
        }
        this.newAddedMessages = [];
    }

    fail(msg?: string) {
        msg && this.appendErrorMessage(msg);
        for (const message of this.responses) {
            message.fail();
        }
        this.ensureAllMessageFinished(false);
        if (this.status === RoundtripStatus.Generating || this.status === RoundtripStatus.Analyzing) {
            this.status = RoundtripStatus.Failed;
        }
        this.updateWebviewMessages();
    }

    /**
     * 处理所有新添加的消息，拿到 workflow 结果
     * @param replacePaths 需要替换的文件路径，替换成 markdown 链接
     * @returns
     */
    async resolveNewAddedMessages(replacePaths?: string[]) {
        this.ensureToolCallMessageWorkflowStarted();
        if (replacePaths && replacePaths.length > 0) {
            await this.replacePathsToMarkdownLinks(replacePaths);
        }
        // 强制更新下界面，防止一些等待用户操作的消息没更新上，操作不了就死循环了
        this.updateWebviewMessages(true);
        const newAddedMessages = this.newAddedMessages;
        const workflowResults = await this.waitForPendingWorkflowResults();
        // 现在消息都是一批处理完后，包括workflow都执行完后，才将消息状态标为结束状态，并不是下一条消息来时结束前一条消息
        newAddedMessages.forEach(message => message.end());
        return workflowResults;
    }

    getComposerTasks(elementId?: string) {
        const allMessages = elementId
            ? this
                .responses
                .filter((t): t is ToolCallMessage => {
                    return t instanceof ToolCallMessage;
                })
                .filter(v => v.uuid === elementId)
            : this.responses.filter((t): t is ToolCallMessage => {
                return t instanceof ToolCallMessage;
            });
        return allMessages
            .map(message => message.composer.tasks.filter(t => t.completed))
            .flat()
            .filter(task => task.action !== 'replaceFrom' || (task as ReplaceComposerTask).visible === true);
    }

    getTextMessage(id?: string) {
        if (!id) {
            return;
        }
        return this.textMessages.find(v => v.uuid === id);
    }

    getToolCallMessage(id?: string) {
        if (!id) {
            return;
        }
        return this.toolCallMessages.find(v => v.uuid === id);
    }

    setForeground(foreground: boolean) {
        this.foreground = foreground;
        this.textMessages.forEach(message => message.composer.setForeground(foreground));
        this.toolCallMessages.forEach(message => message.workflow.setForeground(foreground));
    }

    /**
     * 获取所有新添加的消息，请在 resolveNewAddedMessages 前调用，在处理完之后会被清空
     * @returns 新添加的消息
     */
    getNewAddedMessages() {
        return this.newAddedMessages;
    }

    /**
     * 确保上一个消息的 workflow 已经启动
     */
    ensurePreviousMessageWorkflowStarted() {
        const toolCallMessage = this.getLatestProgressToolCallMessage();
        if (toolCallMessage && toolCallMessage.workflow.isInitializing) {
            toolCallMessage.startWorkflow();
        }
    }

    /**
     * 更新所有 TextMessage 的 composer 状态，比如采纳、放弃后需要调这个方法更新下
     *
     * 遍历所有文本消息，并更新每条消息的 composer 状态
     */
    async updateTextMessageComposerStatus() {
        for (const message of this.textMessages) {
            await message.updateComposerStatus();
        }
    }

    private getLatestProgressTextMessage() {
        const message = this.getLatestMessage();
        return message instanceof TextMessage && message.inProgress ? message : undefined;
    }

    private getLatestReasonMessage(messageId: number) {
        return findLast(this.responses, (response): response is ReasonMessage => {
            return response instanceof ReasonMessage && response.id === messageId;
        });
    }

    private getLatestProgressToolCallMessage() {
        const message = this.getLatestMessage();
        return message instanceof ToolCallMessage && message.inProgress ? message : undefined;
    }

    private addNewMessage(message: TextMessage | ToolCallMessage | ReasonMessage) {
        this.ensurePreviousMessageWorkflowStarted();
        this.responses.push(message);
        this.newAddedMessages.push(message);
    }

    private async createNewToolCallMessage(
        toolCallEventData: Omit<ToolCallEvent, 'eventType'>,
        messageContext?: {mcpManager?: McpManager, requestInfo?: Record<string, number | string>}
    ) {
        const {name, params, ...extraData} = toolCallEventData;
        const context = {
            ...messageContext,
            repo: this.repo,
            virtualEditor: this.virtualEditor,
            conversationId: this.conversationId,
            updateWebviewMessages: () => this.updateWebviewMessages(),
        };
        const newMessage = new ToolCallMessage(
            this.conversationId,
            this.repo,
            this.logger,
            this.virtualEditor,
            context,
            name,
            params,
            extraData
        );
        if (params) {
            await newMessage.workflow.setParams(params);
        }
        newMessage.workflow.setForeground(this.foreground);
        this.addNewMessage(newMessage);
        return newMessage;
    }

    private createNewTextMessage() {
        const textMessage = new TextMessage(
            this.conversationId,
            this.virtualEditor,
            this.repo,
            this.logger
        );
        textMessage.composer.setForeground(this.foreground);
        this.addNewMessage(textMessage);
        return textMessage;
    }

    /**
     * 检查所有消息是否都结束接收 response 并且启动了 workflow
     */
    private ensureToolCallMessageWorkflowStarted() {
        for (const message of this.responses) {
            if (message instanceof ToolCallMessage && message.workflow.isInitializing) {
                message.startWorkflow();
            }
        }
    }

    /**
     * 检查所有状态，任何转圈的都必须给我停掉
     * 理论上这个方法没有任何作用，因为到这里所有message和workflow都结束了
     * 这个方法是兜底处理
     */
    private ensureAllMessageFinished(success: boolean) {
        for (const message of this.responses) {
            if (message instanceof TextMessage) {
                message.composer.tasks.forEach(task => task.finish !== true && task.cancel());
                if (message.inProgress) {
                    message.markStatus(success ? MessageStatus.Success : MessageStatus.Failed);
                }
            }
            else if (message instanceof ToolCallMessage) {
                message.composer.tasks.forEach(task => task.finish !== true && task.cancel());
                message.workflow.cancel();
                if (message.inProgress) {
                    message.markStatus(success ? MessageStatus.Success : MessageStatus.Failed);
                }
            }
            else if (message instanceof ReasonMessage) {
                if (message.inProgress) {
                    message.markStatus(success ? MessageStatus.Success : MessageStatus.Failed);
                }
            }
        }
    }

    private async waitForPendingWorkflowResults() {
        const newAddedToolCallMessages = this.newAddedMessages.filter(isToolCallMessage);
        const pendingWorkflows = newAddedToolCallMessages.map(v => v.workflow);
        this.newAddedMessages = [];

        const results = await Promise.all(pendingWorkflows.map(async v => {
            await v.workflowPromise;
            this.updateWebviewMessages();
            return v.includeInNextRequest ? v.result : null;
        }));

        // 如果会话已经取消了，就不需要再继续请求了，直接返回空数组，workflows 结果放在下一轮请求中处理
        if (this.status === 'cancelled') {
            return [];
        }
        return results.filter((v): v is WorkflowResult => v !== null);
    }

    private appendErrorMessage(msg: string) {
        const textMessage = new TextMessage(
            this.conversationId,
            this.virtualEditor,
            this.repo,
            this.logger
        );
        textMessage.composer.setForeground(this.foreground);
        textMessage.isErrorMessage = true;
        this.responses.push(textMessage);
        // 直接设置不走append流程
        textMessage.originalContent = msg;
        textMessage.content = msg;
        textMessage.end();
    }

    updateAcceptSuggestions() {
        const messages = this.toolCallMessages;
        const reversedMessages = messages.reverse();
        // 从后往前算，有重复的就跳过
        const suggestions = reversedMessages.reduce<typeof this.multiSuggestions>(
            (result, msg) => {
                // 一次只会改一个文件
                const task = msg.composer.tasks[0];
                if (task instanceof EditComposerTask) {
                    const existedTaskIndex = result.findIndex(previous => isFilePathEqual(previous.path, task.key));
                    if (task.status === WorkflowStatus.SUCCESS) {
                        // 存在连续改多个的情况，以反转后消息的最后一个任务为准
                        const latestMsg = reversedMessages.find(msg => {
                            const task = msg.composer?.tasks?.[0];
                            return task instanceof EditComposerTask && isFilePathEqual(task.key, task.key)
                                && task.status === WorkflowStatus.SUCCESS;
                        })!;
                        const content = latestMsg.composer.tasks[0].content;
                        const patchText = createPatch(task.key, task.originalContent || '', content);
                        const generatedContent = patchText
                            .split('\n')
                            .slice(4)
                            .filter(line => line.startsWith('+') || line.startsWith('-'))
                            .join('\n');
                        if (existedTaskIndex === -1) {
                            result.unshift({
                                id: '',
                                path: task.key,
                                row: '1',
                                col: '1',
                                generatedContent,
                                accepted: task.accepted === AcceptState.ACCEPT,
                            });
                        }
                        else {
                            result[existedTaskIndex].generatedContent = generatedContent;
                        }
                    }
                }
                return result;
            },
            []
        );
        this.multiSuggestions = suggestions.map((suggestion, i) => ({...suggestion, id: String(i)}));
        this.logger.info(`[Zulu] reported ${this.multiSuggestions.length} suggestions successfully`);
        return this.multiSuggestions;
    }

    private removeLineStartWithPrefix(lines: string[], prefix: string, count: number) {
        const generatedLines = [];
        let lineIndex = 0;
        while (lineIndex < lines.length) {
            if (count > 0 && lines[lineIndex].startsWith(prefix)) {
                count--;
            }
            else {
                generatedLines.push(lines[lineIndex]);
            }
            lineIndex++;
        }
        return generatedLines;
    }

    updateSuggestionsByPath(
        filePath: string,
        accepted: AcceptState,
        acceptance?: {addLines: number, removeLines: number}
    ) {
        this.multiSuggestions = this.multiSuggestions.map(suggestion => {
            if (isFilePathEqual(suggestion.path, filePath)) {
                if (acceptance) {
                    const lines = suggestion.generatedContent.split('\n');
                    const generatedLines = this.removeLineStartWithPrefix(
                        this.removeLineStartWithPrefix(lines, '+', acceptance.addLines),
                        '-',
                        acceptance.removeLines
                    );
                    suggestion.generatedContent = generatedLines.join('\n');
                    return {
                        ...suggestion,
                        accepted: generatedLines.length > 0,
                    };
                }
                return {
                    ...suggestion,
                    accepted: accepted === AcceptState.ACCEPT,
                };
            }
            return suggestion;
        });
        return this.multiSuggestions;
    }

    /**
     * 处理所有新增的 TextMessage，将路径替换为 markdown 链接
     * TODO 这里逻辑能封装到 TextMessage 里边吗，变成一个后处理的 Workflow 和 ToolCallMessage 保持一致
     * @param pathsToLinks 需要替换的文件路径
     */
    private async replacePathsToMarkdownLinks(pathsToLinks: string[]) {
        const textMessages = this.newAddedMessages.filter(isTextMessage);
        await Promise.all(textMessages.map(async message => {
            message.content = await replacePathTextInMarkdown(message.content, pathsToLinks);
        }));
    }
}
