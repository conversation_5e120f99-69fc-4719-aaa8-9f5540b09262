import osName from 'os-name';
import os from 'os';
import defaultShell from 'default-shell';

export function getOsName() {
    try {
        const name = osName();
        return name;
    }
    catch {
        // window 的某些版本会去执行 powershell 判断，可能会报错
        return os.platform();
    }
}

/**
 * 获取当前系统信息，参考的 cline
 *
 * @param cwd 当前 workspace 目录
 * @returns 系统信息对象，包含操作系统名称、默认shell、用户主目录路径
 */
export function getBaseSysInfo() {
    return {
        os: getOsName(),
        defaultShell,
        homeDir: os.homedir(),
    };
}
