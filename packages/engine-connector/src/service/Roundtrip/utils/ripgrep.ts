import * as childProcess from 'child_process';
import {createInterface} from 'readline';

// TOTO 这些配置给统一起来，engine初始化时有个统一的地方拿
export function getRipgrepPath() {
    return process.env.RIPGREP_BIN_PATH;
}

type RgText = {text: string};

interface RgSubmatch {
    match: RgText;
    start: number;
    end: number;
}

interface RgMatchData {
    path: RgText;
    lines: RgText;
    line_number: number;
    absolute_offset: number;
    submatches: RgSubmatch[];
}

interface RgMatchLine {
    type: 'match' | 'context' | 'begin' | 'end';
    data: RgMatchData;
}

const MAX_LINES = 10000;

const MAX_CONTEXT_LINES = 1;

export interface SearchResult {
    file: string;
    startLine: number;
	endLine: number;
    match: string;
    beforeContext: string[];
    afterContext: string[];
}

class RipgrepParser {
    private results: SearchResult[] = [];
	private currentResult: SearchResult | null = null;
	private bufferedContexts: RgMatchData[] = [];

    constructor(
        private readonly maxMatch: number,
    ) {

    }

    handleLine(line: string): boolean {
		if (line.trim() === '') {
            return false;
        }
        this.resolve(line);
		return this.results.length >= this.maxMatch;
    }

	getResults() {
		if (this.currentResult) {
			this.setAfterContext();
			this.results.push(this.currentResult);
			this.currentResult = null;
		}
		return this.results;
	}

	private resolve(line: string) {
		try {
			const parsed = JSON.parse(line) as RgMatchLine;
			if (parsed.type === 'match') {
				// 如果两个匹配结果是连续的，手动把它们合并成一个
				if (this.currentResult
					&& this.currentResult.file === parsed.data.path.text
					&& this.currentResult.endLine + 1 === parsed.data.line_number
				) {
					this.currentResult.match += parsed.data.lines.text;
					const {startLine, match} = this.currentResult;
					// 正常匹配的末尾会包含\n，所以需要多减1
					const addedLines = match.split('\n').length - 2;
					this.currentResult.endLine = startLine + (addedLines > 0 ? addedLines : 0),
					this.bufferedContexts = [];
					return;
				}

				if (this.currentResult) {
					this.setAfterContext();
					this.results.push(this.currentResult);
				}
				this.currentResult = this.createSearchResult(parsed.data);
				this.setBeforeContext();
			}
			else if (parsed.type === 'context') {
				this.bufferedContexts.push(parsed.data);
			}
		}
		catch {
			// 跳过这条数据
		}
	}

	private setAfterContext() {
		if (!this.currentResult) {
			return [];
		}
		let endLine = this.currentResult.endLine;
		const afterContext: string[] = [];
		for (const context of this.bufferedContexts.slice(0, MAX_CONTEXT_LINES)) {
			if (this.currentResult.file !== context.path.text) {
				break;
			}
			if (context.line_number !== endLine + 1) {
				break;
			}
			endLine = context.line_number;
			afterContext.push(context.lines.text);
		}

		this.bufferedContexts = this.bufferedContexts.slice(afterContext.length);
		this.currentResult.afterContext = afterContext;
	}

	private setBeforeContext() {
		if (!this.currentResult) {
			return [];
		}
		let startLine = this.currentResult.startLine;
		const beforeContext: string[] = [];
		for (let i = this.bufferedContexts.length - 1; i >= 0; i--) {
			const context = this.bufferedContexts[i];
			if (this.currentResult.file !== context.path.text) {
				break;
			}
			if (context.line_number !== startLine - 1) {
				break;
			}
			startLine = context.line_number;
			beforeContext.unshift(context.lines.text);
		}

		this.bufferedContexts = [];
		this.currentResult.beforeContext = beforeContext;
	}

	private createSearchResult(parseData: RgMatchData): SearchResult {
		const matchText = parseData.lines.text;
		return {
			file: parseData.path.text,
			startLine: parseData.line_number,
			endLine: parseData.line_number + matchText.trimEnd().split('\n').length - 1,
			match: matchText,
			beforeContext: [],
			afterContext: [],
		};
	}
}

export async function execRipgrep(bin: string, args: string[], maxMatch = 300) {
	const parser = new RipgrepParser(maxMatch);

	await new Promise((resolve, reject) => {
        const rgProcess = childProcess.spawn(bin, args);
        // cross-platform alternative to head, which is ripgrep author's recommendation for limiting output.
        const rl = createInterface({
            input: rgProcess.stdout,
            // treat \r\n as a single line break even if it's split across chunks.
            // This ensures consistent behavior across different operating systems.
            crlfDelay: Infinity,
        });

        let lineCount = 0;

        rl.on('line', line => {
			const hitLimit = parser.handleLine(line);
			lineCount++;
            if (hitLimit || lineCount >= MAX_LINES) {
                rl.close();
                rgProcess.kill();
            }
        });

        let errorOutput = '';
        rgProcess.stderr.on('data', data => {
            errorOutput += data.toString();
        });
        rl.on('close', () => {
            if (errorOutput) {
                reject(new Error(`ripgrep process error: ${errorOutput}`));
            } else {
                resolve('');
            }
        });
        rgProcess.on('error', error => {
            reject(new Error(`ripgrep process error: ${error.message}`));
        });
    });

	return parser.getResults();
}
