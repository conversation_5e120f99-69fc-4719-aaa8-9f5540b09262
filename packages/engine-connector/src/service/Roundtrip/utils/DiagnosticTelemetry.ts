import {VirtualEditor} from '@comate/plugin-shared-internals';
import {apiTelemetryDiagnostics, DiagnosticData} from '../../Composer/api.js';
import {EditComposerTask} from '../../Composer/tasks/EditComposerTask.js';
import {sleep} from '../../Composer/utils/index.js';
import {isVscode} from '../../../utils/checkIDE.js';
import {WorkflowContext} from '../Workflow/WorkflowBase.js';

export class DiagnosticTelemetry {
    private diagnosticsBeforeEdit: DiagnosticData[] | null = null;

    constructor(
        private readonly composerTask: EditComposerTask,
        private readonly virtualEditor: VirtualEditor,
        private readonly editType: string,
        private readonly workflowContext: WorkflowContext
    ) {

    }

    get enable() {
        const requestInfo = this.workflowContext.requestInfo;
        if (isVscode && requestInfo && typeof requestInfo.conversationId === 'number') {
            return true;
        }
        return false;
    }


    async beforeEdit() {
        if (!this.enable) {
            return;
        }
        const {fileDiagnostics} = await this.getDiagnostics();
        this.diagnosticsBeforeEdit = fileDiagnostics ?? null;
    }

    async afterEdit(patchContent = '') {
        if (!this.enable) {
            return;
        }
        // 稍微等一会插件处理完，新的诊断结果出来
        await sleep(2 * 1000);
        const {fileDiagnostics, originalDiagnostics, modifiedDiagnostics} = await this.getDiagnostics();
        // 如果编辑前后这个文件是打开状态，可以拿到这个文件的诊断，如果文件整个过程都没打开过
        // 可以拿 diff 的左右文件作为修改前和修改后
        // 这两者 schema 不同会诊断结果有一点不一样，很多第三方插件提供的诊断信息在 diff 窗口是没有的
        if (Array.isArray(fileDiagnostics) && Array.isArray(this.diagnosticsBeforeEdit)) {
            await this.telemetry(patchContent, 'file', this.diagnosticsBeforeEdit, fileDiagnostics);
        }
        else if (Array.isArray(originalDiagnostics) && Array.isArray(modifiedDiagnostics)) {
            await this.telemetry(patchContent, 'diff', originalDiagnostics, modifiedDiagnostics);
        }
    }

    private async telemetry(
        patchContent = '',
        type: 'file' | 'diff',
        diagnosticsBeforeEdit: DiagnosticData[],
        diagnosticsAfterEdit: DiagnosticData[]
    ) {
        try {
            const requestInfo = this.workflowContext.requestInfo;
            if (typeof requestInfo?.conversationId !== 'number') {
                return;
            }

            await apiTelemetryDiagnostics({
                sessionId: requestInfo.conversationId,
                editType: this.editType,
                data: {
                    path: this.composerTask.filePath,
                    patch: patchContent,
                    messageId: requestInfo?.messageId ?? 0,
                    type,
                    beforeEdit: {
                        diagnostics: diagnosticsBeforeEdit,
                        content: this.composerTask.originalContent ?? '',
                    },
                    afterEdit: {
                        diagnostics: diagnosticsAfterEdit,
                        content: this.composerTask.content ?? '',
                    },
                }
            });
        }
        catch (e) {
            // 要不要上报记录一下
            return;
        }
    }

    private async getDiagnostics(): Promise<{
        fileDiagnostics?: DiagnosticData[];
        originalDiagnostics?: DiagnosticData[];
        modifiedDiagnostics?: DiagnosticData[];
    }> {
        return this.virtualEditor.getDocumentDiagnostics({
            absolutePath: this.composerTask.absolutePath,
            elementId: this.workflowContext.elementId,
            conversationId: this.workflowContext.conversationId,
        });
    }
}
