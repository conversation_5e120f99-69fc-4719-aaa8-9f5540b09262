export function assertParameterString(key: string, value: any) {
    if (typeof value !== 'string' || !value) {
        throw new Error(`parameter \'${key}\' is required and must be a string`);
    }
    return value;
}

export function toBoolean(value: any) {
    if (typeof value === 'boolean') {
        return value;
    }
    else if (typeof value === 'string') {
        return value.toLowerCase() === 'true';
    }
    return false
}

export function extractStringValues(params: Record<string, any>, keys: string[]) {
    const res: Record<string, string | undefined> = {};
    for (const k of keys) {
        res[k] = typeof params[k] === 'string' ? params[k] : undefined;
    }
    return res;
}
