// 所有需要提前返回的命令匹配规则
export const SPECIAL_MATCH_COMMANDS = [
    // 系统监控和性能工具
    {
        desc: '系统监控',
        match: (cmd: string) => (
            cmd.startsWith('top') ||
            cmd.startsWith('htop')
        ),
    },
    {
        desc: '数据库交互',
        match: (cmd: string) => (
            cmd.startsWith('mysql') ||
            cmd.startsWith('mongo')
        ),
    },
    {
        desc: '调试工具',
        match: (cmd: string) => cmd.startsWith('gdb'),
    },
    {
        desc: '交互式shell',
        match: (cmd: string) => {
            const cmdParts = cmd.split(' ');
            // 如果命令后面跟着文件名（如 .py, .sh, .js 等），说明是在运行脚本而不是交互式shell
            const hasScriptFile = cmdParts.length > 1 && (
                cmdParts[1].includes('.') ||  // 包含扩展名
                cmdParts[1].startsWith('/') || // 绝对路径
                cmdParts[1].startsWith('./') || // 相对路径
                cmdParts[1].startsWith('../')   // 相对路径
            );

            return (
                (cmd.startsWith('python3') && !hasScriptFile) ||
                (cmd.startsWith('python') && !hasScriptFile) ||
                (cmd.startsWith('node') && !hasScriptFile) ||
                (cmd.startsWith('bash') && !hasScriptFile) ||
                (cmd.startsWith('zsh') && !hasScriptFile)
            );
        },
    },
    {
        desc: '文本编辑器和查看器',
        match: (cmd: string) => (
            cmd.startsWith('vim') ||
            cmd.startsWith('less') ||
            cmd.startsWith('screen')
        ),
    },
    {
        desc: '网络工具',
        match: (cmd: string) => cmd.startsWith('telnet'),
    },

    // 开发工具和服务器
    {
        desc: '开发服务器',
        match: (cmd: string) => (
            cmd.startsWith('node server') ||
            cmd.startsWith('nodemon')
        ),
    },
    {
        desc: '构建工具',
        match: (cmd: string) => (
            cmd.startsWith('webpack') && (cmd.includes('--watch') || cmd.includes('serve')) ||
            cmd.startsWith('vite') ||
            cmd.startsWith('ng serve') ||
            cmd.startsWith('vue-cli-service')
        ),
    },
    {
        desc: 'Web服务器',
        match: (cmd: string) => cmd.startsWith('python -m http.server'),
    },
    {
        desc: '文件监控',
        match: (cmd: string) => (
            cmd.startsWith('tail -f') ||
            cmd.startsWith('watch')
        ),
    },
    {
        desc: '网络诊断和监控',
        match: (cmd: string) => (
            cmd.startsWith('ping') ||
            cmd.startsWith('nslookup') ||
            cmd.startsWith('nc -l') ||
            cmd.startsWith('tcpdump') ||
            cmd.startsWith('tcpflow') ||
            cmd.startsWith('iperf3')
        ),
    },
    {
        desc: '远程连接',
        match: (cmd: string) => cmd.startsWith('ssh'),
    },
    {
        desc: '容器工具',
        match: (cmd: string) => cmd.startsWith('docker attach'),
    },

    // 特殊匹配规则
    {
        desc: '版本控制',
        match: (cmd: string) => cmd.includes('git log') && !cmd.includes('-n'),
    },
] as const;

// 服务启动成功的标志，检测到这些字符串后，如果3秒内没有新输出就可以提前结束
const SERVER_READY_SIGNALS = [
    {
        desc: '开发服务器就绪',
        patterns: [
            'Server is running',
            'Server started',
            'Running at',
            'Listening on',
            'ready in',
            'Dev server running at',
            'Local:',
            'Development Server',
            'Serving!',
            'Server ready',
        ],
    },
    {
        desc: '数据库连接成功',
        patterns: [
            'Connection established',
            'Successfully connected',
            'Database connected',
        ],
    },
    {
        desc: '容器启动完成',
        patterns: [
            'Started container',
            'Container is running',
            'Ready to handle connections',
        ],
    },
] as const;

/**
 * 检查terminal输出内容是否包含就绪信号
 * @param content terminal输出内容
 * @returns 是否包含就绪信号
 */
export function containsServerReadySignal(content: string): boolean {
    return SERVER_READY_SIGNALS.some(({patterns}) =>
        patterns.some(pattern => content.includes(pattern))
    );
}
