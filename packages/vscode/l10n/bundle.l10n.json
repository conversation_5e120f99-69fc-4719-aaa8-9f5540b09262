{"version.update.title": "New version is available. Would you like to install?", "version.update.install": "Install Latest Version", "version.update.ignore": "Don't Show Again", "statusBar.not.support.text": "Unsupported language", "statusBar.enable.text": "Enable", "statusBar.title": "<PERSON><PERSON> Comate", "statusBar.nosuggestions": "Baidu Comate: No Suggestion", "completion.panel.accept.text": "Accept Solution", "completion.panel.accept.tooltip": "Replace code with this solution", "completion.panel.codelens.disabled.text": "Please enable CodeLens in settings to use the Baidu Comate Completion panel.", "completion.panel.title": "Completion panel", "completion.panel.empty.text": "No synthesized solutions found.", "completion.panel.hidden.text": "Duplicates hidden", "completion.panel.synthesizing.text": "Synthesizing solutions", "completion.decoration.basic": "💡 Tab to accept, Esc to hidden", "completion.decoration.widget.text": "💡 Tab to accept Comate inline suggestion, Enter to accept IDE suggestion", "completion.decoration.acceptLine.text": "💡 {0} + ↓ to accept line", "nl2code.empty.text": "Sorry, I can't answer this question. Feel free to ask me other programming-related questions.", "nl2code.no.selection.text": "Code selection required. Please try again after selecting code", "optimize.codelens.title": "Optimize", "optimize.codeActions.title": "Ask Comate to optimize", "optimize.codelens.tooltip": "Comate optimize this function", "optimize.error": "Failed to optimize", "optimize.code.prompt": "Please optimize the following code:", "optimize.no.selection.text": "Please select the code to be optimized", "optimize.function.prompt": "Please optimize the following function:", "chatTrial.codelens.title": "Cha<PERSON>", "chatTips.selectedCode.title": "Press {0} + I to trigger inline chat", "chatTips.blankRow.title": "{0} + Y Generate code with <PERSON>ulu", "decorations.generating.title": "Comate is generating...", "decorations.lineComent.trigger.title": "{0} + P to trigger code generation based on this comment", "comment.prompt": "Please add inline comments to the following function:", "comment.codelens.title": "Add Comments", "comment.codelens.tooltip": "Comate add inline comments to this function", "comment.inlinechat.title": "/Add Comments", "comment.generate.error": "Failed to add inline comments.", "docstring.prompt": "Please add docstring to the following function:", "docstring.codelens.title": "Add Docstring", "docstring.codelens.tooltip": "Comate add docstring to this function", "docstring.inlinechat.title": "/Add Docstring", "docstring.generate.error": "Failed to add docstring.", "split.prompt": "Please split the following function:", "split.codelens.title": "Split", "split.codelens.tooltip": "Comate split this function", "split.generate.error": "Failed to split function", "explain.codelens.title": "Explain", "explain.codeActions.title": "Ask Comate to explain", "explain.codelens.tooltip": "Comate explain this function", "explain.generate.error": "Failed to generate explanation.", "explain.no.selection.text": "Please select the code to be explained.", "explain.prompt": "Please explain the following {0}:", "unitTest.codelens.title": "Generate UT", "unitTest.codelens.tooltip": "Comate generate unit test for this function", "unitTest.generate.error": "Failed to generate unit test.", "unitTest.accept.error": "Failed to accept unit test：", "unitTest.prompt": "Please generate unit test for the following {0}{1}:", "inlinechat.placeholder.generate": "Code Generation from Natural Language", "inlinechat.placeholder.selected": "Enter Natural Language to Modify Code or Select Recommended Items (Enter to Submit, Esc to Cancel)", "inlinechat.placeholder.unselected": "Enter Natural Language to Generate Code or Select Recommended Items (Enter to Submit, Esc to Cancel)", "inlinechat.title": "{0} Comate is generating code", "inlinechat.accept": "Accept {0}", "inlinechat.reject": "Reject {0}", "inlinechat.placeholder.error": "Please accept or reject the previous changes", "inlinechat.codelens.title": "Inline Chat", "inlinechat.codeActions.title": "Inline Chat", "inlineLog.prompt": "Please add log statements for the following function:", "inlineLog.codelens.title": "Add Logs", "inlineLog.codelens.tooltip": "Comate add log statements for this function", "inlineLog.generate.error": "Failed to add log statements", "diff.title": "View Changes", "diff.accept": "Accept {0}", "diff.reject": "Reject {0}", "diff.dualScreenDiff.open": "Open Diff {0}", "chat.webview.title": "Comate <PERSON><PERSON>", "chat.no.insertPosition.text": "Please specify the insertion position first.", "accept.path.error": "Please enter the correct file path.", "quickFix.action.text": "Ask comate to fix", "quickFix.prompt": "Fix these problems according to the diagnostic information{0}:", "quickFix.generate.error": "Failed to problems", "quickFix.prompt.suffix": ", just provide the fixed code without inline comment", "regexHover.tooltip": "Explain this regular expression", "regexHover.prompt": "Please explain this regular expression: ", "embeddings.maximum.files.message": "Indexing failed: The number of files in the workspace has exceeded the maximum limit of {0}.", "embeddings.progress.message": "Update Embeddings", "embeddings.progress.tooltip.message": "Comate is updating embeddings, which will enhance the quality of the generation", "openPlatform.title.text": "Comate Open Platform", "openPlatform.webview.title": "Comate Docs Management", "messageGenerate.generate.error": "Failed to generate: {0}", "messageGenerate.noDiff.error": "Failed to retrieve any content changes.", "messageGenerate.getDiff.error": "Failed to retrieve diff content: {0}", "messageGenerate.emptyContent.error": "Failed to generate commit message.", "autoWork.auth.modal.title": "Authorization Confirmation", "autoWork.indexing.message": "Indexing in progress. Please wait.", "autoWork.connection.interrupt.message": "Connection interrupted. Please try again.", "autoWork.indexing.reminder.text": "Comate is analyzing your codebase, progress {0}%. For more context-specific solutions, wait for indexing to complete before regenerating.", "autoWork.error": "AutoWork generation failed", "autoWork.task.thought": "Analyze the goal", "autoWork.task.answer": "Frame the answer", "autoWork.autoDebug": "Analyze and fix this with <PERSON><PERSON>", "autoWork.autoDebug.prompt": "Please analyze the terminal's error logs and provide a solution. Only resolve the first error", "autoWork.autoDebug.thought": "Analyze your terminal output", "autoWork.autoDebug.search": "Retrieve information related to the output", "autoWork.autoDebug.answer": "Generate solution", "comatePlus.permission.workspace.text": "read and write workspace files", "comatePlus.permission.disk.text": "read and write disk files", "comatePlus.permission.user.text": "get user information", "comatePlus.auth.message": "Grant permission for the plugin {0} to {1}?", "comatePlus.userInfo.error": "Failed to fetch user information. Please verify that the username in the configuration is correct.", "comatePlus.clear.permission": "All plugin permissions have been successfully revoked.", "comatePlus.split.error": "The selected function is already compact, no need for further splitting. You can select another function.", "comatePlus.noSelection": "Please select code in the editor", "comatePlus.not.function.text": "The selected code is not a function. Please make a new selection.", "common.generate.error": "Failed to generate: {0}", "common.unknown.error": "unknown error", "common.request.error": "Failed to request: {0}", "common.code": "code", "common.function": "function", "common.authorize": "Authorize", "common.using": "using", "common.without": "without", "common.comma": ", ", "common.semicolon": "; ", "common.framework": "framework", "common.related": "Related", "common.prompt": "Prompt", "common.thinking": "Thinking", "common.searchResult": "Search Result", "common.deny": "拒绝", "common.navigateToSetting": "Go to Setting", "common.customize.error": "Hybrid Cloud users, please go to Plugin Configuration - Service Configuration to check and debug.", "common.private.error": "Private Service users, please go to Plugin Configuration - Service Configuration to check and debug.", "log.level.placeholder": "Set log level", "do.not.show.again": "Don't show again", "login.message": "Hi there! I'm {0}, your AI coding assistant. Ready to log in and dive into the joy of coding together?", "login.action.text": "<PERSON><PERSON>", "login.action.withLicense.text": "Login with license", "login.device.error": "Device fingerprint retrieval failed. Unable to proceed with login.", "login.loggingIn.text": "Baidu Comate is logging in...", "login.success": "<PERSON>du Comate login successful!", "login.failed": "Failed to login: {0}", "login.token.error": "Failed to get token", "login.key.error": "Failed to get key", "login.install.internal": "Install Internal Version", "login.internalUser.message": "Welcome! Switch to Baidu's internal version for an enhanced experience? (Uninstall the current extension and download the internal version).", "login.agreement.message": "Dear Baidu Comate User, to enhance our service, we will update the user service agreement on April 17, 2024. Please [click here](https://comate.baidu.com/en/userAgreement) for the updates. Thank you for your support!", "renew.action.text": "Renew Now", "command.quick.pick.placeholder": "Select command to execute", "onboarding.import.title": "Importing from {0}", "onboarding.importResult.title": "Imported from {0}: ", "onboarding.importResult.settings.failed": "error importing settings, keybindings, or snippets", "onboarding.importResult.settings.succeed": "settings, keybindings, and snippets are all set", "onboarding.importResult.extensions.failed": "error importing extensions", "onboarding.importResult.extensions.succeed": "extensions are all set", "onboarding.importResult.failedExtensions": "failed to import {0}", "onboarding.importResult.unsupportedExtensions": "ignored unsupported extensions {0}"}