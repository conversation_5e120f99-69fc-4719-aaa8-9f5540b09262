/* eslint-disable complexity */
import {IncomingMessage} from 'http';
import * as vscode from 'vscode';
import {CancelToken} from 'axios';
import {SSEProcessor} from '@comate/plugin-shared-internals';
import {Message} from '@shared/protocols';
import {getExtensionContextAsync} from '@/utils/extensionContext';
import {TasksProcessor} from '@/utils/TasksProcessor';
import {ICompletionSuccessRateTracker} from '@/services/CompletionProvider/SuccessRateTracker/types';
import {TYPES} from '@/inversify.config';
import {iocContainer} from '@/iocContainer';
import {getDeviceUUIDThrottled} from '@/utils/deviceUUID';
import {VSCodeConfigProvider} from '@/services/ConfigProvider';
import {isSaasOrPoc} from '@/utils/features';
import {getTraceRepoInfo} from '@/utils/trace';
import {hideSensitiveContent} from '@/utils/common';
import {ChatViewProvider} from '@/services/ChatViewProvider';
import {CHAT_MODEL_ID} from '@/services/ModelSelector';
import {
    generateBatchCode,
    generateCode,
    GenerateCodeOptions,
    generateStreamCode,
    RawGenerateCode,
    GenerateCodeResponse,
    generateCodelensStreamCode,
} from '../api';
import {SWAN_APP_NAME} from '../constants';
import {UserService} from '../services/UserService';
import {SuccessReturn, UnsuccessReturn} from '../services/types';
import {isForbidden, showForbiddenMessage} from './showForbiddenMessage';
import {debug, error} from './outputChannel';

export const getIdeName = () => {
    const appName = vscode.env.appName;
    if ($features.ENTERPRISE_VERSION && $features.ENTERPRISE_VERSION === 'gitee') {
        return 'vscode-gitee';
    }
    switch (appName) {
        case 'Comate':
        case 'comate':
        case 'Comate Dev':
        case 'comate dev':
            return 'comate';
        case 'iCoding':
            return 'vscode-iCoding';
        case SWAN_APP_NAME:
            return 'vscode-swan';
        default:
            return 'vscode-local';
    }
};

export async function buildParams(
    document: Pick<vscode.TextDocument, 'uri' | 'getText'> | undefined,
    position: vscode.Position,
    userService: UserService
): Promise<SuccessReturn<GenerateCodeOptions> | UnsuccessReturn> {
    const usernamePromise = userService.getCurrentUser();

    const repoInfoPromise = getTraceRepoInfo();

    const contextPromise = getExtensionContextAsync();

    const [usernameRes, repoInfo, contextRes] = await Promise.allSettled(
        [usernamePromise, repoInfoPromise, contextPromise]
    );

    // username 如果获取失败，就不往下走了
    if (usernameRes.status === 'rejected') {
        return {
            type: 'failed',
            reason: 'get username failed',
        };
    }
    const username = usernameRes.value[0];

    const repo = (() => {
        if (repoInfo.status === 'rejected') {
            return '';
        }
        return repoInfo.value.repoName;
    })();

    const repoId = (() => {
        if (repoInfo.status === 'rejected') {
            return '';
        }
        return repoInfo.value.repoId;
    })();

    const path = document ? vscode.workspace.asRelativePath(document.uri, false) : '';

    const version = (() => {
        if (contextRes.status === 'rejected') {
            return '';
        }
        const currentVersion: string = contextRes.value.extension.packageJSON.version;
        return currentVersion;
    })();

    const key = isSaasOrPoc
        ? iocContainer.get(VSCodeConfigProvider).getLicense()
        : undefined;

    const device = isSaasOrPoc
        ? await getDeviceUUIDThrottled() ?? ''
        : undefined;

    const params = {
        key,
        device,
        ide: getIdeName(),
        username,
        repo,
        repoId,
        path,
        rawContent: document?.getText() ?? '',
        content: document?.getText() ?? '',
        row: `${position.line + 1}`,
        col: `${position.character + 1}`,
        pluginVersion: version,
        modelKey: '',
    };

    const modelType = (() => {
        if (contextRes.status === 'rejected') {
            return '';
        }
        return contextRes.value.globalState.get<string>(CHAT_MODEL_ID);
    })();

    if (modelType) {
        params.modelKey = modelType;
    }

    return {type: 'success', value: params};
}

function filterEmptyUserAndAssistant(data: Message[]): Message[] {
    const skipIndices: number[] = [];

    data.forEach((item, index) => {
        if (item.role === 'user') {
            // If role is user and content is empty, add indices of user and corresponding assistant to skipIndices.
            const assistantIndex = data.findIndex(
                (assistant, assistantIndex) => assistant.role === 'assistant' && assistantIndex > index
            );
            if (!item.content) {
                skipIndices.push(index);
                if (assistantIndex > -1) {
                    skipIndices.push(assistantIndex);
                }
            }
            // 内容非字符串的也排除
            if (typeof data[assistantIndex].content !== 'string') {
                skipIndices.push(index);
                skipIndices.push(assistantIndex);
            }
        }
    });

    return data.filter((_, index) => !skipIndices.includes(index));
}

export function buildChatHistory(chatViewProvider: ChatViewProvider, maxCount = 10) {
    const chatHistories = chatViewProvider.getChatHistory(maxCount, {});

    const filteredChatHistories = filterEmptyUserAndAssistant(chatHistories);

    return JSON.stringify(filteredChatHistories.map(v => ({
        role: v.role === 'user' ? 'user' : 'assistant',
        content: v.content.trim() + (v.code ? `\n${hideSensitiveContent(v.code)}` : ''),
    })));
}

export type GeneratorReturn = AsyncGenerator<RawGenerateCode[], void, void>;

const isValidData = (position: vscode.Position, responseData?: Partial<RawGenerateCode>) => {
    if (
        !responseData
        || !responseData.content
        || !responseData.range
        || !responseData.uuid
    ) {
        return false;
    }
    const [startLine, startCharacter, endLine] = responseData.range;
    if (startLine !== endLine || startCharacter - 1 !== position.character) {
        return false;
    }

    return true;
};

export async function fetchWithParams(params: GenerateCodeOptions) {
    const editor = vscode.window.activeTextEditor;
    const line = editor?.selection.start.line;
    const uri = editor?.document.uri;
    const result = await generateCode(params);
    const data = result.data;
    const tracker = iocContainer.get<ICompletionSuccessRateTracker>(TYPES.ICompletionSuccessRateTracker);
    if (data.status === 'OK') {
        tracker.trackSuccess(uri, line);
        return data.data as RawGenerateCode;
    }
    // 如果是非forbidden的错误，则生成失败
    if (!isForbidden(data)) {
        tracker.trackFailure(uri, line);
    }
    throw new Error(data.message);
}

export async function fetchCompletionBatch(params: GenerateCodeOptions, position: vscode.Position) {
    const editor = vscode.window.activeTextEditor;
    const line = editor?.selection.start.line;
    const uri = editor?.document.uri;

    const result = await generateBatchCode(params);
    const data = result.data;

    const tracker = iocContainer.get<ICompletionSuccessRateTracker>(TYPES.ICompletionSuccessRateTracker);

    if (data.status !== 'OK') {
        tracker.trackFailure(uri, line);
        throw new Error(data.message);
    }

    const completions: RawGenerateCode[] = [];
    for (const item of data.data ?? []) {
        if (isValidData(position, item)) {
            completions.push(item as RawGenerateCode);
        }
    }

    tracker.trackSuccess(uri, line);
    return completions;
}

const batchSize = 2;

export async function fetchAndStreamCompletions(
    document: vscode.TextDocument,
    position: vscode.Position,
    useService: UserService,
    count: number
): Promise<SuccessReturn<GeneratorReturn> | UnsuccessReturn> {
    const params = await buildParams(document, position, useService);
    if (params.type !== 'success') {
        return params;
    }
    const tasks: Array<Promise<RawGenerateCode[]>> = [];
    // 写死 10 个，后端现在 batch 接口是 2 条
    for (let i = 0; i < count / batchSize; i++) {
        tasks.push(fetchCompletionBatch(params.value, position));
    }

    const processResult = new TasksProcessor(tasks).processSSE();

    return {
        type: 'success',
        value: processResult,
    };
}

export type StreamResponse = Partial<RawGenerateCode> & GenerateCodeResponse;

export async function processIncomingMessage(
    data: IncomingMessage,
    update: (
        content: string,
        uuid: string,
        chatId: string,
        conversationId?: number,
        reasoningEnd?: boolean,
        reasoningSummary?: string
    ) => void,
    cancellationToken?: vscode.CancellationToken,
    isExemptionsReasonUI = true
) {
    const processor = new SSEProcessor<StreamResponse>(data, JSON.parse, cancellationToken);
    let uuid = '';
    let content = '';
    let chatId = '';
    let reasoningSummary = '';
    // eslint-disable-next-line no-undef-init
    let conversationId: number | undefined = undefined;
    const updateContent = (data: Partial<RawGenerateCode>, completed = false) => {
        const isEndReasoning = data.reasoningEnd || !data.hasOwnProperty('reasoningEnd');
        if (data.uuid && !uuid) {
            uuid = data.uuid;
            debug('chat response uuid:', uuid);
        }
        if (data.chatId && !chatId) {
            chatId = data.chatId;
        }
        if (data.conversationId && conversationId === undefined) {
            conversationId = data.conversationId;
        }
        if (data.content && (isEndReasoning || isExemptionsReasonUI)) {
            content += data.content;
            // WARN 为什么结束了不更新：我们现在的实现都是等流结束了使用的return的内容更新，update里就不需要更新了，我们的调用现在是按这个逻辑来的
            !completed && update(
                content, uuid, chatId, conversationId, data.reasoningEnd, reasoningSummary.replace(/null/g, ''));
        }
        if (data.reasoningSummary && data.reasoningSummary !== null) {
            reasoningSummary += data.reasoningSummary;
            !data.reasoningEnd
            && update(content, uuid, chatId, conversationId, data.reasoningEnd, reasoningSummary.replace(/null/g, ''));
        }
    };
    try {
        for await (const chunk of processor.processSSE()) {
            if (showForbiddenMessage(chunk as any) || chunk.message) {
                processor.error = true;
                processor.errorMsg = chunk.message || (chunk as any);
                processor.cancel();
                throw new Error((chunk as GenerateCodeResponse).message);
            }
            updateContent(chunk.data ? chunk.data : chunk, data.complete);
        }
        return {content, uuid, processor, chatId, conversationId};
    }
    catch (e: any) {
        // 临时处理生成到一半然后异常的情况
        if (content || e.message) {
            error('request failed: ', e.message);
            return {content, uuid, processor, chatId, conversationId};
        }
        throw e;
    }
}

export async function fetchAndStreamCode(
    params: GenerateCodeOptions,
    update: (content: string, uuid: string, chatId: string, conversationId?: number) => void,
    cancellationToken?: vscode.CancellationToken,
    cancelToken?: CancelToken
) {
    const res = await generateStreamCode(params, cancelToken);
    return processIncomingMessage(res.data, update, cancellationToken);
}

export async function fetchAndStreamCodelens(
    params: GenerateCodeOptions,
    update: (content: string, uuid: string, chatId: string, conversationId?: number) => void,
    cancellationToken?: vscode.CancellationToken,
    cancelToken?: CancelToken,
    isExemptionsReasonUI?: boolean
) {
    const res = await generateCodelensStreamCode(params, cancelToken);
    return processIncomingMessage(res.data, update, cancellationToken, isExemptionsReasonUI);
}
