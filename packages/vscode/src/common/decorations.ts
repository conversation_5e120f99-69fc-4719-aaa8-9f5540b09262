import {repeat} from 'lodash';
import * as vscode from 'vscode';

const tabDecorationStyle: vscode.ThemableDecorationAttachmentRenderOptions = {
    textDecoration:
        // eslint-disable-next-line max-len
        'none; white-space: pre; box-sizing: border-box; border-radius: 4px; box-shadow: inset 0 0 0 1px #03CAEE;',
    color: '#939796',
    backgroundColor: new vscode.ThemeColor('editor.background'),
    height: '100%',
};

export const createTabToJumpGuide = (): vscode.TextEditorDecorationType => {
    return vscode.window.createTextEditorDecorationType({
        before: {
            ...tabDecorationStyle,
            contentText: ' ← Tab To Jump ',
        },
    });
};

export const createTabToJumpUpGuide = (lineNumber: number): vscode.TextEditorDecorationType => {
    return vscode.window.createTextEditorDecorationType({
        before: {
            ...tabDecorationStyle,
            contentText: ` ↑ Tab To Jump  line ${lineNumber + 1} `,
        },
    });
};

export const createTabToJumpDownGuide = (lineNumber: number): vscode.TextEditorDecorationType => {
    return vscode.window.createTextEditorDecorationType({
        before: {
            ...tabDecorationStyle,
            contentText: ` ↓ Tab To Jump  line ${lineNumber + 1} `,
        },
    });
};

export const createTabToAcceptGuide = (): vscode.TextEditorDecorationType => {
    return vscode.window.createTextEditorDecorationType({
        before: {
            ...tabDecorationStyle,
            contentText: ' ✓ Tab To Accept ',
        },
    });
};

// 当有左右 diff 时，为了对齐右侧代码，在行尾添加标准 tab 提示一样宽的不可见内容，实现对齐效果
export const createInvisibleTabToAcceptGuide = (): vscode.TextEditorDecorationType => {
    return vscode.window.createTextEditorDecorationType({
        before: {
            ...tabDecorationStyle,
            contentText: ' ✓ Tab To Accept ',
            textDecoration: tabDecorationStyle.textDecoration + 'border-radius: 0; box-shadow: none;',
            color: new vscode.ThemeColor('editor.background'),
        },
    });
};

export const createEndOfLineWhitespace = (count: number = 1): vscode.TextEditorDecorationType => {
    return vscode.window.createTextEditorDecorationType({
        before: {
            contentText: repeat(' ', count),
            textDecoration: 'none; white-space: pre',
            backgroundColor: new vscode.ThemeColor('editor.background'),
            height: '100%',
        },
    });
};
