import crypto from 'node:crypto';
import * as vscode from 'vscode';
import {RawGenerateCode, GenerateCodeOptions, stopGenerate} from '@/api';
import {RewriteBaseResult, TriggerSource, ActionType, apiRewriteAndJump} from '@/api/smartTab';
import {RewriteHandler} from '@/services/CompletionProvider/types';
import {adjustRewriteRange} from '@/services/EditPredictionProvider/utils';
import {formatForRequest} from '@/services/ProgrammingContextTracker/utils';
import {checkSubsequence} from '@/utils/common';
import {fetchWithParams} from './Fetcher';

export interface InlineCompletionResult {
    kind: 'completion';
    value: RawGenerateCode;
}

export interface InlineRewriteResult {
    kind: 'rewrite';
    value: RewriteBaseResult;
}

export type InlineEditResult = InlineCompletionResult | InlineRewriteResult;

// eslint-disable-next-line complexity, max-statements
export function castRewriteToCompletion(
    document: vscode.TextDocument,
    position: vscode.Position,
    rewrite: RewriteBaseResult
): RawGenerateCode | null {
    const rewriteStartLine = rewrite.startRow - 1;
    const rewriteEndLine = rewrite.endRow - 1;
    // 光标不在改写范围
    if (position.line < rewriteStartLine || position.line > rewriteEndLine) {
        return null;
    }
    const oldText = document.getText(
        new vscode.Range(rewriteStartLine, 0, rewriteEndLine, document.lineAt(rewriteEndLine).text.length)
    );
    const newText = rewrite.generatedContent;
    if (newText.length <= oldText.length) {
        return null;
    }
    const oldLines = oldText.split(/\r?\n/);
    const newLines = newText.split(/\r?\n/);
    if (newLines.length < oldLines.length) {
        return null;
    }
    const normalizedPosition = position.with(position.line - rewriteStartLine);

    const firstN = normalizedPosition.line;
    const lastN = oldLines.length - 1 - normalizedPosition.line;

    for (let i = 0; i < firstN; i++) {
        if (newLines.at(i) !== oldLines.at(i)) {
            return null;
        }
    }
    for (let i = 1; i < lastN + 1; i++) {
        if (newLines.at(-i) !== oldLines.at(-i)) {
            return null;
        }
    }
    const oldCursorLine = oldLines[normalizedPosition.line];
    const newCursorLine = newLines[normalizedPosition.line];
    const oldCursorLineSuffix = oldCursorLine.slice(normalizedPosition.character);
    const newCursorLineSuffix = newCursorLine.slice(normalizedPosition.character);
    if (oldCursorLine.slice(0, normalizedPosition.character) !== newCursorLine.slice(0, normalizedPosition.character)) {
        return null;
    }
    if (oldCursorLineSuffix.length > 0 && !checkSubsequence(newCursorLineSuffix, oldCursorLineSuffix)) {
        return null;
    }
    const insertedLines = [
        newCursorLineSuffix,
        ...newLines.slice(firstN + 1, lastN ? -lastN : undefined),
    ];
    return {
        uuid: rewrite.uuid,
        content: insertedLines.join('\n'),
        range: [
            position.line + 1,
            position.character + 1,
            position.line + 1,
            position.character + 1,
        ],
        score: 0,
    };
}

interface RequestSignal {
    sent: boolean;
}

async function _fetchInlineEdit(
    document: vscode.TextDocument,
    position: vscode.Position,
    selectedCompletionInfo: vscode.SelectedCompletionInfo | undefined,
    params: GenerateCodeOptions,
    requestSignal: RequestSignal,
    rewriteHandler?: RewriteHandler,
    cancelToken?: vscode.CancellationToken
): Promise<InlineEditResult> {
    const startLine = Math.max(0, position.line - 1);
    const endLine = Math.min(position.line + 2, document.lineCount - 1);
    const [rewriteStartLine, rewriteEndLine] = adjustRewriteRange(document, position, startLine, endLine);
    if (rewriteHandler?.getEnabled(document.uri, rewriteStartLine, rewriteEndLine)) {
        const context = await rewriteHandler.contextTracker.getContext(document.uri, position, 10);
        requestSignal.sent = true;
        const [rewritePromise, cursorPredictionPromise] = await apiRewriteAndJump(
            {
                ...params,
                diffList: JSON.stringify(formatForRequest(context)),
                triggerSource: TriggerSource.EditCode,
                actionType: ActionType.Rewrite,
                rewriteSpecificStartRow: rewriteStartLine + 1,
                rewriteSpecificEndRow: rewriteEndLine + 1,
            },
            1000 * 20,
            cancelToken
        );
        const result = await rewritePromise;
        rewriteHandler.setCursorPredictionContext(cursorPredictionPromise);
        if (result.modiType === 'rewrite') {
            // eslint-disable-next-line max-depth
            if (!selectedCompletionInfo) {
                // TODO: 有候选项时，发给模型的原文会有拼接，这里转化逻辑没有考虑这类情况，无法正确计算出 insertedText @tianzerun
                const completion = castRewriteToCompletion(
                    document,
                    position,
                    result
                );
                // eslint-disable-next-line max-depth
                if (completion) {
                    // 如果这个改写可以转为续写，则改为续写结果
                    return {
                        kind: 'completion',
                        value: completion,
                    };
                }
            }
            return {
                kind: 'rewrite',
                value: result,
            };
        }
        return {
            kind: 'completion',
            value: {
                uuid: result.uuid,
                content: result.generatedContent,
                range: result.range,
                score: result.score,
            },
        };
    }
    requestSignal.sent = true;
    const completion = await fetchWithParams(params);
    return {
        kind: 'completion',
        value: completion,
    };
}

export async function fetchInlineEdit(
    document: vscode.TextDocument,
    position: vscode.Position,
    selectedCompletionInfo: vscode.SelectedCompletionInfo | undefined,
    params: GenerateCodeOptions,
    rewriteHandler?: RewriteHandler,
    cancelToken?: vscode.CancellationToken
): Promise<InlineEditResult> {
    const requestId = crypto.randomUUID();
    params.requestId = requestId;
    const requestSignal = {sent: false};

    const stopGenerationDisposable = cancelToken?.onCancellationRequested(() => {
        if (requestSignal.sent) {
            stopGenerate(requestId);
        }
    });

    try {
        return await _fetchInlineEdit(
            document,
            position,
            selectedCompletionInfo,
            params,
            requestSignal,
            rewriteHandler,
            cancelToken
        );
    }
    catch (e) {
        throw e;
    }
    finally {
        stopGenerationDisposable?.dispose();
    }
}
