/* eslint-disable @typescript-eslint/no-unused-vars */
// @ts-nocheck
import crypto from 'node:crypto';
import * as vscode from 'vscode';
import {Container} from 'inversify';
import axios from 'axios';
import {iocContainer} from '@/iocContainer';
import {VSCodeConfigProvider} from '@/services/ConfigProvider';
import {getExtensionContextAsync} from '@/utils/extensionContext';
import MultilineModelFeatures from '@/services/Helper/MultilineModel/MultilineModelFeatures';
import {UserService} from '../../services/UserService';
import {ComateAction} from '../../utils/tracker';
import {PromptInfo} from '../prompt/extractPrompt';
import {ContextualFilterManager} from '../../services/Helper/ContextualFilter';
import {InlineCompletionItem} from '../../services/types';

const BASE_URL_MAPPING: Record<string, string> = {
    internal: 'https://comate.baidu-int.com',
    saas: 'https://comate.baidu.com',
    poc: 'https://comate.baidu.com',
};

const logHost = BASE_URL_MAPPING[$features.PLATFORM];

function now() {
    return new Date().getTime();
}

export class TelemetryData {
    private displayedTime: number | undefined = undefined;

    constructor(
        readonly properties: Record<string, any>,
        readonly measurements: Record<string, any>,
        private readonly issuedTime: number
    ) {}

    static createAndMarkAsIssued(properties?: Record<string, any>, measurements?: Record<string, any>) {
        // eslint-disable-next-line no-param-reassign
        properties = {
            ...properties,
            unique_id: crypto.randomUUID(),
        };
        return new TelemetryData(
            properties || {},
            measurements || {},
            now()
        );
    }

    extendedBy(properties: Record<string, any>, measurements?: Record<string, any>) {
        const newProperties = {
            ...this.properties,
            ...properties,
        };
        const newMeasurements = {
            ...this.measurements,
            ...measurements,
        };
        const result = new TelemetryData(newProperties, newMeasurements, this.issuedTime);
        return result;
    }

    markAsDisplayed() {
        if (this.displayedTime === undefined) {
            this.displayedTime = now();
        }
    }

    readCodeDuration() {
        if (this.displayedTime) {
            return now() - this.displayedTime;
        }
        return 0;
    }

    requestCodeDuration() {
        if (this.displayedTime && this.issuedTime) {
            return this.displayedTime - this.issuedTime;
        }
        return 0;
    }
}

export function telemetrizePromptLength(prompt: PromptInfo) {
    return prompt.isFimEnabled
        ? {
            promptPrefixCharLen: prompt.prefix.length,
            promptSuffixCharLen: prompt.suffix.length,
        }
        : {
            promptCharLen: prompt.prefix.length,
        };
}

interface ExtraData {
    uuid: string;
    displayText: string;
    telemetry: TelemetryData;
}

export async function telemetry(
    globalContext: Container,
    action: ComateAction,
    extra: ExtraData,
    document?: vscode.TextDocument,
    position?: vscode.Position
) {
    const userService = globalContext.get(UserService);
    const [username] = await userService.getCurrentUser();
    const context = await getExtensionContextAsync();
    const license = iocContainer.get(VSCodeConfigProvider).getLicense() ?? '';
    const contextualFilterScore = extra.telemetry.measurements.contextualFilterScore;
    const [
        ,
        probabilityAcceptByDecisionTree,
        probabilityAcceptByLinearRegression,
    ] = contextualFilterScore ?? [];
    let prefix = '';
    let suffix = '';
    let fileName = '';
    let prefixFeatures = {};
    let suffixFeatures = {};
    if (document && position) {
        prefix = document.getText(new vscode.Range(new vscode.Position(0, 0), position));
        suffix = document.getText(new vscode.Range(position, document.lineAt(document.lineCount - 1).range.end));
        const multilineModelFeatures = new MultilineModelFeatures(prefix, suffix, document.languageId);
        prefixFeatures = multilineModelFeatures.prefixFeatures;
        suffixFeatures = multilineModelFeatures.suffixFeatures;
        fileName = document.uri.toString();
    }
    const params = {
        action,
        probabilityAcceptByDecisionTree,
        probabilityAcceptByLinearRegression,
        uuid: extra.uuid,
        username,
        license,
        version: 2,
        ide: 'vscode',
        ideVersion: vscode.version,
        platform: $features.PLATFORM,
        fileName,
        pluginVersion: context.extension.packageJSON.version,
        displayTextLength: extra.displayText.length,
        readCodeDuration: extra.telemetry.readCodeDuration(),
        requestCodeDuration: extra.telemetry.requestCodeDuration(),
        requestMultiline: extra.telemetry.measurements.requestMultiline,
        requestMultilineScore: extra.telemetry.measurements.requestMultilineScore,
        timestamp: Date.now(),
        contextualFilter: extra.telemetry.measurements.contextualFilter,
        prefixFeatures,
        suffixFeatures,
    };
    try {
        // TODO 不上报了，数据太多了
        // await axios.post(`${logHost}/logger/completion.log`, params, {
        //     headers: {
        //         'Content-Type': 'application/json',
        //     },
        // });
    }
    catch {
        // nothing
    }
}

export function telemetryShown(
    globalContext: Container,
    inlineCompletion: InlineCompletionItem,
    document?: vscode.TextDocument,
    position?: vscode.Position
) {
    const telemetryData = inlineCompletion.telemetry;
    telemetryData.markAsDisplayed();
    telemetry(globalContext, 'shown', inlineCompletion, document, position);
}
// TODO 记一下缓存的采纳率吧
export function telemetryAccepted(globalContext: Container, inlineCompletion: InlineCompletionItem) {
    const contextualFilterManager = globalContext.get(ContextualFilterManager);
    contextualFilterManager.previousLabel = 1;
    contextualFilterManager.previousLabelTimestamp = Date.now();
    telemetry(globalContext, 'accepted', inlineCompletion);
}

export function telemetryRejected(globalContext: Container, _telemetryData?: TelemetryData) {
    const contextualFilterManager = globalContext.get(ContextualFilterManager);
    contextualFilterManager.previousLabel = 0;
    contextualFilterManager.previousLabelTimestamp = Date.now();
    // telemetry(globalContext, 'rejected', inlineCompletion);
}

// TODO copilot
// mkCanceledResultTelemetry
// mkBasicResultTelemetry
// handleGhostTextResultTelemetry
