import {inject, injectable} from 'inversify';
import * as vscode from 'vscode';
import 'reflect-metadata';
import {VSCodeConfigProvider} from '@/services/ConfigProvider';
import {CONTEXT_SHOW_FEEDBACK} from '@/constants';
import {IL10nProvider} from './types';
import {L10n} from './L10n';

@injectable()
export class L10nProvider implements IL10nProvider {
    private disposables: vscode.Disposable[] = [];

    constructor(@inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider) {
        L10n.init(this.configProvider.getDisplayLanguage());
        this.onLanguageChange();

        this.disposables.push(
            configProvider.onDidChange(() => {
                const language = this.configProvider.getDisplayLanguage();
                if (language !== L10n.currentLanguage) {
                    L10n.updateLanguage(language);
                    this.onLanguageChange();
                }
            })
        );
    }

    private onLanguageChange() {
        const notEnglish = !L10n.isEnglish;
        vscode.commands.executeCommand('setContext', CONTEXT_SHOW_FEEDBACK, notEnglish);
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
