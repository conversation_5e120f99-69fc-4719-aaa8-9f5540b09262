type EnKeyType = typeof import('../../../l10n/bundle.l10n.json');
type ZhKeyType = typeof import('../../../l10n/bundle.l10n.zh-cn.json');

export type TranslationKey = keyof EnKeyType & keyof ZhKeyType;

export enum GlobalText {
    VERSION_UPDATE_TITLE = 'version.update.title',
    VERSION_UPDATE_INSTALL = 'version.update.install',
    VERSION_UPDATE_IGNORE = 'version.update.ignore',
    COMMON_GENERATE_ERROR = 'common.generate.error',
    COMMON_UNKNOWN_ERROR = 'common.unknown.error',
    COMMON_REQUEST_ERROR = 'common.request.error',
    COMMON_CODE = 'common.code',
    COMMON_FUNCTION = 'common.function',
    COMMON_USING = 'common.using',
    COMMON_WITHOUT = 'common.without',
    COMMON_COMMA = 'common.comma',
    COMMON_FRAMEWORK = 'common.framework',
    COMMON_RELATED = 'common.related',
    COMMON_PROMPT = 'common.prompt',
    COMMON_THINKING = 'common.thinking',
    COMMON_SEARCH_RESULT = 'common.searchResult',
    COMMON_AUTHORIZE = 'common.authorize',
    COMMON_DENY = 'common.deny',
    COMMON_NAVIGATE_TO_SETTING = 'common.navigateToSetting',
    COMMON_CUSTOMIZE_ERROR = 'common.customize.error',
    COMMON_PRIVATE_ERROR = 'common.private.error',
    LOG_LEVEL_PLACEHOLDER = 'log.level.placeholder',
    DO_NOT_SHOW_AGAIN = 'do.not.show.again',
}

export enum StatusBarText {
    NOT_SUPPORT = 'statusBar.not.support.text',
    ENABLE = 'statusBar.enable.text',
    NO_SUGGESTIONS = 'statusBar.nosuggestions',
    TITLE = 'statusBar.title',
}

export enum CompletionText {
    PANEL_ACCEPT = 'completion.panel.accept.text',
    PANEL_ACCEPT_TOOLTIP = 'completion.panel.accept.tooltip',
    PANEL_CODELENS_DISABLED = 'completion.panel.codelens.disabled.text',
    PANEL_TITLE = 'completion.panel.title',
    PANEL_EMPTY = 'completion.panel.empty.text',
    PANEL_HIDDEN = 'completion.panel.hidden.text',
    PANEL_SYNTHESIZING = 'completion.panel.synthesizing.text',
    DECORATION_BASIC = 'completion.decoration.basic',
    DECORATION_WITH_WIDGET = 'completion.decoration.widget.text',
    DECORATION_ACCEPT_LINE = 'completion.decoration.acceptLine.text',
}

export enum Nl2codeProviderText {
    EMPTY_TEXT = 'nl2code.empty.text',
    NO_SELECTION = 'nl2code.no.selection.text',
}

export enum OptimizeProviderText {
    CODELENS_TITLE = 'optimize.codelens.title',
    CODELENS_TOOLTIP = 'optimize.codelens.tooltip',
    OPTIMIZE_ERROR = 'optimize.error',
    OPTIMIZE_CODE_PROMPT = 'optimize.code.prompt',
    NO_SELECTION = 'optimize.no.selection.text',
    OPTIMIZE_FUNCTION_PROMPT = 'optimize.function.prompt',
}

export enum CodeSelectionActionsProviderText {
    EXPLAIN_SELECTED_CODE = 'explain.codeActions.title',
    OPTIMIZE_SELECTED_CODE = 'optimize.codeActions.title',
    INLINECHAT_SELECTED_CODE = 'inlinechat.codeActions.title',
}

export enum ChatTrialProviderText {
    CODELENS_TITLE = 'chatTrial.codelens.title',
}

export enum InlineChatTrialProviderText {
    CODELENS_TITLE = 'inlinechat.codelens.title',
}

export enum ChatTipsProviderText {
    CHAT_TIPS_TITLE = 'chatTips.selectedCode.title',
    CHAT_TIPS_CODE_BLANK_ROW_TITLE = 'chatTips.blankRow.title',
}

export enum DecorationsText {
    GENERATING_TITLE = 'decorations.generating.title',
    LINE_COMMENT_CODE_TRIGGER_TITLE = 'decorations.lineComent.trigger.title',
}

export enum CommentProviderText {
    PROMPT = 'comment.prompt',
    CODELENS_TITLE = 'comment.codelens.title',
    CODELENS_TOOLTIP = 'comment.codelens.tooltip',
    INLINECHAT_TITLE = 'comment.inlinechat.title',
    GENERATE_ERROR = 'comment.generate.error',
}

export enum DocstringProviderText {
    PROMPT = 'docstring.prompt',
    CODELENS_TITLE = 'docstring.codelens.title',
    CODELENS_TOOLTIP = 'docstring.codelens.tooltip',
    INLINECHAT_TITLE = 'docstring.inlinechat.title',
    GENERATE_ERROR = 'docstring.generate.error',
}

export enum SplitProviderText {
    PROMPT = 'split.prompt',
    CODELENS_TITLE = 'split.codelens.title',
    CODELENS_TOOLTIP = 'split.codelens.tooltip',
    GENERATE_ERROR = 'split.generate.error',
}

export enum ExplainProviderText {
    CODELENS_TITLE = 'explain.codelens.title',
    CODELENS_TOOLTIP = 'explain.codelens.tooltip',
    GENERATE_ERROR = 'explain.generate.error',
    NO_SELECTION = 'explain.no.selection.text',
    PROMPT = 'explain.prompt',
}

export enum UnitTestProviderText {
    CODELENS_TITLE = 'unitTest.codelens.title',
    CODELENS_TOOLTIP = 'unitTest.codelens.tooltip',
    GENERATE_ERROR = 'unitTest.generate.error',
    ACCEPT_ERROR = 'unitTest.accept.error',
    PROMPT = 'unitTest.prompt',
}

export enum InlineLogProviderText {
    PROMPT = 'inlineLog.prompt',
    CODELENS_TITLE = 'inlineLog.codelens.title',
    CODELENS_TOOLTIP = 'inlineLog.codelens.tooltip',
    GENERATE_ERROR = 'inlineLog.generate.error',
}

export enum DiffProviderText {
    TITLE = 'diff.title',
    ACCEPT = 'diff.accept',
    REJECT = 'diff.reject',
    OPEN_DUAL_SCREEN_DIFF = 'diff.dualScreenDiff.open',
}

export enum ChatProviderText {
    WEBVIEW_TITLE = 'chat.webview.title',
    NO_INSERT_POSITION = 'chat.no.insertPosition.text',
}

export enum AcceptProviderText {
    PATH_ERROR = 'accept.path.error',
}

export enum QuickFixText {
    ACTION = 'quickFix.action.text',
    PROMPT = 'quickFix.prompt',
    PROMPT_SUFFIX = 'quickFix.prompt.suffix',
    GENERATE_ERROR = 'quickFix.generate.error',
    FIX_MAX_LEN = 'quickFix.fix.maxLen.text',
}

export enum ExtensionText {
    LOGIN_MESSAGE = 'login.message',
    LOGIN_ACTION_TEXT = 'login.action.text',
    LOGIN_ACTION_WITH_LICENSE = 'login.action.withLicense.text',
    LOGIN_DEVICE_ERROR = 'login.device.error',
    LOGIN_LOGGING_IN = 'login.loggingIn.text',
    LOGIN_SUCCESS = 'login.success',
    LOGIN_FAILED = 'login.failed',
    LOGIN_TOKEN_ERROR = 'login.token.error',
    LOGIN_KEY_ERROR = 'login.key.error',
    LOGIN_INTERNAL_USER = 'login.internalUser.message',
    LOGIN_INSTALL_INTERNAL = 'login.install.internal',
    LOGIN_AGREEMENT = 'login.agreement.message',
    RENEW_ACTION = 'renew.action.text',
    QUICK_PICK_PLACEHOLDER = 'command.quick.pick.placeholder',
}

export enum RegexHoverText {
    TOOLTIP = 'regexHover.tooltip',
    PROMPT = 'regexHover.prompt',
}

export enum EmbeddingsServiceText {
    MAXIMUM_FILES = 'embeddings.maximum.files.message',
    PROGRESS = 'embeddings.progress.message',
    PROGRESS_TOOLTIP = 'embeddings.progress.tooltip.message',
}

export enum OpenPlatformText {
    TITLE = 'openPlatform.title.text',
    WEBVIEW_TITLE = 'openPlatform.webview.title',
}

export enum AutoWorkText {
    MODAL_AUTH_TITLE = 'autoWork.auth.modal.title',
    INDEXING_MESSAGE = 'autoWork.indexing.message',
    CONNECTION_INTERRUPT = 'autoWork.connection.interrupt.message',
    INDEXING_REMINDER = 'autoWork.indexing.reminder.text',
    ERROR = 'autoWork.error',
    TASK_THOUGHT = 'autoWork.task.thought',
    TASK_ANSWER = 'autoWork.task.answer',
    AUTO_DEBUG = 'autoWork.autoDebug',
    AUTO_DEBUG_PROMPT = 'autoWork.autoDebug.prompt',
    AUTO_DEBUG_STAGE_THOUGHT = 'autoWork.autoDebug.thought',
    AUTO_DEBUG_STAGE_SEARCH = 'autoWork.autoDebug.search',
    AUTO_DEBUG_STAGE_ANSWER = 'autoWork.autoDebug.answer',
}

export enum ComatePlusText {
    PERMISSION_WORKSPACE = 'comatePlus.permission.workspace.text',
    PERMISSION_DISK = 'comatePlus.permission.disk.text',
    PERMISSION_USER = 'comatePlus.permission.user.text',
    AUTH_MESSAGE = 'comatePlus.auth.message',
    USER_INFO_ERROR = 'comatePlus.userInfo.error',
    CLEAR_PERMISSION = 'comatePlus.clear.permission',
    SPLIT_ERROR = 'comatePlus.split.error',
    NO_SELECTION = 'comatePlus.noSelection',
    NOT_A_FUNCTION = 'comatePlus.not.function.text',
}

export enum InlineChatText {
    INLINECHAT_GENERATE = 'inlinechat.placeholder.generate',
    INLINECHAT_SELECTED = 'inlinechat.placeholder.selected',
    INLINECHAT_UNSELECTED = 'inlinechat.placeholder.unselected',
    INLINECHAT_TITLE = 'inlinechat.title',
    INLINCHAT_ERROR = 'inlinechat.placeholder.error',
}

export enum MessageGenerateText {
    GENERATE_ERROR = 'messageGenerate.generate.error',
    NO_DIFF_ERROR = 'messageGenerate.noDiff.error',
    GET_DIFF_ERROR = 'messageGenerate.getDiff.error',
    EMPTY_CONTENT_ERROR = 'messageGenerate.emptyContent.error',
}
