import {IllegalFileType} from '@shared/composer';

export interface Position {
    line: number;
    column: number;
}

type AutoDebugCodeChunkType = 'directoryTree' | 'file' | 'definition' | 'symbolsInStructOrPackage';

export type CodeChunkType =
    | 'embedding'
    | 'definition'
    | 'reference'
    | 'implements'
    | 'didopen'
    | 'documentsymbol'
    | 'keyword' // 关键字匹配
    | 'fullContent' // 全文
    | 'architecture' // 代码架构
    | 'autoDebug'
    | 'CURRENT_FILE'
    | 'selection'
    | 'terminal'
    | AutoDebugCodeChunkType
    | IllegalFileType;

export interface CodeChunk {
    repo: string;
    type: CodeChunkType;
    path: string; // 当 type 为 embedding 时，path 为绝对路径，其余情况为工作区相对路径
    content: string; // 上下文信息
    commentContent?: string; // 代码解释
    contentStart: Position;
    contentEnd: Position;
    distance?: number;
    symbolName?: string;
    symbolType?: string;
    selection?: {
        contentStart: Position;
        contentEnd: Position;
    };
}

export interface KnowledgeChunk {
    id: string;
    content: string; // 文档内容
    url: string; // 原文地址
    title: string; // 原文标题
}

export interface WebChunk {
    id: number; // 网页id，顺序id用于展示角标
    title: string; // 网页标题
    url: string;
}

export enum Editor {
    VSCODE = 'vscode',
    CURSOR = 'cursor',
    COMATE = 'comate',
}

export interface EditorEnvironment {
    userDataPath: string;
    extensionPath: string;
}
