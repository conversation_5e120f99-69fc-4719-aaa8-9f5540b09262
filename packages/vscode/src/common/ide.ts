import * as vscode from 'vscode';
import {SWAN_APP_NAME} from '../constants';

export const getIdeName = () => {
    const appName = vscode.env.appName;
    if ($features.ENTERPRISE_VERSION && $features.ENTERPRISE_VERSION === 'gitee') {
        return 'vscode-gitee';
    }
    switch (appName) {
        case 'Comate':
        case 'comate':
        case 'Comate Dev':
        case 'comate dev':
            return 'comate';
        case 'iCoding':
            return 'vscode-iCoding';
        case SWAN_APP_NAME:
            return 'vscode-swan';
        default:
            return 'vscode-local';
    }
};

export const isComateIDE = $features.IDE === 'ide';

// export const isComateIDE = (
//     $features.ENVIRONMENT === 'development'
//         ? $features.IDE === 'ide'
//         : getIdeName() === 'comate'
// );
