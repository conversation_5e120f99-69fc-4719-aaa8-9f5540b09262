/* eslint-disable complexity */
/* eslint-disable max-depth */
import zlib from 'zlib';
import qs from 'qs';
import {CancellationToken} from 'vscode-languageserver';
import {noop} from 'lodash';
import consoleLogger from '@/common/consoleLogger';
import {hideSensitive} from '../utils/common';
import {tradeCancelToken} from './common';
import {
    GenerateCodeOptions,
    GenerateTrackUuidParams,
    Response,
    axiosInstance,
    generateCode,
} from '.';

export interface PredictNextEditLineParams {
    // agent 自己生成的用于追踪的 id
    uuid: string;
    username: string;
    repo: string;
    path: string;
    // 1-based indexing
    row: number;
    // 1-based indexing
    col: number;
    // 代码片段
    content: string;
    // DiffBlock[] JSON 序列化后的字符串
    diffList: string;
}

export interface PredictNextEditLineResult {
    // 透传回来的 uuid
    uuid: string;
    row: number;
    // 透传回来的 path
    path: string;
}

export async function apiPredictNextEditLine(params: PredictNextEditLineParams, cancelToken?: CancellationToken) {
    const axiosCancelToken = tradeCancelToken(cancelToken);
    const query = qs.stringify(hideSensitive(params, ['content']));
    const result = await axiosInstance.post<Response<PredictNextEditLineResult>>(
        '/generate/position',
        query,
        {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            cancelToken: axiosCancelToken?.token,
        }
    );
    return result;
}

export interface RewriteCodeBlockParams extends GenerateCodeOptions {
    // ProgrammingContextElement[] JSON 序列化后的字符串
    diffList: string;
    actionType: ActionType;
    triggerSource: TriggerSource;
    // 1-based indexing
    rewriteSpecificStartRow: number;
    // 1-based indexing
    rewriteSpecificEndRow: number;
    // 仅采纳光标预测跳转结果后的触发传值，目的是为了让服务端能够区分该种场景下走改写模型，而非续写模型
    isFromJump?: boolean;
}

export interface RewriteBaseResult {
    uuid: string;
    // 1-based indexing
    startRow: number;
    // 1-based indexing
    endRow: number;
    generatedContent: string;
    path: string;
    score: number;
}

export interface RewriteCodeBlockResult extends RewriteBaseResult {
    modiType: 'completion' | 'rewrite';
    // 续写时为续写替换范围，改写时 range[0] 表示改写的开始行，range[2] 表示改写的结束行
    range: [number, number, number, number];
}

export interface CursorPredictionResult {
    status: string;
    message: string;
    uuid: string;
    nextEditLine: number;
    path: string;
    sseType: string;
}

export async function apiRewriteAndJump(
    params: RewriteCodeBlockParams,
    timeout?: number,
    cancelToken?: CancellationToken
): Promise<[Promise<RewriteCodeBlockResult>, Promise<CursorPredictionResult>]> {
    const axiosCancelToken = tradeCancelToken(cancelToken);
    const query = JSON.stringify(hideSensitive(params, ['content', 'neighborSnippetList']));
    const compressed = zlib.gzipSync(Buffer.from(query));

    const response = await axiosInstance.post(
        '/v2/generate/rewriteWithJump',
        compressed,
        {
            // baseURL: 'http://comate-test.now.baidu-int.com/api',
            headers: {
                'Content-Type': 'application/json',
                'Content-Encoding': 'gzip',
                Accept: 'text/event-stream',
            },
            responseType: 'stream',
            cancelToken: axiosCancelToken?.token,
            timeout,
        }
    );

    const stream = response.data;

    let resolveRewrite = noop;
    let resolveCursorPrediction = noop;

    const rewritePromise = new Promise<RewriteCodeBlockResult>(resolve => {
        resolveRewrite = resolve;
    });

    const cursorPredictionPromise = new Promise<CursorPredictionResult>(resolve => {
        resolveCursorPrediction = resolve;
    });

    // 返回两个promise，为了能立马拿到第一个响应
    (async () => {
        let buffer = '';
        let dataCount = 0;

        try {
            for await (const chunk of stream) {
                buffer += chunk.toString();

                const lines = buffer.split('\n\n');
                buffer = lines.pop() || '';

                for (const line of lines) {
                    if (line.startsWith(':Connection')) {
                        continue;
                    }
                    else if (line.startsWith('data:')) {
                        const data = line.slice(5);
                        if (data === '[DONE]') {
                            return;
                        }

                        try {
                            const parsedData = JSON.parse(data);
                            dataCount++;

                            if (dataCount === 1) {
                                consoleLogger.debug('Rewrite API first res', parsedData);
                                resolveRewrite(parsedData);
                            }
                            else if (dataCount === 2) {
                                consoleLogger.debug('Rewrite API second res', parsedData);
                                resolveCursorPrediction(parsedData);
                                // 获取到第二个数据后关闭流
                                stream.destroy();
                                return;
                            }
                        }
                        catch (error) {
                            throw new Error(`解析SSE数据失败: ${error}`);
                        }
                    }
                }
            }
        }
        catch (error) {
            if (dataCount === 0) {
                resolveRewrite(null);
                resolveCursorPrediction(null);
            }
            else if (dataCount === 1) {
                resolveCursorPrediction(null);
            }
        }
    })();

    return [rewritePromise, cursorPredictionPromise];
}

export enum ActionType {
    Rewrite = 'REWRITE',
    PositionPredict = 'POSITION_PREDICT',
}

export enum TriggerSource {
    MethodParameterAdded = 'METHOD_PARAMETER_ADDED', // 方法参数添加 调模型改写
    MethodParameterRemoved = 'METHOD_PARAMETER_REMOVED', // 方法参数删除 调模型改写
    MethodParameterTypeChanged = 'METHOD_PARAMETER_TYPE_CHANGED', // 方法参数类型修改 调模型改写
    MethodParameterNameChanged = 'METHOD_PARAMETER_NAME_CHANGED', // 方法参数名修改 插件端改写
    MethodReturnChanged = 'METHOD_RETURN_CHANGED', // 方法返回值修改 调模型改写
    MethodNameChanged = 'METHOD_NAME_CHANGED', // 方法名修改 插件端改写
    MultiNameChanged = 'MULTI_NAME_CHANGED', // 多名字修改 调模型改写
    SimilarCodeRewrite = 'SIMILAR_CODE_REWRITE', // 相似代码改写 调模型改写
    ReferenceUpdate = 'REFERENCE_UPDATE', // 引用更新 调模型改写（如函数定义变更，需要对引用该函数的位置进行改写）
    DiagnosticInfo = 'DIAGNOSTIC_INFO', // 光标附近有诊断信息触发的改写
    DiagnosticInfoKeyboardMove = 'DIAGNOSTIC_INFO_KEYBOARD_MOVE', // 光标键盘移动到诊断信息上触发的改写
    EditCode = 'EDIT_CODE', // 编辑代码触发改写
}

export interface RewriteRecord extends GenerateTrackUuidParams {
    uuid: string;
    actionType: ActionType.Rewrite;
    triggerSource: TriggerSource;
    rewritePreCode: string; // 前缀代码片段
    rewriteMidCode: string; // 中间代码片段
    rewriteSufCode: string; // 后缀代码片段
    rewriteSpecificStartRow: string; // 改写特定行，1-based indexing
    rewriteSpecificEndRow: string; // 改写特定行，1-based indexing
}

export interface PositionPredictRecord extends GenerateTrackUuidParams {
    uuid: string;
    actionType: ActionType.PositionPredict;
    triggerSource: TriggerSource;
}

type PersistRecordParams = RewriteRecord | PositionPredictRecord;

// 自生成的 uuid，上报信息给 gateway 记录
export async function apiPersistRecord(params: PersistRecordParams) {
    const result = await generateCode(params);
    return result;
}
