/* eslint-disable max-lines */
/* eslint-disable no-void */
import {IncomingMessage} from 'http';
import zlib from 'zlib';
import {basename} from 'path';
import {CancelToken} from 'axios';
import pMemoize from 'p-memoize';
import {UserFeedback, AgentConfig, AgentApplyStatus} from '@shared/protocols';
import * as vscode from 'vscode';
import qs from 'qs';
import FormData from 'form-data';
import {LicenseFullDetail} from '@comate/plugin-shared-internals';
import {getExtensionContextAsync} from '@/utils/extensionContext';
import {hideSensitive} from '@/utils/common';
import {showForbiddenMessage} from '@/common/showForbiddenMessage';
import {codelensWhiteList} from '@/utils/codelens';
import {info} from '../common/outputChannel';
import {createAxios, withProxy} from './common';

const BASE_URL_MAPPING: Record<string, string> = {
    internal: 'https://comate.baidu-int.com/api',
    'internal-test': 'http://10.11.58.93:8080',
    saas: 'https://comate.baidu.com/api',
    'saas-test': 'https://comate-cop.now.baidu-int.com/api',
    poc: 'https://comate.baidu.com/api',
};

export const axiosInstance = withProxy(createAxios({
    baseURL: BASE_URL_MAPPING[$features.PLATFORM + ($features.ENVIRONMENT === 'test' ? '-test' : '')],
}));

info(`Main api baseURL: ${axiosInstance.defaults.baseURL}`);

export interface Response<T> {
    status: string;
    message?: string;
    data?: T;
}

// TODO: 整理下接口
export interface InlineSuggestionModeItem {
    displayName: string;
    requestDelayMs: number;
}

export type InlineSuggestionModeType = 'fast' | 'balance' | 'accurate' | 'extremeAccurate' | 'extremeFast';

export type InlineSuggestionModeMap = Record<InlineSuggestionModeType, InlineSuggestionModeItem>;

export interface UnitTestConfig {
    java: {
        mockFrame: string;
        unitTestFrame: string;
    };
    go: {
        mockFrame: string;
        unitTestFrame: string;
    };
    javascript: {
        mockFrame: string;
        unitTestFrame: string;
    };
    cpp: {
        mockFrame: string;
        unitTestFrame: string;
    };
    python: {
        mockFrame: string;
        unitTestFrame: string;
    };
}

export type LinePreferMode = 'auto' | 'singleLine' | 'multiLine';

export interface ZuluConfig {
    terminalAutoExecution: string;
    terminalAutoExecutionDenyList: string[];
    terminalAutoExecutionAllowList: string[];
}

export interface UserConfig {
    langSuggestion: Record<string, boolean>;
    delayMilliSecondsMode: string;
    delayMilliSecondsModeMap: InlineSuggestionModeMap;
    enableInlineUnitTest: boolean;
    enableInlineCodeExplain: boolean;
    enableInlineCodeToComment: boolean;
    /** 是否允许 API 续写 */
    enableGenAPIInvokeCompletion: boolean;
    /** 是否展示函数的 添加日志 功能 CodeLense */
    enableInlineLog: boolean;
    enableSecurityEnhance: boolean;
    linePreferMode: LinePreferMode;
    enableCommentEnhancement: boolean;
    unitTestConfig?: UnitTestConfig;
    isInsider?: boolean;
    isPreferStream?: boolean;
    /** 开放平台和autowork的开关 */
    isEnableAutoworkAndPlus?: boolean;
    /** 是否开启基于代码库的对话增强 */
    enableCodebaseEnhancedContext?: boolean;
    // NOTE: 虽然叫 ChatPanel，但实际上作用是触发 InlineChat，考虑到和服务端的接口保持一致，暂时不做修改
    /** 是否开启 codelens 里开始对话的快捷按钮 */
    enableChatPanelShortcut?: boolean;
    displayLanguage?: string;
    /** 混合云用户服务地址 */
    customizedUrl?: string;
    /** 私有域服务地址 */
    privateService?: string;
    /** 智能粘贴功能开关 */
    enableAutoPaste?: boolean;
    /** 智能体功能开关 */
    enableIntelligenceAgent?: AgentConfig;
    /** 是否开启安全智能体 */
    enableSecurityIntelligence: boolean;
    /** 智能体申请状态 */
    applyStatus: AgentApplyStatus;
    /** 是否在编辑区生成函数注释 */
    functionDocstringOutputInEditor: boolean;
    /** 是否在编辑区生成行间注释 */
    inlineCommentOutputInEditor: boolean;
    /** 用户选择的模型版本 */
    modelId?: string;
    /** 是否开启续写智能体的诊断信息感知修复 */
    enableDiagnosticInfo: boolean;
    /** zulu的相关配置 */
    zuluConfig?: ZuluConfig;
}

const USER_CONFIG_URL = '/config/';
export async function getUserConfig(licenseOrUserName: string) {
    const res = await axiosInstance.get<Response<UserConfig>>(
        USER_CONFIG_URL + encodeURIComponent(licenseOrUserName)
    );
    return res.data.data;
}

export const memoizedGetUserConfig = pMemoize(getUserConfig);

export async function updateUserConfig(
    licenseOrUserName: string,
    config: Partial<Omit<UserConfig, 'delayMilliSecondsModeMap'>>
) {
    const data = JSON.stringify(config);
    const res = await axiosInstance.post<Response<void>>(
        USER_CONFIG_URL + encodeURIComponent(licenseOrUserName),
        data,
        {
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
    return res;
}

export async function getSuffixSupport() {
    try {
        const res = await axiosInstance.get<Response<{[key: string]: string[]}>>(
            '/lang/supported'
        );
        return res?.data?.data;
    }
    catch (e) {
        return undefined;
    }
}

export async function deleteCode(uuid: string) {
    const res = await axiosInstance.delete<Response<void>>(
        '/generate/cancel/' + uuid
    );
    return res;
}

/**
 * 停止生成任务
 *
 * @param requestId 请求ID
 * @returns 返回停止生成任务的响应
 */
export async function stopGenerate(requestId: string) {
    const res = await axiosInstance.delete<Response<void>>(
        '/generate/stop/' + requestId
    );
    return res;
}

export interface GenerateCodeOptions {
    username: string;
    repo: string;
    path: string;
    content: string;
    row: string;
    col: string;
    ide: string;
    pluginVersion: string;
    // 客户端生成的请求 UUID，可用于停止生成等场景
    requestId?: string;
    device?: string | undefined;
    key?: string | undefined;
    [key: string]: Record<string, any> | string | boolean | number | undefined;
}

export interface RawGenerateCode {
    uuid: string;
    content: string;
    range: [number, number, number, number];
    score: number;
    docString?: string;
    chatId?: string;
    conversationId?: number;
    reasoningSummary?: string;
    reasoningEnd?: boolean;
}

export type GenerateCodeResponse = Response<Partial<RawGenerateCode>>;

export type BatchCodeResponse = Response<Array<Partial<RawGenerateCode>>>;

async function apiPostGenerate(params: GenerateCodeOptions, cancelToken?: CancelToken) {
    const ideVersion = vscode.version;
    const deductedParams = hideSensitive({
        ...params,
        ideVersion,
    }, ['content', 'neighborSnippetList']);
    const query = qs.stringify(deductedParams);
    const result = await axiosInstance.post<GenerateCodeResponse>(
        '/generate',
        query,
        {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            cancelToken,
        }
    );

    return result;
}

async function apiPostGzipGenerate(params: GenerateCodeOptions, cancelToken?: CancelToken) {
    const ideVersion = vscode.version;
    const deductedParams = hideSensitive({
        ...params,
        ideVersion,
    }, ['content', 'neighborSnippetList']);
    try {
        const query = JSON.stringify(deductedParams);
        const buffer = Buffer.from(query);

        // 使用zlib的gzip方法压缩数据
        const compressed = zlib.gzipSync(buffer);

        const result = await axiosInstance.post<GenerateCodeResponse>(
            '/v2/generate',
            compressed,
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Content-Encoding': 'gzip',
                },
                cancelToken,
            }
        );

        return result;
    }
    catch (e: any) {
        return apiPostGenerate(params, cancelToken);
    }
}

export async function generateCode(params: GenerateCodeOptions, cancelToken?: CancelToken) {
    const result = await apiPostGzipGenerate(params, cancelToken);
    showForbiddenMessage(result.data);
    return result;
}

export interface GenerateTrackUuidParams extends GenerateCodeOptions {
    generatedContent: string; // 插件返回内容
    model?: string; // 插件名（每个插件唯一）
    function?: string; // 使用的插件的哪个功能
    shown: boolean; // 是否展示给用户看到了，如果全都展示，可固定传true
    multiline?: boolean; // 生成结果是否是多行
}

export async function generateTrackUuid(params: GenerateTrackUuidParams) {
    const result = await apiPostGenerate(params);
    return result;
}

export async function generateBatchCode(params: GenerateCodeOptions) {
    const deductedParams = hideSensitive(params, ['content', 'neighborSnippetList']);
    const query = qs.stringify(deductedParams);
    const result = await axiosInstance.post<BatchCodeResponse>(
        '/generate/batch',
        query,
        {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
        }
    );
    return result;
}

interface MultiAcceptData {
    path: string;
    row: string;
    col: string;
    generatedContent: string;
    accepted: boolean;
}

interface AcceptCodeOptions {
    uuid: string;
    accepted: boolean;
    /** 没采纳时用户的实际输入，目前默认都传一个空字符串 */
    content: string;
    /** 用于部分采纳时回传采纳部分代码内容 */
    generatedContent?: string;
    /** 改写上报新增字段，描述采纳的（可能是部分）代码内容 */
    acceptedContent?: string;
    persistent15s?: boolean;
    persistent30s?: boolean;
    persistent120s?: boolean;
    persistent300s?: boolean;
    persistent600s?: boolean;
    /** 主要用于Comate+追踪采纳了的代码 */
    acceptedBlocks?: string;
    acceptanceInfo?: AcceptanceInfo;
    /** 用于多文件，支持采纳放弃的场景（智能体） */
    multiSuggestions?: {fileContent: MultiAcceptData[]};
}

interface AcceptanceInfo {
    row: number; // 插入的行号
    col: number; // 插入的列号
    originContent: string; // 原始代码
    acceptanceContent: string; // 被采纳的代码块
    acceptanceType: ACCEPTANCE_TYPE; // 采纳类型
}

export enum ACCEPTANCE_TYPE {
    COPY = 'COPY',
    NEWFILE = 'NEWFILE',
    INSERTINTOTERMINAL = 'INSERTINTOTERMINAL',
    REPLACETOFILE = 'REPLACETOFILE',
    INSERTTOFILE = 'INSERTTOFILE',
    INSERT = 'INSERT', // accept和其他默认情况
    REPLACE = 'REPLACE',
}

export async function acceptCode(params: AcceptCodeOptions) {
    const deductedParams = hideSensitive(params, ['content', 'generatedContent']);
    if (deductedParams.acceptanceInfo) {
        deductedParams.acceptanceInfo = JSON.stringify(deductedParams.acceptanceInfo);
    }
    const query = qs.stringify(deductedParams);
    const res = await axiosInstance.put<Response<void>>(
        '/generate/acceptance',
        query,
        {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
        }
    );
    return res;
}

/** 具体定义参考：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/-T00tPbSNX/NuTeHtulhtwuII */
export type ShownTypeName =
    'completion' | 'insertOnly' | 'deleteOnly' | 'inlineChange' | 'floatBox' | 'sideBySide' | 'noChange';

interface ShowCodeOptions {
    shownMilliSeconds?: number;
    shownStepByStepTimes?: number;
    shownTypeName?: ShownTypeName;
}

export async function showCode(uuid: string, params?: ShowCodeOptions) {
    const query = qs.stringify(params);
    const res = await axiosInstance.put<Response<void>>(
        `/generate/shown/${uuid}`,
        query
    );
    return res;
}

export async function generateStreamCode(params: GenerateCodeOptions, cancelToken?: CancelToken) {
    const ideVersion = vscode.version;

    const deductedParams = hideSensitive({
        ...params,
        ideVersion,
        rawContent: undefined,
    }, ['content', 'neighborSnippetList']);

    const query = qs.stringify(deductedParams);

    const result = await axiosInstance.post<IncomingMessage>(
        '/generate/stream',
        query,
        {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            responseType: 'stream',
            cancelToken,
        }
    );

    return result;
}

// 按照codelens统一请求方式调用接口
export async function generateCodelensStreamCode(params: GenerateCodeOptions, cancelToken?: CancelToken) {
    if (typeof params.function !== 'string' || !codelensWhiteList.includes(params.function)) {
        return generateStreamCode(params, cancelToken);
    }

    const ideVersion = vscode.version;
    const deductedParams = hideSensitive({
        ...params,
        ideVersion,
    }, ['rawContent', 'content', 'neighborSnippetList']);

    deductedParams.userInputHistory = deductedParams.userInputHistory ?? JSON.stringify([]);
    deductedParams.codelens = {
        repoId: deductedParams.repoId,
        name: basename(deductedParams.path),
        conversationId: deductedParams.conversationId,
        fileContent: '',
        logModules: [],
        logCodes: [],
        methodArgumentSpecificationList: [],
        methodCalledStackList: [],
        methodType: '',
        projectInfo: '',
        ...deductedParams.codelens,
    };

    if (deductedParams.function === 'CODE_TUNING') {
        deductedParams.codelens.fileContent = deductedParams.rawContent;
    }

    deductedParams.rawContent = '';

    const result = await axiosInstance.post<IncomingMessage>(
        '/generate/codelens/stream',
        deductedParams,
        {
            headers: {
                'Content-Type': 'application/json',
            },
            responseType: 'stream',
            cancelToken,
        }
    );
    return result;
}

export interface OneBasedPosition {
    line: number;
    character: number;
}

export interface OneBasedRange {
    start: OneBasedPosition;
    end: OneBasedPosition;
}

interface ModifyCodeOptions {
    uuid: string;
    capturedActualContext?: string;
    chatId?: string;
    isLike?: '0' | '1' | '2';
    userFeedback?: UserFeedback;
    persistent15s?: boolean;
    persistent30s?: boolean;
    persistent120s?: boolean;
    persistent300s?: boolean;
    persistent600s?: boolean;
    mergeResult?: {
        replaceRange: OneBasedRange;
        replaceText: string;
    };
}

interface Params {
    uuid: string;
    isLike?: '0' | '1' | '2';
    userFeedback?: UserFeedback;
}
interface Cache {
    [uuid: string]: Params | undefined;
}
// HACK: 注意，这里的存储空间会持续占用，虽然不大
const previousFeedbackData: Cache = {};
function mergePreviousData(params: Params): Params {
    try {
        const newParams = {...previousFeedbackData[params.uuid], ...params};
        previousFeedbackData[params.uuid] = newParams;
        return newParams;
    }
    catch (err) {
        console.error(err);
        return params;
    }
}

export async function modifyCode(params: ModifyCodeOptions) {
    const rawDeductedParams = hideSensitive(params, ['capturedActualContext']);
    // NOTE: 点赞点踩和反馈是独立操作，会相互覆盖，因此需要记录之前操作并合并
    const deductedParams = mergePreviousData(rawDeductedParams as any);
    const formData = new FormData();
    for (const [key, value] of Object.entries(deductedParams)) {
        if (typeof value === 'object') {
            formData.append(key, JSON.stringify(value));
        }
        else {
            formData.append(key, value);
        }
    }
    const res = await axiosInstance.put<Response<void>>(
        '/generate/modify',
        formData,
        {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
        }
    );
    return res;
}

interface UpdateContextOptions {
    uuid: string;
    /** 一段时间后的原文 */
    capturedActualAllContext: string;
}

export async function uploadContext(params: UpdateContextOptions) {
    const deductedParams = hideSensitive(params, ['capturedActualAllContext']);
    const query = qs.stringify(deductedParams);
    const res = await axiosInstance.post<boolean>(
        '/snippet/upload',
        query,
        {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            custom: {
                disableLogging: true,
            },
        }
    );
    return res;
}

interface UserAcceptanceAction {
    username?: string;
    version?: string;
    session?: string;
    category: string;
    action: string;
    operation?: {
        [key: string]: any;
        result?: string;
    };
    ide?: string; // 比如 vscode 、idea
    ideVersion?: string;
    payload?: Record<string, any>;
}

export async function track(params: UserAcceptanceAction) {
    const context = await getExtensionContextAsync();
    const version = context.extension.packageJSON.version;
    const session = vscode.env.machineId;
    const data = JSON.stringify({...params, version, session});

    const result = await axiosInstance.put<UserAcceptanceAction>(
        '/stat/efficiency/save',
        data,
        {
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );

    return result;
}

interface LicenseDetail {
    validDays: number;
    message?: string | null;
    renewUrl: string;
}

// 检查license到期时间
export async function getLicenseDetail(key: string) {
    const res = await axiosInstance.get<Response<LicenseDetail>>(
        '/key/expiring/detail/' + key
    );
    return res.data;
}

// 检查license是否可以使用高级功能
export async function licenseValidate(key: string) {
    const res = await axiosInstance.get<Response<boolean>>(
        '/key/valid/' + key
    );
    return res.data;
}

export interface UserInfoWithLicense {
    uid: string;
    username: string;
    name: string;
    keyType: 'NONE' | 'IAM' | 'PASSORT' | 'GITEE' | 'GITHUB';
}

// 不需要和内网统一，这个接口加在了开放平台里，基础能力是不需要的
export async function getUserInfoWithLicense(license: string): Promise<UserInfoWithLicense> {
    const res = await axiosInstance.get(
        '/v2/api/comateplus/users',
        {
            headers: {
                'X-Username': license,
            },
        }
    );
    return res.data?.data;
}


export async function getTokenByClientIdInternal(clientId: string) {
    const res = await axiosInstance.get<Response<string | null>>(
        '/api/comateplus/users/token?clientId=' + clientId
    );
    return res.data;
}

interface InternalLicenseInfo {
    name: string;
    username: string;
    userAvatarUrl?: string;
}

export async function getLicenseByTokenInternal(clientId: string, token: string) {
    const res = await axiosInstance.get<Response<InternalLicenseInfo | null>>(
        '/api/comateplus/users/get?clientId=' + clientId,
        {
            headers: {
                token,
            },
        }
    );
    return res.data;
}

export async function getTokenByClientId(clientId: string) {
    const res = await axiosInstance.get<Response<string | null>>(
        '/client/generate/token?clientId=' + clientId
    );
    return res.data;
}

export interface LicenseInfo {
    displayName: string;
    key: string;
    userAvatarUrl?: string;
    isInternalUser?: boolean;
}

export async function getLicenseByToken(clientId: string, token: string) {
    const res = await axiosInstance.get<Response<LicenseInfo | null>>(
        '/client/key?clientId=' + clientId,
        {
            headers: {
                token,
            },
        }
    );
    return res.data;
}

export async function removePreviousBinding(clientId: string) {
    const res = await axiosInstance.get<Response<boolean>>('/client/logout?clientId=' + clientId);
    return res.data;
}

interface LogParams {
    type: 'event' | 'error' | 'warn';
    platform: string;
    common: {
        license: string;
        username: string;
        plugin: string;
        version: string;
        ideVersion: string;
        os: {
            platform: string;
            arch: string;
            release: string;
        };
    };
    event: {
        content: string;
    };
}

export async function uploadLog(params: LogParams) {
    return axiosInstance.post('/clientLog/upload', params);
}

// 高级功能使用记一次，only for saas
export async function advancedFeatureUsageCount(license: string) {
    return axiosInstance.post('/key/used/' + license);
}

export interface ResponseBase<T> {
    code: number;
    data: T;
    message?: string;
    traceId: string;
}

export interface ClientGlobalConfig {
    inlineCodeCompleteConfig: Array<{
        languageId: string;
        prefixRegexStr: string;
        suffixRegexStr: string;
        mode: number;
    }>;
    commonConfig: {
        diffTrackerRateSeconds: number;
        /** 感知光标移动，由诊断信息触发改写的 debounce 时间 */
        rewriteByDiagnosticInfoDelayMillSeconds: number;
    };
}

export interface GlobalConfig {
    endpoint: string;
    serverHost: string;
    system: {
        enableUserGuide: boolean;
        enableAgent: boolean;
        enableFeedback: boolean;
        enableJoiningInfoFlowGroup: boolean;
        env: string;
    };
    completion: {
        enableCache: boolean;
        enableRewrite: boolean;
        inlineCodeCompleteConfig: null;
        modelRewriteDelayMillSeconds: null;
        clientRewriteDelayMillSeconds: null;
        rewriteByDiagnosticInfoDelayMillSeconds: null;
        onlyUseRewriteResult: null;
    };
    autowork: {
        endpoint: string;
        enableIntentRecognition: boolean;
        enableZulu: boolean;
        enableAutoApplyFunction: boolean; // 智能采纳
        enableInlineChat: boolean;
    };
    comateplus: {
        enabled: boolean;
        endpoint: string;
    };
    log: {
        logUploadUrl: string;
        performanceUploadUrl: string;
    };
    ut: {
        enableBatchGenerate: boolean;
        endpoint: string;
        generateUri: string;
        acceptUri: string;
        completionUri: string;
        batchUploadUri: string;
        enableUTAgent: null;
    };
    webSocket: {
        endpoint: string;
    };
    iapi: {
        enableInterfaceGeneration: boolean;
        interfaceEndpoint: string;
        trackEndpoint: string;
        searchEndpoint: string;
    };
    dao: {
        enableGenDao: boolean;
        enableGenSql: boolean;
        endpoint: string;
    };
    license: {
        endpoint: string;
        bceEndpoint: string;
        enableSwitchAccount: boolean;
    };
    notification: {
        enableAINative: boolean;
        enableBaiduUsingSaas: boolean;
        enablePocUsingSaas: boolean;
    };
    embedding: {
        enableSearchFromInternet: boolean;
        indexVersionCheckUrl: string;
        multiEmbeddingSearchUrl: string;
        embeddingSearchUrl: string;
        knowledgeWorkspacesUrl: string;
        embeddingIndexProgressUrl: string;
        autoworkConfigUrl: string;
        dependencyConfigUrl: string;
        dependencyParseUrl: string;
        multiEmbeddingSearchUrlForLineCommentSearchUrl: string;
    };
    codeGraph: {
        enabled: boolean;
        endpoint: string;
    };
    security: {
        enabled: boolean;
    };
    icafe: {
        enabled: boolean;
        endpoint: string;
        rankEndpoint: string;
    };
    metrics: null;
}

// 获取全局配置
export async function getClientGlobalConfig() {
    const res = await axiosInstance.get<Response<ClientGlobalConfig>>('/config/global/client');
    return res.data;
}

// 获取完整的全局配置
export async function getGlobalConfig() {
    const res = await axiosInstance.get<GlobalConfig>('/config/global', {
        timeout: 10000,
    });
    return res.data;
}

// 获取证书类型，比如免费个人版本，only for saas
export async function getLicenseFullDetail(license: string) {
    const res = await axiosInstance.get<ResponseBase<LicenseFullDetail>>('/key/type/' + license, {
        timeout: 5000, // 针对可能的网络问题
    });
    return res.data?.data;
}

// comate服务探活接口
export async function serviceHealthy() {
    const res = await axiosInstance.get<'ok'>('/ok');
    return res.data;
}

export interface RetentionRateData {
    acceptNewLineCount: string;
    acceptCharLength: string;
    totalNewLineCount: string;
    totalCharLength: string;
    language: string;
    source: string;
    uri: string;
    startTimestamp: string;
    endTimestamp: string;
    key: string;
    ideVersion: string;
    pluginVersion: string;
    ide: string;
}
// 提交代码生成占比分母，基于行变更，only for saas
export async function uploadLineChange(data: RetentionRateData[]) {
    const res = await axiosInstance.put('/commit/lines/realtime/upload', data, {
        headers: {
            'Content-Type': 'application/json',
        },
    });
    return res.data;
}

export interface GitChangeData {
    commitId: string;
    key: string;
    addLines: number;
    deleteLines: number;
    commitTime: number;
}
// 提交代码生成占比分母，基于git变更，only for saas
export async function uploadGitChange(data: GitChangeData[]) {
    const res = await axiosInstance.put('/commit/lines/upload', data, {
        headers: {
            'Content-Type': 'application/json',
        },
    });
    return res.data;
}

export interface ForwardToQianfanParams {
    username: string;
    pluginName: string;
    model: 'EB' | 'EB_4' | 'EB_8k' | 'EB_turbo' | 'EB_4_turbo';
    /** 使用插件的那个功能 */
    function?: string;
    /** 文件后缀，比如 .js */
    lang: string;
    /** SaaS 必传 */
    key?: string;
    /** 和千帆接口参数一致，比如会有 messages */
    [key: string]: any;
}

export interface QianfanStreamResponse {
    end: boolean;
    finishReason: string;
    id: string;
    result: string;
}

export interface QianfanStreamErrorResponse {
    status: 'FORBIDDEN';
    message: string;
    data: any;
}

/**
 * Comate 服务端会将这个请求直接转发给千帆模型服务
 * @param params
 * @param cancelToken
 * @returns
 */
export async function forwardToQianfan(params: ForwardToQianfanParams, cancelToken?: CancelToken) {
    const res = await axiosInstance.post('/v2/api/generate/stream', params, {
        headers: {
            'Content-Type': 'application/json',
        },
        responseType: 'stream',
        cancelToken,
    });
    return res.data;
}

export interface EditCode {
    name: 'edit_diff';
    /** 变更的文件 */
    path: string;
    /** 变更带来的 diff */
    content: string;
    /** YYYY-MM-DD HH:mm:ss */
    modified_time: string;
}

export interface RunTerminalCommand {
    name: 'run_cmd';
    /** 命令 */
    content: string;
    /** cwd 相对于当前工作区的路径，获取不到时为 null */
    path: string | null;
    /** 命令输出 */
    output: string;
    /** YYYY-MM-DD HH:mm:ss */
    modified_time: string;
}

export interface DiagnosticInfo {
    name: 'diagnostic_info';
    /** 诊断信息 */
    content: string;
    path: string;
    /** 诊断原数据 JSON 序列化的字符串 */
    output: string;
    modified_time: string;
}

export type ProgrammingContextElement =
    | EditCode
    | RunTerminalCommand
    | DiagnosticInfo;

export interface EditDiff {
    // 对应此刻续写的 uuid
    uuid: string;
    edit_diff: ProgrammingContextElement[];
}

export async function uploadEditDiff(params: EditDiff) {
    const res = await axiosInstance.post('/upload_edit_diff', params);
    return res.data;
}

interface ModelListParams {
    key: string;
    type: 'CHAT' | 'COMPLETION';
}

export interface ModelRes {
    modelId: string;
    modelName: string;
    displayName: string;
    isDefault: boolean;
}
export async function modelListApi(params: ModelListParams) {
    const res = await axiosInstance.get<Response<ModelRes[]>>('/v2/api/models/available', {params});
    return res.data;
}

/** 私有化功能，接入SQL获取表结构 */
export async function getSQLDDL(data: any) {
    const res = await axiosInstance.post('/sql/schema', data);
    return res.data;
}

interface ComateRelease {
    id: string;
    pluginType: 'VSCODE';
    fileName: string;
    pluginVersion: string;
    uploadTime: string;
    uploadUser: string;
    latestFlag: boolean;
    url: string;
    pluginText?: string;
}

interface ComateReleasesResponse {
    status: string;
    message: string;
    data: ComateRelease;
}

/** 私有化功能，从管理后台插件中心获取版本信息 */
export async function getLatestRelease() {
    const res = await axiosInstance.get<ComateReleasesResponse>('/console/plugin/latest?pluginType=VSCODE');
    if (res?.data?.status === 'OK') {
        return res.data.data;
    }
    throw new Error(res.data.message);
}

/** SaaS功能，从管理后台插件中心获取SaaS版本信息 */
export async function getSaaSPluginVersion() {
    const res = await axiosInstance.get<ComateReleasesResponse>('/plugin/getSuggestedPlugin?pluginType=VSCODE');
    if (res.data.status === 'OK') {
        return res.data.data;
    }
    else {
        return {
            pluginVersion: '',
            pluginText: '',
        };
    }
}
