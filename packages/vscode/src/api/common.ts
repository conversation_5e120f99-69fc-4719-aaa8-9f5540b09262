import {randomUUID} from 'crypto';
import axios, {AxiosInstance, CreateAxiosDefaults} from 'axios';
import * as vscode from 'vscode';
import {cloneDeep} from 'lodash';
import {CancellationToken} from 'vscode-languageserver-protocol';
import {
    ENVIRONMENT,
    INTERNAL_API_HOST,
    INTERNAL_TEST_API_HOST,
    SAAS_API_HOST,
    SAAS_TEST_API_HOST,
} from '@comate/plugin-shared-internals';
import {LogLevel, log, error as errorLog} from '@/common/outputChannel';
import {safeStringify} from '@/utils/common';
import {isInternal, isPoc} from '@/utils/features';
import {COMATE_CONFIG_PREFIX} from '@/constants';
import {L10n} from '@/common/L10nProvider/L10n';
import {getExtensionContextAsync} from '@/utils/extensionContext';
import {vscodeCommands} from '@/utils/vscodeComands';
import formatPrivateService from '@/utils/privateService';

export const withLogger = (axiosInstance: AxiosInstance, logLevel: LogLevel = LogLevel.Debug) => {
    axiosInstance.interceptors.request.use(config => {
        if (config.custom?.disableLogging) {
            return config;
        }
        const debugInfo = [];
        if (config.method) {
            debugInfo.push(config.method.toUpperCase());
        }
        if (config.url) {
            debugInfo.push(config.url);
        }
        if (typeof config.data === 'string') {
            debugInfo.push(config.data);
        }
        if (!config.headers['X-Trace-Id']) {
            const uuid = randomUUID();
            config.headers['X-Trace-Id'] = uuid;
            debugInfo.push(`traceId=${uuid}`);
        }
        else if (config.data instanceof URLSearchParams) {
            const requestData = safeStringify(Object.fromEntries(config.data));
            if (requestData) {
                debugInfo.push(requestData);
            }
        }
        log(logLevel, ...debugInfo);
        return config;
    }, error => {
        errorLog(`Request error: ${error?.message}${error?.config?.url ? ` for URL: ${error.config.url}` : ''}`);
        return Promise.reject(error);
    });

    axiosInstance.interceptors.response.use(response => {
        if (response.config.custom?.disableLogging) {
            return response;
        }
        const debugInfo = [`API RES: from (${response.config.url})`];
        const responseData = safeStringify(response.data);
        if (response.headers['content-type'] === 'application/json' && responseData) {
            debugInfo.push(responseData);
        }
        log(logLevel, ...debugInfo);
        return response;
    }, error => {
        errorLog(`Response error: ${error?.message}${error?.config?.url ? ` for URL: ${error.config.url}` : ''}`);
        return Promise.reject(error);
    });

    return axiosInstance;
};

// const getEndpointFromConfig = () => {
//     const config = vscode.workspace.getConfiguration(COMATE_CONFIG_PREFIX);
//     const value = config.get<string>('serviceEndpoint');
//     return value ?? '';
// };

export const withProxy = (axiosInstance: AxiosInstance) => {
    // if (!isPoc) {
    return axiosInstance;
    // }

    // axiosInstance.interceptors.request.use(config => {
    //     const endpoint = getEndpointFromConfig();
    //     if (endpoint) {
    //         config.baseURL = endpoint;
    //     }
    //     return config;
    // }, error => {
    //     return Promise.reject(error);
    // });

    // return axiosInstance;
};

interface UserInfo {
    user: string;
    license: string;
    customizeService?: string;
}

interface HeadersConfig extends Partial<UserInfo> {
    clientType?: string;
    clientVersion?: string;
}

const headersConfig: HeadersConfig = {};

export function updateApiHeadersConfig(config: HeadersConfig | undefined) {
    Object.assign(headersConfig, config);
}

export async function checkOpenFolderGlobalState() {
    const context = await getExtensionContextAsync();
    const openFolderState = await context.globalState.get('openFolderState', {redirect: undefined});
    if (openFolderState?.redirect) {
        vscodeCommands.openChatPanel({activeTabKey: 'AGENT'});
    }
    delete openFolderState.redirect;
    await context.globalState.update('openFolderState', openFolderState);
}

type UserInfoGetter = () => Promise<UserInfo> | UserInfo;

let getUserInfo: UserInfoGetter | null = null;

export function setApiUserInfoGetter(getter: UserInfoGetter) {
    getUserInfo = getter;
}

export async function getHeadersConfig() {
    const userInfo = getUserInfo
        ? await getUserInfo()
        : undefined;
    updateApiHeadersConfig(userInfo);
    return cloneDeep(headersConfig);
}

function getAcceptLanguage() {
    const currentLanguage = L10n.currentLanguage;
    switch (currentLanguage) {
        case 'en':
            return 'en-US,en';
        default:
            return 'zh-CN,zh';
    }
}

export function withAcceptLanguage(axiosInstance: AxiosInstance) {
    axiosInstance.interceptors.request.use(async config => {
        config.headers['Accept-Language'] = getAcceptLanguage();
        return config;
    }, error => {
        return Promise.reject(error);
    });
    return axiosInstance;
}

const WHITE_LIST_PREFIX = [
    '/key/type/',
];

function withPrivateService(axiosInstance: AxiosInstance) {
    axiosInstance.interceptors.request.use(async config => {
        // eslint-disable-next-line no-console
        const comateConfig = vscode.workspace.getConfiguration(COMATE_CONFIG_PREFIX);
        const privateService = formatPrivateService(comateConfig.get<string>('privateService'));
        if (!isInternal && config.baseURL && privateService) {
            if (!isPoc && WHITE_LIST_PREFIX.find(v => config.url?.startsWith(v))) {
                return config;
            }
            // 如果config.baseURL包含https://comate.baidu.com，把 https://comate.baidu.com 替换成headersConfig.privateService
            config.baseURL = config.baseURL.replace('https://comate.baidu.com', privateService);
            // eslint-disable-next-line no-console
            console.log('service url', `${config.baseURL}${config.url}`);
            // 私有域请求超时时间放大至2min
            config.timeout = 2 * 60 * 1000;
        }
        return config;
    }, error => {
        return Promise.reject(error);
    });
    return axiosInstance;
}

const API_MAP = {
    saas: {
        [ENVIRONMENT.PRODUCTION]: SAAS_API_HOST,
        [ENVIRONMENT.DEVELOPMENT]: SAAS_API_HOST,
        [ENVIRONMENT.TEST]: SAAS_TEST_API_HOST,
    },
    internal: {
        [ENVIRONMENT.PRODUCTION]: INTERNAL_API_HOST,
        [ENVIRONMENT.DEVELOPMENT]: INTERNAL_API_HOST,
        [ENVIRONMENT.TEST]: INTERNAL_TEST_API_HOST,
    },
    // TODO 暂时没用到
    poc: {
        [ENVIRONMENT.PRODUCTION]: SAAS_API_HOST,
        [ENVIRONMENT.DEVELOPMENT]: SAAS_API_HOST,
        [ENVIRONMENT.TEST]: SAAS_TEST_API_HOST,
    },
};

let httpHost = process.env.HTTP_HOST;

export function setApiHost(newHttpHost: string) {
    httpHost = newHttpHost;
}

export const getApiHost = () => {
    if (httpHost) {
        return httpHost;
    }
    return API_MAP?.[$features.PLATFORM]?.[$features.ENVIRONMENT] ?? INTERNAL_API_HOST;
};

export function createAxios(options: CreateAxiosDefaults, logLevel?: LogLevel) {
    return withPrivateService(withAcceptLanguage(withLogger(
        axios.create({proxy: false, baseURL: getApiHost(), ...options}),
        logLevel
    )));
}

export function tradeCancelToken(cancelToken?: CancellationToken) {
    if (!cancelToken) {
        return undefined;
    }
    const axiosCancelToken = axios.CancelToken.source();
    cancelToken.onCancellationRequested(() => {
        axiosCancelToken.cancel();
    });
    return axiosCancelToken;
}
