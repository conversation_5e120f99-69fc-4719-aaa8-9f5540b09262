/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable max-depth */
/* eslint-disable complexity */
import path from 'node:path';
import * as vscode from 'vscode';
import {inject, injectable} from 'inversify';
import {throttle} from 'lodash';
import {
    TEXT_CHANGE_DETAILS,
    TEXT_CHANGE_DETAILS_ACCEPT,
    WATI_FOR_ACCEPT_CODE_FROM_CHAT,
    CONTEXT_COMPOSER_DIFF_SOURCE,
} from '@shared/constants';
import {LicenseFullDetail} from '@comate/plugin-shared-internals';
import {getExtensionContextAsync} from '@/utils/extensionContext';
import {getLicenseFullDetail, RetentionRateData, uploadLineChange} from '@/api';
import {iocContainer} from '@/iocContainer';
import {getTextWithLineTirm} from '@/utils/removeEmptyLines';
import {info, error} from '@/common/outputChannel';
import {isInternal} from '@/utils/features';
import {ConfigKey, VSCodeConfigProvider} from '../ConfigProvider';

export interface ChangeDetail {
    acceptNewLineCount: number;
    acceptCharLength: number;
    totalNewLineCount: number;
    totalCharLength: number;
    language: string;
    uri: string;
    lineNumber: number;
    source: 'completion' | 'chat';
    startTimestamp: number;
    endTimestamp: number;
}

const languageIdBlackList = ['markdown', 'plaintext'];
const fileNameBlackList = ['makefile', 'dockerfile'];

function inExcludedFile(document: vscode.TextDocument) {
    if (document.uri.scheme === 'vscode-scm') {
        return true;
    }
    if (
        (document.uri.scheme !== 'file' || document.uri.fsPath.endsWith('.log'))
        && document.uri.scheme !== 'untitled'
    ) {
        return true;
    }
    if (document.isUntitled && document.uri.scheme === 'untitled') {
        return true;
    }
    const targetLanguageId = document.languageId;
    if (languageIdBlackList.includes(targetLanguageId)) {
        return false;
    }
    const fsPath = document.uri.fsPath;
    const fileName = path.basename(fsPath)?.toLocaleLowerCase();
    if (fileNameBlackList.some(item => fileName.includes(item))) {
        return true;
    }

    return false;
}

class FileEvent {
    modifiedTime: number;
    uri: string = '';
    event: string = '';
    constructor() {
        this.modifiedTime = Date.now();
    }
}

function matchCodeSnippets(source: string, target: string) {
    if (!target || !source) {
        return false;
    }
    const sourceTrim = source.replace(/\s/g, '');
    const targetTrim = target?.replace(/\s/g, '');
    return sourceTrim === targetTrim;
}

export async function asyncSendStatistics(changeDetail: ChangeDetail[]) {
    try {
        const key = iocContainer.get(VSCodeConfigProvider).getConfig<string>(
            isInternal ? ConfigKey.Username : ConfigKey.Key
        );
        if (!key) {
            return;
        }
        const context = await getExtensionContextAsync();
        const ideVersion = vscode.version;
        const pluginVersion = context.extension.packageJSON.version;

        const params = changeDetail.map(v => {
            const newV: Record<string, string | number> = {...v};
            Object.keys(newV).forEach(key => {
                if (key !== 'lineNumber') {
                    newV[key] = String(newV[key]);
                }
            });
            return {
                ...newV,
                key,
                ide: 'vscode',
                ideVersion,
                pluginVersion,
            };
        });
        info(`codeRatioCalculator: ${JSON.stringify(params)}`);
        // TODO 补一下类型
        await uploadLineChange(params as RetentionRateData[]);
    }
    catch (e: unknown) {
        if (e instanceof Error) {
            error('codeRatioCalculator send error:', e.message);
        }
    }
}

function mergeConsecutiveChanges(changes: ChangeDetail[], mergedChanges: ChangeDetail[]) {
    changes.forEach((currentChange, index) => {
        const previousChange = index > 0 ? changes[index - 1] : undefined;

        const shouldMergeWithPrevious = previousChange
            && (previousChange.totalNewLineCount === 1 || previousChange.acceptNewLineCount === 1)
            && currentChange.uri === previousChange.uri
            && currentChange.lineNumber !== undefined
            && currentChange.lineNumber === previousChange.lineNumber;

        if (shouldMergeWithPrevious) {
            const lastMergedChange = mergedChanges[mergedChanges.length - 1];

            lastMergedChange.totalCharLength += currentChange.totalCharLength;
            lastMergedChange.acceptCharLength += currentChange.acceptCharLength;

            lastMergedChange.totalNewLineCount = Math.max(
                currentChange.totalNewLineCount,
                lastMergedChange.totalNewLineCount
            );
            lastMergedChange.acceptNewLineCount = Math.max(
                currentChange.acceptNewLineCount,
                lastMergedChange.acceptNewLineCount
            );

            lastMergedChange.startTimestamp = Math.min(lastMergedChange.startTimestamp, currentChange.startTimestamp);
            lastMergedChange.endTimestamp = Math.max(lastMergedChange.endTimestamp, currentChange.endTimestamp);
        }
        else {
            mergedChanges.push(currentChange);
        }
    });
}

@injectable()
export class CodeRatioCalculator {
    private readonly fileChangeEventMap: Map<string, FileEvent>;
    private causedByPasteEvent: boolean;
    private reportTextchangeEventFixedTime: number;
    private reportTextChangeEventWithDebounce: () => void;
    private licenseFullDetail?: LicenseFullDetail;

    constructor(@inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider) {
        this.fileChangeEventMap = new Map();
        this.causedByPasteEvent = false;
        this.reportTextchangeEventFixedTime = 1000 * 60 * 3; // 3分钟上报一次
        this.reportTextChangeEventWithDebounce = throttle(
            this.reportTextChangeEvent,
            this.reportTextchangeEventFixedTime,
            {
                leading: true,
                trailing: true,
            }
        );
    }

    get effectiveInputLines() {
        return this.licenseFullDetail?.commonConfig?.effectiveInputLines || 30;
    }

    async isEnabled() {
        if (isInternal) {
            return true;
        }
        const res = await getLicenseFullDetail(this.configProvider.getLicense());
        this.licenseFullDetail = res;
        return ['ENTERPRISE', 'TRIAL_ENTERPRISE', 'CUSTOMIZED_ENTERPRISE', 'PRO_ENTERPRISE'].includes(res.typeCode);
    }

    async getConfig() {
        const delay = this.licenseFullDetail?.commonConfig?.diffTrackerRateSeconds ?? 180;
        this.reportTextchangeEventFixedTime = delay * 1000;
        this.reportTextChangeEventWithDebounce = throttle(
            this.reportTextChangeEvent,
            this.reportTextchangeEventFixedTime,
            {
                leading: true,
                trailing: true,
            }
        );
    }
    async start() {
        const context = await getExtensionContextAsync();
        context.workspaceState.update(TEXT_CHANGE_DETAILS, undefined);
        context.workspaceState.update(TEXT_CHANGE_DETAILS_ACCEPT, undefined);
        if (!(await this.isEnabled())) {
            return;
        }
        this.getConfig();
        vscode.workspace.onDidChangeTextDocument(async e => {
            this.causedByPasteEvent = false;
            try {
                if (vscode.window.activeTextEditor?.document.uri.fsPath !== e.document.uri.fsPath) {
                    return;
                }
                if (inExcludedFile(e.document)) {
                    return;
                }
                const changeEvent = e.contentChanges.length > 0 ? e.contentChanges[0] : undefined;
                if (changeEvent) {
                    const fileEvent = new FileEvent();
                    fileEvent.uri = e.document.uri.fsPath;
                    if (changeEvent.text === '') {
                        if (this.isNewLineEvent(changeEvent, e.document)) {
                            fileEvent.event = 'newline';
                        }
                        else if (changeEvent.rangeLength >= 1) {
                            fileEvent.event = 'delete';
                        }
                    }
                    else if (changeEvent.text && changeEvent.text.length >= 2 && changeEvent.rangeLength === 0) {
                        fileEvent.event = 'insert';
                    }
                    else {
                        fileEvent.event = 'other';
                    }
                    if (fileEvent.event) {
                        this.fileChangeEventMap.set(fileEvent.uri, fileEvent);
                    }
                    await this.isPasteEvent(e);
                }
                this.cacheNewCodeCountForTextChange(e, context);
                this.reportTextChangeEventWithDebounce();
            }
            catch (e) {
                if (e instanceof Error) {
                    error('codeRatioCalculator error:', e.message);
                }
            }
        });
    }

    isNewLineEvent(changeEvent: vscode.TextDocumentContentChangeEvent, document: vscode.TextDocument) {
        if (changeEvent.text === '') {
            const start = changeEvent.range.start;
            const end = changeEvent.range.end;
            const rangeLength = changeEvent.rangeLength;
            if (
                start.line === end.line && start.character === 0 && end.character === rangeLength
                && document.lineAt(start.line).text === ''
            ) {
                return false;
            }
        }
        return false;
    }

    async isPasteEvent(e: vscode.TextDocumentChangeEvent) {
        const clipboardText = await vscode.env.clipboard.readText();
        if (clipboardText && clipboardText.length > 0 && e.contentChanges.length > 0) {
            const contentText = e.contentChanges[0].text;
            if (matchCodeSnippets(contentText, clipboardText)) {
                this.causedByPasteEvent = true;
                return;
            }
        }
        this.causedByPasteEvent = false;
    }

    private judgeFromChatChange(changedText: string, chatSuggestedCode: string): boolean {
        const changedTextLines = changedText.split('\n');
        const chatSuggestedCodeLines = chatSuggestedCode.split('\n');
        // 变更行和推荐行按照行比较，如果存在一行代码，匹配到 chatSuggestedCode 里的代码 且大于3个字符，则认为该变更是来自聊天窗口的
        return changedTextLines.some((changedLine: string) => {
            return chatSuggestedCodeLines.some((line: string) => {
                if (matchCodeSnippets(line, changedLine) && changedLine.trim().length > 3) {
                    return true;
                }
                return false;
            });
        });
    }
    async cacheNewCodeCountForTextChange(
        changeEvent: vscode.TextDocumentChangeEvent,
        context: vscode.ExtensionContext
    ) {
        // 计算所有 changes 的总行数
                                // 0527修改(群id: 11358031) -  编辑区中的30行过滤规则去掉，先看数据，后续统一由后端过滤
        // const totalLines = changeEvent.contentChanges.reduce((sum, change) => {
        //     const textWithLine = getTextWithLineTirm(change.text);
        //     return sum + textWithLine.split('\n').length;
        // }, 0);
        if (changeEvent?.contentChanges) {
            for (const contentChange of changeEvent.contentChanges) {
                const textWithLine = getTextWithLineTirm(contentChange.text);
                const textLineLength = textWithLine.split('\n').length;
                const textLength = textWithLine.length;

                const changeQueue: ChangeDetail[] = context.workspaceState.get(TEXT_CHANGE_DETAILS) || [];
                if (textLength > 0) {
                    const chatSuggestedCode = context.workspaceState.get<string>(WATI_FOR_ACCEPT_CODE_FROM_CHAT) ?? '';
                    const acceptTextFromText = this.getPureCodeText(chatSuggestedCode);

                    const changeText = this.getPureCodeText(contentChange.text);
                    const filePath = changeEvent.document.uri.fsPath;
                    const fileExtension = path.extname(filePath).slice(1);
                    const source = context.workspaceState.get<{value: string}>(CONTEXT_COMPOSER_DIFF_SOURCE)?.value;
                    if (
                        changeText && this.judgeFromChatChange(contentChange.text, chatSuggestedCode)
                    ) {
                        /*
                        * 必要条件
                        * 所有侧边栏都要统一走到这 source有值
                        */
                        // 如果是来自聊天窗口的，则推荐行直接作为分母计算，防止采纳行远大于推荐的行数
                        changeQueue.push({
                            // acceptNewLineCount: textLineLength, // 保留旧的逻辑注释，作为对照
                            // acceptCharLength: textLength,
                            acceptNewLineCount: chatSuggestedCode.split('\n').length,
                            acceptCharLength: acceptTextFromText.length,
                            totalNewLineCount: chatSuggestedCode.split('\n').length,
                            totalCharLength: acceptTextFromText.length,
                            language: fileExtension,
                            source: 'chat',
                            uri: changeEvent.document.uri.fsPath,
                            lineNumber: contentChange.range.start.line,
                            startTimestamp: Date.now(),
                            endTimestamp: Date.now(),
                        });
                    }
                    else {
                        if (source) {
                            return;
                        }
                        if (this.causedByPasteEvent) {
                            return;
                        }
                        // 0527修改(群id: 11358031) -  编辑区中的30行过滤规则去掉，先看数据，后续统一由后端过滤
                        // if (textLineLength >= this.effectiveInputLines) {
                        //     return;
                        // }
                        // // 如果completion总行数超过限制，直接返回
                        // if (totalLines >= this.effectiveInputLines) {
                        //     return;
                        // }
                        changeQueue.push({
                            acceptNewLineCount: 0,
                            acceptCharLength: 0,
                            totalNewLineCount: textLineLength,
                            totalCharLength: textLength,
                            language: fileExtension,
                            source: 'completion',
                            uri: changeEvent.document.uri.fsPath,
                            lineNumber: contentChange.range.start.line,
                            startTimestamp: Date.now(),
                            endTimestamp: Date.now(),
                        });
                    }
                    await context.workspaceState.update(TEXT_CHANGE_DETAILS, changeQueue);
                }
            }
        }
    }

    getPureCodeText(text: string) {
        return text ? text.replace(/\r\n/g, '\n').replace(/\s/g, '') : '';
    }

    reportTextChangeEvent = async () => {
        const context = await getExtensionContextAsync();
        const textChanges: ChangeDetail[] = context.workspaceState.get(TEXT_CHANGE_DETAILS) || [];
        const acceptedChanges: ChangeDetail[] = context.workspaceState.get(TEXT_CHANGE_DETAILS_ACCEPT) || [];

        if (!textChanges.length) {
            return;
        }

        const mergedChanges: ChangeDetail[] = [];
        mergeConsecutiveChanges(textChanges, mergedChanges);
        mergeConsecutiveChanges(acceptedChanges, mergedChanges);

        const aggregatedChanges: ChangeDetail[] = [];
        let totalAcceptNewLineCount = 0;
        let totalAcceptCharLength = 0;
        let totalNewLineCount = 0;
        let totalCharLength = 0;
        let earliestTimestamp = Infinity;
        let latestTimestamp = -Infinity;

        mergedChanges.forEach(change => {
            const existingChange = aggregatedChanges.find(
                c => c.uri === change.uri && c.source === change.source
            );

            if (existingChange) {
                existingChange.acceptNewLineCount += change.acceptNewLineCount;
                existingChange.acceptCharLength += change.acceptCharLength;
                existingChange.totalNewLineCount += change.totalNewLineCount;
                existingChange.totalCharLength += change.totalCharLength;
                existingChange.startTimestamp = Math.min(existingChange.startTimestamp, change.startTimestamp);
                existingChange.endTimestamp = Math.max(existingChange.endTimestamp, change.endTimestamp);
            }
            else {
                aggregatedChanges.push(change);
            }
        });

        aggregatedChanges.forEach(change => {
            change.totalNewLineCount = Math.max(change.totalNewLineCount, change.acceptNewLineCount);
            change.totalCharLength = Math.max(change.totalCharLength, change.acceptCharLength);

            totalAcceptNewLineCount += change.acceptNewLineCount;
            totalAcceptCharLength += change.acceptCharLength;
            totalNewLineCount += change.totalNewLineCount;
            totalCharLength += change.totalCharLength;

            earliestTimestamp = Math.min(earliestTimestamp, change.startTimestamp);
            latestTimestamp = Math.max(latestTimestamp, change.endTimestamp);
            delete (change as any).lineNumber;
        });

        asyncSendStatistics(aggregatedChanges);

        context.workspaceState.update(TEXT_CHANGE_DETAILS, undefined);
        context.workspaceState.update(TEXT_CHANGE_DETAILS_ACCEPT, undefined);
    };
}
