import {URI} from 'vscode-uri';
import {CancellationToken} from 'vscode-languageserver-protocol';
import {RewriteBaseResult} from '@/api/smartTab';
import {IProgrammingContextTracker} from '../ProgrammingContextTracker/types';

export interface RewriteHandler {
    contextTracker: IProgrammingContextTracker;
    showRewrite: (uri: URI, value: Omit<RewriteBaseResult, 'path' | 'score'>, cancelToken?: CancellationToken) => void;
    getEnabled: (uri: URI, startLine: number, endLine: number) => boolean;
    setCursorPredictionContext: (data: any) => any;
}
