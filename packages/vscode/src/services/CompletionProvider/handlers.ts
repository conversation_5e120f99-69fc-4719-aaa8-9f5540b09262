import * as vscode from 'vscode';
import {Container} from 'inversify';
import {TEXT_CHANGE_DETAILS_ACCEPT} from '@shared/constants';
import {TYPES} from '@/inversify.config';
import logger from '@/common/consoleLogger';
import {TelemetryData, telemetryShown, telemetryAccepted} from '@/common/telemetry';
import {getExtensionContextAsync} from '@/utils/extensionContext';
import {getTextWithLineTirm, removeEmptyLines} from '@/utils/removeEmptyLines';
import {acceptCode, showCode} from '../../api';
import {postInsertionTasks} from '../../common/telemetry/postTasks';
import {LRUCache} from '../../utils/cache';
import {isLastLineEmpty} from '../../utils/indent';
import {VSCodeConfigProvider} from '../ConfigProvider';
import {info} from '../../common/outputChannel';
import {CompletionSource, GhostTextResult, InlineCompletionItem, InlineCompletionResult, ResultType} from '../types';
import {ILicenseController} from '../LicenseController/types';
import {ITimeTracker} from '../TimeTracker/types';
import {CompletionContextProvider} from '../CompletionContextProvider';
import {TreeSitterProvider} from '../TreeSitterProvider';
import {PerformanceLogProvider} from '../PerformanceLogProvider';
import {ChangeDetail} from '../CodeRatioCalculator';
import {DisplayState} from './State';
import {getGhostText, inlineState} from './ghostText';
import {ICompletionSuccessRateTracker} from './SuccessRateTracker/types';
import {LoadingDecorations} from './LoadingDecorations';
import {LineCommentDecorations} from './LineCommentDecorations';
import {RewriteHandler} from './types';

const displayState = new DisplayState();
// 额外加一个 cache ，记录最近是否展示过，决定是否调 shown 接口
// 因为 displayState.displayedCompletions 除了点 previous/next 每次都会清空
const shownCompletionCache = new LRUCache<string>(100);

export function handleGhostTextShown(
    globalContext: Container,
    item: InlineCompletionItem,
    document?: vscode.TextDocument,
    position?: vscode.Position
) {
    displayState.setSelected(item.uuid);
    if (!displayState.displayedCompletions.find(v => v.uuid === item.uuid)) {
        displayState.addShownCompletion(item);
    }

    if (shownCompletionCache.get(item.uuid) === undefined) {
        // 展示给用户了，打个点
        showCode(item.uuid);
        telemetryShown(globalContext, item, document, position);
    }
    shownCompletionCache.put(item.uuid, item.uuid);
}

function completionsFromGhostTextResults(
    document: vscode.TextDocument,
    position: vscode.Position,
    selectedCompletionInfo: vscode.SelectedCompletionInfo | undefined,
    ghostText: GhostTextResult,
    duration: number,
    cache: boolean = false
): InlineCompletionItem[] {
    const currentLine = document.lineAt(position);
    const {data, resultType, telemetryBlob} = ghostText;
    const completions = data.map(completion => {
        const [range, text] = (() => {
            if (completion.displayNeedsWsOffset && currentLine.isEmptyOrWhitespace) {
                const range = new vscode.Range(new vscode.Position(position.line, 0), position);
                return [range, completion.choice.completionText];
            }
            else if (
                currentLine.isEmptyOrWhitespace
                && completion.choice.completionText.startsWith(currentLine.text)
            ) {
                const range = new vscode.Range(new vscode.Position(position.line, 0), position);
                return [range, completion.choice.completionText];
            }
            else if (completion.choice.isExtensionToSelectedCompletionInfo && selectedCompletionInfo) {
                const text = selectedCompletionInfo.text + completion.choice.originalText;
                const textLine = document.lineAt(position.line);
                const range = completion.coverSuffix
                    ? selectedCompletionInfo.range.with(undefined, textLine.range.end)
                    : selectedCompletionInfo.range;
                return [range, text];
            }
            else {
                const line = document.lineAt(position);
                const start = line.range.start;
                const textBeforeCursor = document.getText(new vscode.Range(start, position));
                // 如果补全能完全匹配后缀，就替换整行
                // 如果不能，就用后端返回的覆盖内容
                const range = completion.coverSuffix
                    ? line.range
                    : new vscode.Range(
                        start,
                        position.translate(0, completion.choice.expectCoverText.length)
                    );
                const text = textBeforeCursor + completion.displayText;
                return [range, text];
            }
        })();

        return {
            uuid: completion.choice.rawData.uuid,
            text: text,
            range: range,
            displayText: completion.displayText,
            position: position,
            offset: document.offsetAt(position),
            resultType: resultType,
            originalText: completion.choice.originalText,
            source: completion.choice.source,
            coverSuffix: completion.coverSuffix,
            isExtensionToSelectedCompletionInfo: completion.choice.isExtensionToSelectedCompletionInfo,
        };
    });

    // 过滤掉补全和要覆盖的后缀相同的情况
    const validCompletions = completions.filter(item => {
        const {start, end} = item.range;
        const replaceLength = end.character - start.character;
        return replaceLength < item.text.length;
    });

    const orderedCompletions = (() => {
        if (resultType === ResultType.TypingAsSuggested && displayState.selectedCompletion) {
            const selected = validCompletions.find(item => item.uuid === displayState.selectedCompletion);
            if (selected) {
                const others = validCompletions.filter(
                    item => item.uuid !== displayState.selectedCompletion
                );
                return [selected, ...others];
            }
        }
        return validCompletions;
    })();

    const inlineCompletions = orderedCompletions.map(item => {
        const {uuid, text, range: completionRange} = item;
        const range = new vscode.Range(
            new vscode.Position(completionRange.start.line, completionRange.start.character),
            new vscode.Position(completionRange.end.line, completionRange.end.character)
        );
        // @ts-expect-error
        const inlineCompletion: InlineCompletionItem = new vscode.InlineCompletionItem(text, range);
        inlineCompletion.telemetry = telemetryBlob;
        inlineCompletion.displayText = item.displayText;
        inlineCompletion.uuid = uuid;
        inlineCompletion.uri = document.uri;
        inlineCompletion.cache = cache;
        inlineCompletion.duration = duration;
        inlineCompletion.position = item.position;
        inlineCompletion.insertOffset = document.offsetAt(
            new vscode.Position(item.position.line, item.position.character)
        );
        inlineCompletion.command = {
            title: 'unvisiable',
            command: 'baidu.comate._ghostTextPostInsert',
            arguments: [inlineCompletion],
        };
        inlineCompletion.originalText = item.originalText;
        inlineCompletion.source = item.source;
        inlineCompletion.coverSuffix = item.coverSuffix;
        inlineCompletion.isExtensionToSelectedCompletionInfo = item.isExtensionToSelectedCompletionInfo;

        return inlineCompletion;
    });

    return inlineCompletions;
}

// eslint-disable-next-line complexity, max-statements
export async function getInlineCompletions(
    globalContext: Container,
    document: vscode.TextDocument,
    position: vscode.Position,
    completionContext: vscode.InlineCompletionContext,
    cancelToken: vscode.CancellationToken,
    completionContextProvider?: CompletionContextProvider,
    treeSitterProvider?: TreeSitterProvider,
    loadingDecorations?: LoadingDecorations,
    lineCommentDecorations?: LineCommentDecorations,
    isCommentToCodeMode?: boolean,
    rewriteHandler?: RewriteHandler
): Promise<InlineCompletionResult> {
    const completionStartTime = performance.now();
    const telemetryData = TelemetryData.createAndMarkAsIssued();
    const licenseController = globalContext.get<ILicenseController>(TYPES.ILicenseController);
    if (!licenseController.hasLicense) {
        return {
            type: 'aborted',
            reason: 'not logged in',
        };
    }

    const configProvider = globalContext.get(VSCodeConfigProvider);
    const languageId = configProvider.getLanguageIdByFilePath(document.uri.fsPath);
    if (!languageId || !configProvider.getEnabledConfig(languageId)) {
        return {
            type: 'aborted',
            reason: 'document is ignored',
        };
    }

    if (cancelToken.isCancellationRequested) {
        return {
            type: 'cancelled',
            reason: 'cancelled before getGhost',
        };
    }
    globalContext.get<ITimeTracker>(TYPES.ITimeTracker).recordCompletionStart();

    const ghostTextResult = await getGhostText(
        globalContext,
        document,
        position,
        cancelToken,
        completionContext,
        telemetryData,
        completionContextProvider,
        document.version,
        treeSitterProvider,
        loadingDecorations,
        lineCommentDecorations,
        isCommentToCodeMode,
        rewriteHandler
    );

    if (ghostTextResult.type !== 'success') {
        const editor = vscode.window.activeTextEditor;
        const line = editor?.selection.start.line;
        const uri = editor?.document.uri;
        const tracker = globalContext.get<ICompletionSuccessRateTracker>(TYPES.ICompletionSuccessRateTracker);
        tracker.trackFailure(uri, line);
        return ghostTextResult;
    }

    displayState.reset(position, document.uri);

    const completionEndTime = performance.now();

    const isFromNetwork = ghostTextResult.value?.resultType === ResultType.Network;

    const completionDuration = completionEndTime - completionStartTime;

    logger.debug('(completion)(time) total:', completionDuration);

    const inlineCompletions = completionsFromGhostTextResults(
        document,
        position,
        completionContext.selectedCompletionInfo,
        ghostTextResult.value,
        completionDuration,
        !isFromNetwork
    );

    if (cancelToken.isCancellationRequested) {
        return {
            type: 'cancelled',
            reason: 'cancelled before display',
        };
    }

    return inlineCompletions.length > 0
        ? {
            type: 'success',
            value: inlineCompletions,
        }
        : {
            type: 'failed',
            reason: 'no completions in final result',
        };
}

export function handleGhostTextResult(result: InlineCompletionResult) {
    if (result.type === 'success') {
        return result.value;
    }
    else {
        info(`${result.type}: ${result.reason}`);
    }
    return [];
}

async function updateAcceptGlobalStateForCodeRatioCalculator(inlineCompletion: InlineCompletionItem) {
    const context = await getExtensionContextAsync();
    const inlineCompletionAcceptedList = context.workspaceState.get<ChangeDetail[]>(TEXT_CHANGE_DETAILS_ACCEPT) || [];
    const acceptNewLineCount = removeEmptyLines(inlineCompletion.displayText).length || 0;
    const acceptCharLength = getTextWithLineTirm(inlineCompletion.displayText).length || 0;
    const document = vscode.workspace.textDocuments.find(doc => doc.uri.toString() === inlineCompletion.uri.toString());
    inlineCompletionAcceptedList.push({
        acceptNewLineCount,
        acceptCharLength,
        totalNewLineCount: 0,
        totalCharLength: 0,
        language: document?.languageId || 'UNDEFINED',
        source: 'completion',
        startTimestamp: Date.now(),
        endTimestamp: Date.now(),
        uri: inlineCompletion.uri.fsPath,
        lineNumber: inlineCompletion.position.line,
    });
    context.workspaceState.update(TEXT_CHANGE_DETAILS_ACCEPT, inlineCompletionAcceptedList);
}

export async function handleGhostTextPostInsert(globalContext: Container, inlineCompletion: InlineCompletionItem) {
    acceptCode({
        uuid: inlineCompletion.uuid,
        accepted: true,
        content: '',
        // 由于多行推荐时可逐行采纳，但后续用户可能又直接Tab把剩余的都采纳了， 这样可能导致服务端记录的generatedContent只有逐行采纳的那几行，因此这里采纳时将全部内容回传。
        generatedContent: inlineCompletion.originalText,
    });

    const isMultiLine = inlineCompletion.originalText?.includes('\n');

    const completionLogSkill = (() => {
        if (inlineCompletion.source === CompletionSource.API) {
            return 'completion-api';
        }
        return isMultiLine ? 'completion-multiline' : 'completion';
    })();

    globalContext.get(PerformanceLogProvider).log({
        skill: completionLogSkill,
        uuid: inlineCompletion.uuid,
        duration: Math.ceil(inlineCompletion.duration ?? 0),
        plugin: 'comate',
        cache: inlineCompletion.cache,
    });

    globalContext.get<ITimeTracker>(TYPES.ITimeTracker).recordCompletionEnd(inlineCompletion.displayText);

    displayState.unset();
    if (
        inlineCompletion.telemetry
        && inlineCompletion.uri
        && inlineCompletion.displayText
        && undefined !== inlineCompletion.insertOffset
        && inlineCompletion.range
    ) {
        const completionData = {
            offset: inlineCompletion.insertOffset,
            uri: inlineCompletion.uri,
            displayText: inlineCompletion.displayText,
            telemetry: inlineCompletion.telemetry,
            uuid: inlineCompletion.uuid,
        };
        telemetryAccepted(globalContext, inlineCompletion);
        await postInsertionTasks(
            globalContext,
            completionData
        );
    }
    updateAcceptGlobalStateForCodeRatioCalculator(inlineCompletion);
}

export function handleCompletionHide() {
    if (inlineState.prefix && inlineState.cacheKey && isLastLineEmpty(inlineState.prefix)) {
        inlineState.setHiddenKey(inlineState.cacheKey);
        return;
    }
    inlineState.setHiddenKey();
}
