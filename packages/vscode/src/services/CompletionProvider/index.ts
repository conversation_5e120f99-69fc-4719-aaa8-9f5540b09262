/* eslint-disable no-negated-condition, max-depth */
import * as vscode from 'vscode';
import {Container} from 'inversify';
import 'reflect-metadata';
import {InlineCompletionItem} from '../types';
import {CompletionContextProvider} from '../CompletionContextProvider';
import {TreeSitterProvider} from '../TreeSitterProvider';
import {VSCodeConfigProvider} from '../ConfigProvider';
import {CodeBlankRowTipProvider} from '../CodeBlankRowTipProvider';
import {MultiLineCompletionTracker} from './MultiLineCompletionTracker';
import {GhostTextUsageGuide} from './GhostTextUsageGuide';
import {AbstractCompletionProvider} from './AbstractCompletionProvider';
import {LoadingDecorations} from './LoadingDecorations';
import {LineCommentDecorations} from './LineCommentDecorations';
import {RewriteHandler} from './types';

export class CompletionProvider extends AbstractCompletionProvider {
    constructor(
        protected ctx: Container,
        protected ghostTextUsageGuide?: GhostTextUsageGuide,
        protected multiLineCompletionTracker?: MultiLineCompletionTracker,
        protected completionContextProvider?: CompletionContextProvider,
        protected treeSitterProvider?: TreeSitterProvider,
        protected loadingDecorations?: LoadingDecorations,
        protected lineCommentDecorations?: LineCommentDecorations,
        protected configProvider?: VSCodeConfigProvider,
        protected rewriteHandler?: RewriteHandler,
        protected codeBlankRowTipProvider?: CodeBlankRowTipProvider
    ) {
        super(
            ctx,
            ghostTextUsageGuide,
            multiLineCompletionTracker,
            completionContextProvider,
            treeSitterProvider,
            loadingDecorations,
            lineCommentDecorations,
            configProvider,
            rewriteHandler,
            codeBlankRowTipProvider
        );
    }

    async provideInlineCompletionItems(
        document: vscode.TextDocument,
        position: vscode.Position,
        context: vscode.InlineCompletionContext,
        cancelToken: vscode.CancellationToken
    ) {
        const inlineCompletions = await super.provideInlineCompletionItems(document, position, context, cancelToken);
        const firstCompletion = inlineCompletions[0];
        // handleDidShowCompletionItem 方法还在 vscode api proposal 阶段，因此无法知晓我们的续写内容是否展示给了用户。这里直接认为展示给了用户，进行相应处理。
        // https://github.com/microsoft/vscode/blob/ec291c126878742ad640055ce604a58129cd088c/src/vscode-dts/vscode.proposed.inlineCompletionsAdditions.d.ts#L48
        this._handleDidShowCompletionItem(this.ctx, firstCompletion, document, position);
        return inlineCompletions;
    }
}

export class ProposalEnabledCompletionProvider extends AbstractCompletionProvider {
    constructor(
        protected readonly ctx: Container,
        protected ghostTextUsageGuide?: GhostTextUsageGuide,
        protected multiLineCompletionTracker?: MultiLineCompletionTracker,
        protected completionContextProvider?: CompletionContextProvider,
        protected treeSitterProvider?: TreeSitterProvider,
        protected loadingDecorations?: LoadingDecorations,
        protected lineCommentDecorations?: LineCommentDecorations,
        protected configProvider?: VSCodeConfigProvider,
        protected rewriteHandler?: RewriteHandler,
        protected codeBlankRowTipProvider?: CodeBlankRowTipProvider
    ) {
        super(
            ctx,
            ghostTextUsageGuide,
            multiLineCompletionTracker,
            completionContextProvider,
            treeSitterProvider,
            loadingDecorations,
            lineCommentDecorations,
            configProvider,
            rewriteHandler,
            codeBlankRowTipProvider
        );
    }

    handleDidShowCompletionItem(item: InlineCompletionItem) {
        this._handleDidShowCompletionItem(this.ctx, item);
    }
}

export class LegacyCompletionProvider extends AbstractCompletionProvider {
    constructor(
        protected readonly ctx: Container,
        protected ghostTextUsageGuide?: GhostTextUsageGuide,
        protected multiLineCompletionTracker?: MultiLineCompletionTracker,
        protected completionContextProvider?: CompletionContextProvider,
        protected treeSitterProvider?: TreeSitterProvider,
        protected loadingDecorations?: LoadingDecorations,
        protected lineCommentDecorations?: LineCommentDecorations,
        protected configProvider?: VSCodeConfigProvider,
        protected rewriteHandler?: RewriteHandler,
        protected codeBlankRowTipProvider?: CodeBlankRowTipProvider
    ) {
        super(
            ctx,
            ghostTextUsageGuide,
            multiLineCompletionTracker,
            completionContextProvider,
            treeSitterProvider,
            loadingDecorations,
            lineCommentDecorations,
            configProvider,
            rewriteHandler,
            codeBlankRowTipProvider
        );
        this.disposables.push(
            // @ts-expect-error
            vscode.window.getInlineCompletionItemController(this).onDidShowCompletionItem(item => {
                const completionItem = item.completionItem as InlineCompletionItem;
                this._handleDidShowCompletionItem(this.ctx, completionItem);
            })
        );
    }
}
