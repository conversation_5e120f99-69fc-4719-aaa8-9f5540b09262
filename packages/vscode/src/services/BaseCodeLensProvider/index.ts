import * as vscode from 'vscode';
import {inject, injectable} from 'inversify';
import {TreeSitterProvider, isFileTooLarge} from '../TreeSitterProvider';
import {VSCodeConfigProvider, CodeLensDisplayMode, ConfigKey, CodelensConfig} from '../ConfigProvider';
import {DiffProvider} from '../DiffProvider';
import {TYPES} from '../../inversify.config';
import {ILicenseController} from '../LicenseController/types';
import Parser from 'web-tree-sitter';
import {extractFunctionNodes, checkTreeSitterSupport} from '../../utils/treeSitterUtils';
import {TreeSitterLanguage} from '../../utils/treeSitterUtils';
import {TemporaryCodeLensProvider} from '../TemporaryCodeLensProvider';

export interface ICodeLensProvider {
    /**
     * 配置项的 key，如果为空则表示不需要配置
     */
    configKey?: keyof CodelensConfig;

    /**
     * 使用预计算好的 nodes 生成 CodeLens
     * @param document 文档对象
     * @param nodes 预计算好的语法树节点
     * @returns CodeLens 数组
     */
    generateCodeLenses(
        nodes: Parser.SyntaxNode[],
        document?: vscode.TextDocument,
        treeSitterLanguage?: TreeSitterLanguage,
        cancelToken?: vscode.CancellationToken
    ): vscode.CodeLens[];
}

@injectable()
export class BaseCodeLensProvider implements vscode.CodeLensProvider, vscode.Disposable {
    private providers: ICodeLensProvider[] = [];
    private disposables: vscode.Disposable[] = [];
    static readonly supportedLanguages = [
        'python',
        'java',
        'go',
        'cpp',
        'c',
        'javascript',
        'typescript',
        'javascriptreact',
        'jsx',
        'typescriptreact',
        'vue',
    ];

    constructor(
        @inject(TreeSitterProvider) protected readonly treeSitterProvider: TreeSitterProvider,
        @inject(VSCodeConfigProvider) protected readonly configProvider: VSCodeConfigProvider,
        @inject(TYPES.ILicenseController) protected readonly licenseController: ILicenseController,
        @inject(DiffProvider) readonly diffProvider: DiffProvider,
        @inject(TemporaryCodeLensProvider) private readonly tmpCodeLensProvider: TemporaryCodeLensProvider
    ) {
        this.disposables.push(
            vscode.languages.registerCodeLensProvider(
                BaseCodeLensProvider.supportedLanguages.map(item => ({
                    scheme: 'file',
                    language: item,
                })),
                this
            )
        );
    }

    /**
     * 注册一个 CodeLens provider
     */
    addCodeLensProvider(provider: ICodeLensProvider) {
        this.providers.push(provider);
    }

    /**
     * 获取所有的 CodeLens，不考虑显示模式
     */
    getCodeLenses(document: vscode.TextDocument, cancelToken?: vscode.CancellationToken) {
        try {
            // 1. 检查是否在 diff 视图中
            if (this.diffProvider.isInDiff(document.uri.fsPath)) {
                return [];
            }

            // 2. 检查 license
            if (!this.licenseController.hasLicense) {
                return [];
            }

            if (isFileTooLarge(document)) {
                return [];
            }

            // 3. 获取配置
            const codelensConfig = this.configProvider.getConfig<CodelensConfig>(ConfigKey.EnableCodelens);

            // 4. 检查是否有启用的 provider
            // 过滤出有configKey的providers
            const configuredProviders = this.providers.filter(provider => provider.configKey);
            // 如果配置了configKey的providers都没有一个是启用的，则返回空数组
            if (
                codelensConfig
                && !configuredProviders.some(provider => provider.configKey && codelensConfig[provider.configKey])
            ) {
                return [];
            }

            // 5. 获取语法树节点
            const nodes = this.getNodes(document)?.filter(node => node.startPosition.row !== node.endPosition.row); // 确保函数整体大于一行;
            if (!nodes) {
                return [];
            }

            const allCodeLenses: vscode.CodeLens[] = [];
            // 6. 让每个启用的 provider 生成 CodeLens
            for (const provider of this.providers) {
                try {
                    // 检查是否启用，如果 provider 没有配置项则默认启用
                    if (!provider.configKey || codelensConfig && codelensConfig[provider.configKey]) {
                        const treeSitterLanguage = checkTreeSitterSupport(document.languageId);
                        const codeLenses = provider.generateCodeLenses(
                            nodes,
                            document,
                            treeSitterLanguage,
                            cancelToken
                        );
                        allCodeLenses.push(...this.tmpCodeLensProvider.filterOverlapCodeLenses(document, codeLenses));
                    }
                }
                catch (error) {
                    console.error(`Error in provider ${provider.configKey}:`, error);
                    // 继续处理其他 provider
                }
            }

            return allCodeLenses;
        }
        catch (error) {
            console.error('Error computing CodeLenses:', error);
            return [];
        }
    }

    /**
     * VS Code 调用的接口
     */
    async provideCodeLenses(
        document: vscode.TextDocument,
        cancelToken?: vscode.CancellationToken
    ): Promise<vscode.CodeLens[]> {
        // 检查显示模式
        const codelensDisplayMode = this.configProvider.getCodeLensDisplayMode();
        if (codelensDisplayMode !== CodeLensDisplayMode.TextTitle) {
            return [];
        }

        return this.getCodeLenses(document, cancelToken);
    }

    /**
     * 获取语法树节点
     */
    private getNodes(document: vscode.TextDocument): Parser.SyntaxNode[] | null {
        const tree = this.treeSitterProvider.getDocumentTree(document);
        const treeSitterLanguage = checkTreeSitterSupport(document.languageId);
        if (!tree || !treeSitterLanguage) {
            return null;
        }
        return extractFunctionNodes(treeSitterLanguage, tree.rootNode);
    }

    dispose() {
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];
        // 检查每个 provider 是否实现了 vscode.Disposable 接口，如果实现了就调用其 dispose 方法
        this.providers.forEach(provider => {
            if ('dispose' in provider && typeof (provider as vscode.Disposable).dispose === 'function') {
                (provider as vscode.Disposable).dispose();
            }
        });
    }
}
