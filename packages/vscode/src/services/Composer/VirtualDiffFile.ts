import * as vscode from 'vscode';
import {flow} from 'lodash';

export const COMATE_COMPOSRER_DIFF_SCHEME_1 = 'composer-diff-original';
export const COMATE_COMPOSRER_DIFF_SCHEME_2 = 'composer-diff-modified';

export const isComposerDiff = (uri: vscode.Uri) => {
    return uri.scheme === COMATE_COMPOSRER_DIFF_SCHEME_1
        || uri.scheme === COMATE_COMPOSRER_DIFF_SCHEME_2;
};

/** 判断当前document的uri是否是指定文件的diff页 */
export const isModifiedComposerDiff = (uri: vscode.Uri, fsPath: string) => {
    return uri.scheme === COMATE_COMPOSRER_DIFF_SCHEME_2 && uri.fsPath === fsPath;
};

export class VirtualFileSystem implements vscode.FileSystemProvider, vscode.Disposable {
    private readonly _emitter = new vscode.EventEmitter<vscode.FileChangeEvent[]>();
    readonly onDidChangeFile: vscode.Event<vscode.FileChangeEvent[]> = this._emitter.event;

    constructor(readonly opts: {readonly: boolean}) {}

    readonly fileContentMap = new Map<string, string>();
    readonly fileStatMap = new Map<string, vscode.FileStat>();
    // readonly streamOutputIntervalMap = new Map<string, NodeJS.Timeout>();
    watch(): vscode.Disposable {
        return new vscode.Disposable(() => {});
    }

    stat(uri: vscode.Uri): vscode.FileStat {
        const previous = this.fileStatMap.get(uri.fsPath);
        if (!previous) {
            const stat: vscode.FileStat = {
                type: vscode.FileType.File,
                ctime: Date.now(),
                mtime: Date.now(),
                size: 0,
                permissions: this.opts.readonly ? vscode.FilePermission.Readonly : undefined,
            };
            this.fileStatMap.set(uri.fsPath, stat);
            return stat;
        }
        return previous;
    }

    readFile(uri: vscode.Uri): Uint8Array | Thenable<Uint8Array> {
        const content = this.fileContentMap.get(uri.fsPath) || '';
        return new Uint8Array(Buffer.from(content));
    }

    writeFile(uri: vscode.Uri, content: Uint8Array): void | Thenable<void> {
        this.fileContentMap.set(uri.fsPath, content.toString());
        this._emitter.fire([{type: vscode.FileChangeType.Changed, uri}]);
    }

    readDirectory() {
        return [];
    }

    delete() {}
    rename() {}
    createDirectory() {}
    dispose() {}

    lockAsReadonly(uri: vscode.Uri) {
        const previous = this.fileStatMap.get(uri.fsPath)!;
        this.fileStatMap.set(
            uri.fsPath,
            {...previous, mtime: Date.now(), permissions: vscode.FilePermission.Readonly}
        );
    }

    unlock(uri: vscode.Uri) {
        const previous = this.fileStatMap.get(uri.fsPath)!;
        this.fileStatMap.set(
            uri.fsPath,
            {...previous, permissions: undefined}
        );
    }

    updateContent(uri: vscode.Uri, newContent: string, stream: boolean) {
        if (stream) {
            setTimeout(
                () => {
                    // 不断更新的diff的同时，会更新文件的mtime，并生成一个3.5s的延迟函数
                    // 该函数会在3秒后判断, 在这个时间内函数有没有被写入内容，如果没有就还原成可写，反之一直只读
                    const stat = this.fileStatMap.get(uri.fsPath)!;
                    if (Date.now() - stat.mtime > 3000) {
                        this.unlock(uri);
                        this._emitter.fire([{type: vscode.FileChangeType.Changed, uri}]);
                    }
                },
                3500
            );
            this.lockAsReadonly(uri);
        }
        this.fileContentMap.set(uri.fsPath, newContent);
        this._emitter.fire([{type: vscode.FileChangeType.Changed, uri}]);
    }
}

export const virtualFileSystemOriginalImplenment = new VirtualFileSystem({readonly: true});
export const virtualFileSystemModifiedImplenment = new VirtualFileSystem({readonly: false});

const removeIllegalWindowsPathDiskPrefix = (path: string) =>
    path.replace(/^file:\/\/\/?/, '').replace(/^[a-zA-Z]:/, '');
const formatWin32PathSep = (unknownPath: string) => {
    return unknownPath.replace(/\\/g, '/');
};

const parseUnknownFilePath = flow([
    removeIllegalWindowsPathDiskPrefix,
    formatWin32PathSep,
]);

export const createOriginalUri = (path: string, query?: string) => {
    return vscode.Uri.from({
        scheme: COMATE_COMPOSRER_DIFF_SCHEME_1,
        // 去除windows: C:\ 这类路径，该类路径不支持自定义file scheme
        path: parseUnknownFilePath(path),
        query,
    });
};
export const createModifedUri = (path: string, query?: string) => {
    return vscode.Uri.from({
        scheme: COMATE_COMPOSRER_DIFF_SCHEME_2,
        path: parseUnknownFilePath(path),
        query,
    });
};
