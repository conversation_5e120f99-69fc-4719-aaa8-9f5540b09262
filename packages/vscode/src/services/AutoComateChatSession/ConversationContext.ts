import {randomUUID} from 'crypto';
import {SlashType} from '@shared/constants';
import {Container} from 'inversify';
import {
    AutoWorkKnowledgeType,
    ContextType,
    FeedbackOptions,
    KnowledgeList,
    MessageExtraReplyMessage,
    Metadata,
    TaskStatus,
    UserMessage,
    CodeSelection,
} from '@shared/protocols';
import * as vscode from 'vscode';
import axios, {CancelTokenSource} from 'axios';
import {vscodeCommands} from '@/utils/vscodeComands';
import {ACCEPTANCE_TYPE, acceptCode} from '@/api';
import {getCompleteFirstLine, getCurrentSelection} from '@/utils/document';
import {stripExtraIndent} from '@/utils/indent';
import {addMarkdownCodeBlock} from '@/utils/common';
import {error, info} from '@/common/outputChannel';
import {CodeChunk, CodeChunkType} from '@/common/types';
import {PerformanceLogProvider} from '../PerformanceLogProvider';
import {PromptTemplateProvider} from '../PromptTemplateProvider';
import {adopt, feedback} from './api';
import {InternalSession} from './ChatSessionsManager';
import {getAutoWorkChatCurrentFileContext} from './utils';

export default class AutoWorkConversationContext {
    loginName: string = '';
    repoId: string | undefined;
    agent: string = '';
    query: string = '';
    slash: SlashType = SlashType.ASK_V2;
    contexts: KnowledgeList[] = [];
    metadata: Metadata | undefined;
    chatIntentRecognition: boolean = false;
    customizedPrompt: string | undefined = '';
    /** `AutoWork-Ask`: 正常不带思考的对话, `Autowork-IntentAsk`意图识别的对话, `AutoWork-KnowledgeAsk`手动选择#知识的对话 */
    performanceLogSkillType: 'AutoWork-Ask' | 'AutoWork-IntentAsk' | 'AutoWork-KnowledgeAsk' = 'AutoWork-Ask';
    adoptUuid: string | undefined;
    replyMessagesForPromptTemplate: Map<number, MessageExtraReplyMessage> = new Map();
    readonly currentFile: string | undefined;
    readonly sessionId = randomUUID();
    readonly cancelToken: CancelTokenSource;
    protected skipAnalyzeByComateAskFeature: boolean = false;
    readonly session: Partial<InternalSession> = {
        status: 'inProgress',
        ctime: Date.now(),
    };
    private readonly container: Container;
    private createdTime: number | undefined;
    messageId: number | undefined;
    active: boolean = true;
    /** 是否禁用自动携带划选代码作为提问上下文 */
    disableCode?: boolean;

    constructor(ctx: Container, cancelToken?: CancelTokenSource) {
        if (cancelToken) {
            cancelToken.cancel();
        }

        this.container = ctx;
        const activeEditorUri = vscode.window.activeTextEditor?.document.uri!;
        this.currentFile = activeEditorUri ? vscode.workspace.asRelativePath(activeEditorUri) : '';
        this.cancelToken = axios.CancelToken.source();
        // 仅到 contexts 有当前文件时，增加selection
    }

    setLoginName(loginName: string) {
        this.loginName = loginName;
        return this;
    }

    setRepoId(repoId: string | undefined) {
        this.repoId = repoId;
        return this;
    }

    setQuery(query: string) {
        this.query = query;
        return this;
    }

    setAgent(agent: string) {
        this.agent = agent;
        return this;
    }

    setSlash(slash: SlashType) {
        this.slash = slash;
        return this;
    }

    setChatType(chatType: typeof this['performanceLogSkillType']) {
        this.setStartTime();
        this.performanceLogSkillType = chatType;
    }

    setSkipAnalyze(skipAnalyze: boolean) {
        this.skipAnalyzeByComateAskFeature = skipAnalyze;
        return this;
    }

    get skipAnalyze() {
        return this.skipAnalyzeByComateAskFeature && !this.chatIntentRecognition;
    }

    setContexts(contexts: KnowledgeList[]) {
        this.contexts = contexts;
        return this;
    }

    setMetadata(metadata: Metadata | undefined) {
        this.metadata = metadata;
        return this;
    }

    maxCodeChunkToken = 80000;
    setMaxCodeChunkToken(limit: number) {
        this.maxCodeChunkToken = limit;
        return this;
    }

    setChatIntentRecognition(chatIntentRecognition: boolean) {
        this.chatIntentRecognition = chatIntentRecognition;
        return this;
    }

    get conversationId() {
        return this.session.conversationId;
    }

    get filePathsContext() {
        return this
            .contexts
            .filter(item => [ContextType.FILE, ContextType.FOLDER].includes(item.type))
            .map(item => {
                return item.id === AutoWorkKnowledgeType.CURRENT_FILE && this.currentFile!
                    ? vscode.workspace.asRelativePath(this.currentFile)
                    : item.id;
            });
    }

    get currentFileContext() {
        if (this.contexts.some(item => item.type === ContextType.CURRENT_FILE)) {
            return getAutoWorkChatCurrentFileContext();
        }
        return undefined;
    }

    get webKnowledgeContext() {
        return this.contexts.filter(item => item.type === 'WEB');
    }

    get codeSelections(): CodeChunk[] {
        const codeSelectionContext = this.contexts.filter(item => item.type === 'CODE_SELECTION') as CodeSelection[];
        const rootPath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        return codeSelectionContext.map(item => {
            return {
                repo: rootPath || '',
                type: 'selection' as CodeChunkType,
                path: item.filePath,
                content: item.content || '',
                contentStart: {line: item.selection[0], column: 0},
                contentEnd: {line: item.selection[1], column: 0},
            };
        });
    }

    get knowledgeContext() {
        const knowledgeType = [
            ContextType.FILE,
            ContextType.CURRENT_FILE,
            ContextType.WEB,
            ContextType.CODE,
            ContextType.TERMINAL,
        ];
        return this.contexts.filter(item => !knowledgeType.includes(item.type));
    }

    get prompt() {
        if (this.slash === SlashType.AUTO_DEBUG && this.selectedCode) {
            return `${this.query}\n${this.selectedCode}`;
        }
        return this.query;
    }

    setStartTime() {
        this.createdTime = Date.now();
    }

    private getCreatedTimeAndDestroy() {
        const createdTime = this.createdTime;
        this.createdTime = undefined;
        return createdTime!;
    }

    hasMessageId(messageId: number) {
        return this.messageId === messageId;
    }

    setMessageId(messageId: number) {
        this.messageId = messageId;
        return this;
    }

    activate(status: boolean) {
        this.active = status;
        return this;
    }

    setDisableCode(disableCode?: boolean) {
        this.disableCode = disableCode;
        return this;
    }

    setAdoptUuid(uuid: string | undefined) {
        if (uuid) {
            this.adoptUuid = uuid;
        }
        return this;
    }

    setCustomizedPrompt(prompt: string | undefined) {
        this.customizedPrompt = prompt;
        return this;
    }

    get actions() {
        return {
            regenerate: (messageId: number) => {
                vscodeCommands.askAutoWork(
                    messageId,
                    {
                        query: this.query,
                        prompt: this.customizedPrompt,
                        agent: this.agent,
                        slash: this.slash,
                        skipAnalyze: this.skipAnalyze,
                        chatIntentRecognition: this.chatIntentRecognition,
                        knowledgeList: this.contexts,
                        // 之前的逻辑都没有给regenate传递metadata，这里先给saas的自定义prompt场景下的补上
                        metadata: this.metadata?.prompt ? this.metadata : undefined,
                    }
                );
            },
            // 打开全文复制按钮
            copyAll: () => {},
            feedback: async (options: FeedbackOptions) => {
                try {
                    if (!this.session.messageId || !this.session.conversationId) {
                        return;
                    }
                    const res = await feedback({
                        ...options,
                        loginName: this.loginName,
                        messageId: this.session.messageId,
                        conversationId: this.session.conversationId,
                    });
                    if (res.data.code !== 200) {
                        throw new Error(res.data.message);
                    }
                }
                catch (ex) {
                    error('AutoComate feedback error:', (ex as Error).message);
                }
            },
            copy: (generatedContent: string) => {
                vscode.env.clipboard.writeText(generatedContent);
                if (!this.adoptUuid) {
                    return;
                }
                acceptCode({
                    accepted: true,
                    content: '',
                    uuid: this.adoptUuid,
                    generatedContent,
                    acceptanceInfo: {
                        row: 0,
                        col: 0,
                        originContent: '',
                        acceptanceContent: generatedContent,
                        acceptanceType: ACCEPTANCE_TYPE.COPY,
                    },
                });
                adopt({adoptionUuid: this.adoptUuid, slash: this.slash, contentStr: generatedContent}, this.loginName);
            },
        };
    }

    cancel() {
        this.active = false;
        this.cancelToken.cancel();
        if (this.session?.status === 'inProgress') {
            this.session.status = 'canceled';
            for (const task of this.session.tasks || []) {
                if (task.status === TaskStatus.PROCESSING) {
                    task.status = TaskStatus.INIT;
                }
            }
        }
        return this;
    }

    get selectedCode() {
        const resourceCode = this.contexts.find(({type}) => type === 'CODE')?.content;
        const [selectedCode, languageId] = getCurrentSelection();
        const editor = vscode.window.activeTextEditor;
        const firstLine = editor ? getCompleteFirstLine(editor.document, editor.selection) : undefined;
        const context = selectedCode
            ? addMarkdownCodeBlock(stripExtraIndent(selectedCode, false, firstLine), languageId)
            : undefined;
        const code = this.disableCode ? undefined : (resourceCode || context);
        return code;
    }

    setValue<T extends keyof InternalSession>(field: T, value: InternalSession[T]) {
        this.session[field] = value;
        return this;
    }

    trace(label: string) {
        info(`conversation: id=${this.conversationId} ${label}`);
    }

    logFirstTokenPerformance() {
        const startTime = this.getCreatedTimeAndDestroy();
        if (startTime) {
            this.container.get(PerformanceLogProvider).log({
                plugin: 'comate',
                skill: this.performanceLogSkillType,
                duration: Date.now() - startTime,
                type: 'first-token',
            });
        }
    }

    async setReplyMessagesForPromptTemplate(
        messageId: number,
        {content, id, slash}: Pick<UserMessage, 'content' | 'id' | 'slash'>
    ) {
        const shouldSave = await this.container.get(PromptTemplateProvider).comparePrompt({content});
        if (shouldSave) {
            this.replyMessagesForPromptTemplate.set(messageId, {
                id,
                content,
                slash,
            });
        }
    }
}
