/* eslint-disable @typescript-eslint/init-declarations */
/* eslint-disable max-len */
/* eslint-disable max-statements, max-lines, complexity */
import {LazyServiceIdentifer, inject, injectable} from 'inversify';
import * as vscode from 'vscode';
import {flatten, uniq, uniqBy, compact, flow} from 'lodash';
import {EventMessage, AutoWorkAPIProxyCollection, TaskStatus} from '@shared/protocols';
import {SlashType} from '@shared/constants';
import {VSCodeConfigProvider} from '@/services/ConfigProvider';
import {isSaaS, isInternal} from '@/utils/features';
import {getDeviceUUIDThrottled} from '@/utils/deviceUUID';
import {L10n} from '@/common/L10nProvider/L10n';
import {AutoWorkText} from '@/common/L10nProvider/constants';
import {spreadAskAutoWorkParameters} from '@/utils/vscodeComands';
import {debug, error} from '@/common/outputChannel';
import {uniformEOL<PERSON>har} from '@/utils/terminal';
import {iocContainer} from '@/iocContainer';
import {ACCEPTANCE_TYPE, acceptCode} from '@/api';
import consoleLogger from '../../common/consoleLogger';
import {CodeChunk} from '../../common/types';
import {UserService} from '../UserService';
import {ChatViewProvider} from '../ChatViewProvider';
import {getResultWithContent} from '../EmbeddingsService/utils';
import {
    partitionPathsByType,
    getCodeChunksFromFilesAndDirs,
    mergeCodeChunks,
    acceptMatchedCodeBlock,
    getVSCodeDocumentByUri,
    getDocumentReplaceRange,
} from '../../utils/files';
import {Action, ChatBaseProvider} from '../ChatBaseProvider';
import {chatActionDelayedReporter} from '../ChatViewProvider/chatActionDelayedReporter';
import {DiffProvider} from '../DiffProvider';
import {TreeSitterProvider} from '../TreeSitterProvider';
import {CustomizeProvider} from '../CustomizeProvider';
import {PerformanceLogProvider} from '../PerformanceLogProvider';
import {computeRepoId} from '../EmbeddingsService/embeddingUtils';
import {EmbeddingsController} from '../EmbeddingsService/controller';
import {AutoComateConversation} from './Conversation';
import {
    AutoComateTask,
    CodeSearchResultEvent,
} from './types';
import {
    mapSessionToMessage,
    getNeedQueryRewrite,
    getNeedCodeSearch,
    getTaskDesc,
    splitKeywordSearchResult,
    constructArchitectureCodeChunk,
    getAutoWorkWebKnowledgeContext,
    getAutoWorkDocContext,
    getAutoWorkChatGithubRepoContext,
    getAutoWorkChatCurrentRepoContext,
    getAutoWorkChatFileAndFolderContext,
    getAutoWorkTerminalContext,
} from './utils';
import {
    CMD_START_AUTO_WORK_SESSION,
} from './constants';
import {
    apis,
    analyze,
    search,
    chatStreamCode,
    adopt,
    AnalyzeData,
    Context,
    SearchData,
    WebContext,
    TextContext,
    AutoDebugContext,
    KnowledgeChunk,
    cancelChatStream,
    modifyIntentCurrentFileStrategyContextPath,
    AnalyzeServerError,
} from './api';
import {searchKeywords} from './tools';
import {isAutoDebugCapability} from './utils/is';
import {autoDebugSearchTask} from './autoDebugSearchTask';
import AutoWorkConversationContext from './ConversationContext';
import {adaptIntentStrategySearch2Original} from './searchCodeTask/intentSearchTask';

@injectable()
export class NewAutoComateChatSession extends ChatBaseProvider implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];
    private readonly conversationMap: Map<string, AutoComateConversation> = new Map();

    constructor(
        @inject(UserService) private readonly userService: UserService,
        @inject(new LazyServiceIdentifer(() => ChatViewProvider)) private readonly chatViewProvider: ChatViewProvider,
        @inject(EmbeddingsController) private readonly embeddingsController: EmbeddingsController,
        @inject(VSCodeConfigProvider) private readonly vsCodeConfig: VSCodeConfigProvider,
        @inject(DiffProvider) readonly diffProvider: DiffProvider,
        @inject(VSCodeConfigProvider) readonly configProvider: VSCodeConfigProvider,
        @inject(TreeSitterProvider) readonly treeSitterProvider: TreeSitterProvider,
        @inject(CustomizeProvider) readonly customizeProvider: CustomizeProvider,
        @inject(PerformanceLogProvider) readonly performanceLog: PerformanceLogProvider
    ) {
        super(diffProvider);
        this.disposables.push(
            vscode.commands.registerCommand(
                CMD_START_AUTO_WORK_SESSION,
                (...args) => {
                    this.askAutoComate(this.getConversation(this.chatViewProvider.sessionUuid), ...args);
                }
            )
        );
        this.disposables.push(
            // this.connection,
            this.chatViewProvider.onDidClearSession(() => {
                for (const context of this.chatViewProvider.generatedConversationContexts) {
                    context.cancel();
                }
                this.chatViewProvider.generatedConversationContexts.splice(
                    0,
                    this.chatViewProvider.generatedConversationContexts.length
                );
            })
        );

        chatViewProvider.appendUnAttachedWebViewListener({
            action: EventMessage.AutoWorkAPIProxy,
            listener: async <K extends keyof AutoWorkAPIProxyCollection>({
                api: apiName,
                params,
            }: {api: K, params: AutoWorkAPIProxyCollection[K]['params']}) => {
                const request = apis[apiName];
                if (typeof request === 'function') {
                    const userName = await this.getUserName();
                    const response = await request({...params, userName});
                    return response;
                }
                return null;
            },
        });
    }

    get conversationContext() {
        // 后面看看要不要改成 findLast, 有容错一些
        return this.chatViewProvider.generatedConversationContexts.find(context => context.active);
    }

    getConversation(sessionUuid: string) {
        let conversation = this.conversationMap.get(sessionUuid);

        if (!conversation) {
            conversation = this.chatViewProvider.createAutoComateConversation(sessionUuid);
            this.conversationMap.set(sessionUuid, conversation);
            this.disposables.push(
                conversation.onDidCancelResponse((messageId: number) => {
                    const context = this.chatViewProvider.generatedConversationContexts.find(context =>
                        context.hasMessageId(messageId)
                    );
                    if (context) {
                        const serverMessageId = context.session.messageId!;
                        this.getUserName().then(userName => cancelChatStream({messageId: serverMessageId, userName}));
                        this.sendCancelPayload();
                    }
                })
            );
        }

        return conversation;
    }
    private async getUserName() {
        const [userName] = await this.userService.getCurrentUser();
        const license = this.vsCodeConfig.getLicense();
        return isInternal ? userName : license;
    }

    private async getIndexReminder() {
        const workspaces = vscode.workspace.workspaceFolders;
        const rootPath = workspaces?.[0]?.uri.fsPath;
        if (!rootPath) {
            return undefined;
        }

        const {progress} = await this.embeddingsController.getProgress(rootPath);
        if (progress < 80) {
            return L10n.t(AutoWorkText.INDEXING_REMINDER, String(progress));
        }
        return undefined;
    }

    private sendCancelPayload() {
        // this.conversationContext?.cancel();
        // 如果有正在执行的resolve函数 取消它
        this.conversationContext?.cancelToken.cancel();
    }

    private rerenderChatMessage(conversation: AutoComateConversation, context?: AutoWorkConversationContext) {
        if (!context?.active) {
            return;
        }
        const messageUpdates = mapSessionToMessage(context.session);
        const messageId = context.messageId;
        if (typeof messageId !== 'number') {
            return;
        }
        const replyMessage = context.replyMessagesForPromptTemplate.get(messageId);
        if (replyMessage) {
            if (messageUpdates.extra) {
                messageUpdates.extra.replyMessage = replyMessage;
            }
            else {
                messageUpdates.extra = {
                    replyMessage,
                };
            }
            context.replyMessagesForPromptTemplate.delete(messageId);
        }
        conversation.updateResponse(messageId, messageUpdates);
    }
    private async computeRepoId() {
        const workspaces = vscode.workspace.workspaceFolders;
        const rootPath = workspaces?.[0]?.uri.fsPath;
        if (!rootPath) {
            return undefined;
        }
        const repoId = await computeRepoId(rootPath);
        return repoId;
    }

    private async getArchitectureCodeChunk(queryType?: string) {
        const workspaces = vscode.workspace.workspaceFolders;
        const rootPath = workspaces?.[0]?.uri.fsPath;
        if (!rootPath) {
            return [];
        }
        const repoId = await this.computeRepoId();
        if (!repoId) {
            return [];
        }
        // 用户query类型是架构相关，或者当前代码库未构建向量时，为模型提供手动拼接的架构相关知识帮助理解和回答
        if (queryType === 'architecture') {
            const architectureCodeChunk = await constructArchitectureCodeChunk(rootPath);
            return architectureCodeChunk;
        }
        return [];
    }

    private async updateMessageTasks(
        conversation: AutoComateConversation,
        taskType: AutoComateTask['taskType'],
        status: AutoComateTask['status']
    ) {
        if (!this.conversationContext?.active) {
            consoleLogger.error('no activeSessionId is set yet');
            return;
        }
        const session = this.conversationContext.session;
        if (!session) {
            consoleLogger.error('no session is found for activeSessionId');
            return;
        }
        const tasks = session.tasks || [];
        const slash = this.conversationContext.slash;
        const prompt = this.conversationContext.query;
        let updated = tasks.map(task => (task.taskType === taskType ? {...task, status, taskType} : task));
        if (!tasks.some(task => task.taskType === taskType)) {
            const newTask = {
                id: tasks.length + 1,
                messageId: session.messageId!,
                taskType,
                status,
                desc: getTaskDesc(taskType, prompt, slash),
            };
            updated = [...updated, newTask];
        }
        this.conversationContext.setValue('tasks', updated);
        if (status === TaskStatus.FAIL) {
            this.conversationContext.setValue('status', 'failed');
        }
        this.rerenderChatMessage(conversation, this.conversationContext);
    }

    private async updateMessageContent(conversation: AutoComateConversation, content?: string) {
        if (!this.conversationContext?.active) {
            consoleLogger.error('no activeSessionId is set yet');
            return;
        }
        const session = this.conversationContext.session;
        if (!session) {
            consoleLogger.error('no session is found for activeSessionId');
            return;
        }
        this.conversationContext.setValue('summary', content);
        this.rerenderChatMessage(conversation, this.conversationContext);
    }

    async updateMessageWithAnswer(
        conversation: AutoComateConversation,
        conversationContext: AutoWorkConversationContext,
        data: Partial<CodeSearchResultEvent['detail']>,
        conversationEvents: Pick<ReturnType<typeof this.createConversationEventProcessor>, 'onConversationStageChange'>
    ) {
        const {onConversationStageChange} = conversationEvents;
        if (!conversationContext?.active) {
            consoleLogger.error('no activeSessionId is set yet');
            return;
        }
        const session = conversationContext.session;
        if (!session) {
            consoleLogger.error('no session is found for activeSessionId');
            return;
        }
        const messageId = conversationContext.messageId;
        if (!messageId) {
            consoleLogger.error('no messageId is found for activeSessionId');
            return;
        }
        const message = conversation.getMessageById(messageId);
        if (!message) {
            return;
        }

        /** 处理思考过程的数据开始 */
        if (data.reasoningEnd === false && data.reasoningSummary) {
            conversationContext.setValue('reasoningSummary', data.reasoningSummary);
            conversationContext.setValue('reasoningStatus', 'inProgress');
        }

        if (data.reasoningEnd === true && conversationContext.session?.reasoningStatus === 'inProgress') {
            conversationContext.setValue('reasoningStatus', 'success');
        }
        /** 处理思考过程的数据结束 */

        if (data.end) {
            const loginName = await this.getUserName();
            const slash = conversationContext.slash;
            /** 第三方业务需要单独统计数据，对采纳内容做定制化处理 */
            const withThirdPartyTracker = <T extends any[]>(fn?: (...args: T) => any) => (...args: T) => {
                if (!data.adoptionUuid || !slash) {
                    return;
                }
                fn?.(...args);
                adopt({adoptionUuid: data.adoptionUuid, slash, contentStr: args[0]}, loginName);
            };

            const chatResponseProxy = {
                getMessageId: () => String(message.id),
                getChatId: () => String(message.id),
                getMessageContent: () => message.content,
                getTrackUuid: () => data.adoptionUuid || message.extra?.uuid,
                updateMessage: () => conversation.updateResponse(messageId, {}),
            };
            const {newFile, insertIntoTerminal, feedback} = this.extraActions(chatResponseProxy);
            const autoDebugActions = this.defaultActions(chatResponseProxy, undefined, undefined, {
                ignoreSmartApplyFeature: true,
            });
            const defaultActions = this.defaultActions(chatResponseProxy);
            const regenerate = conversationContext.actions.regenerate as any;
            const messageActions: Record<string, Action> = isAutoDebugCapability(message)
                ? {
                    ...autoDebugActions,
                    feedback,
                    regenerate,
                    // 打开全文复制按钮
                    copyAll: () => {},
                    copy: withThirdPartyTracker(autoDebugActions.copy),
                    insertIntoTerminal: withThirdPartyTracker(insertIntoTerminal),
                }
                : {...defaultActions, feedback, regenerate, copyAll: () => {}, newFile, insertIntoTerminal};

            const renderJSX = !!data.summaryJSXFormat;
            if (renderJSX) {
                const {diff, replaceToFile} = (() => {
                    // 创建一个闭包，主要是为了处理用户在查看变更时对diff进行调整后采纳场景
                    // 具体的实现逻辑在diffProvider里，大致是查看变更会生成一个comate-diff:的文件，查看变更时对变更行可以进行放弃，放弃后采纳那去除这个变更
                    // 如果是直接点采纳，就走正常的替换逻辑
                    let acceptWithDiff: ((text: string) => any) | undefined;
                    const diff: Action = async (_content, _language, extra) => {
                        try {
                            const editor = await getVSCodeDocumentByUri(extra!.filePath);
                            const range = getDocumentReplaceRange(editor.document, uniformEOLChar(extra!.from))
                                || getDocumentReplaceRange(editor.document, extra!.from);
                            if (!range) {
                                throw new Error('range not found!');
                            }
                            const {accept, diff} = this.diffProvider.createReplacementDiffHandler(
                                editor.document,
                                range,
                                () => uniformEOLChar(extra!.to),
                                data.adoptionUuid,
                                undefined,
                                withThirdPartyTracker()
                            );
                            acceptWithDiff = withThirdPartyTracker(accept);
                            await diff(uniformEOLChar(extra!.to));
                        }
                        catch (ex) {
                            acceptWithDiff = undefined;
                            error(`diffFile error: ${(ex as any).message}`);
                            vscode.window.showErrorMessage('原文件内容已经发生变化，无法查看变更');
                        }
                    };
                    const replaceToFile: Action = async (_content, _language, extra) => {
                        const {filePath, from, to} = extra!;
                        try {
                            if (acceptWithDiff) {
                                await acceptWithDiff(uniformEOLChar(extra!.to));
                            }
                            else {
                                acceptCode({
                                    accepted: true,
                                    content: '',
                                    uuid: data.adoptionUuid!,
                                    acceptanceInfo: {
                                        originContent: from,
                                        row: 0,
                                        col: 0,
                                        acceptanceContent: to,
                                        acceptanceType: ACCEPTANCE_TYPE.REPLACE,
                                    },
                                });
                                await acceptMatchedCodeBlock(filePath, uniformEOLChar(from), uniformEOLChar(to));
                            }
                        }
                        catch (ex) {
                            error(`replaceToFile error: ${(ex as any).message}`);
                            vscode.window.showErrorMessage('原文件内容已经发生变化，无法采纳变更');
                        }
                        finally {
                            acceptWithDiff = undefined;
                        }
                    };
                    return {diff, replaceToFile};
                })();
                messageActions.diff = diff;
                messageActions.replaceToFile = withThirdPartyTracker(replaceToFile);
            }
            chatActionDelayedReporter.execute(data.adoptionUuid!);
            const content = data.summaryJSXFormat || data.summary;
            // 如果没有思考阶段就不用更新
            if (session.tasks?.length) {
                onConversationStageChange('ANSWER', TaskStatus.SUCCEED);
            }
            conversationContext.setValue('status', 'success');
            conversationContext.setAdoptUuid(data.adoptionUuid);
            conversation.updateResponse(
                messageId,
                {
                    content: data.summaryJSXFormat || data.summary,
                    extra: {renderJSX: !!data.summaryJSXFormat},
                    actions: messageActions,
                    conversationId: conversationContext?.session?.conversationId,
                }
            );
            this.updateMessageContent(conversation, content);
            return;
        }
        conversationContext.logFirstTokenPerformance();
        conversationContext.setValue('knowledgeChunks', data.knowledge);
        conversationContext.setValue('webChunks', data.web);
        conversationContext.setValue('codeChunks', data.data);
        conversationContext.setAdoptUuid(data.adoptionUuid);
        this.updateMessageContent(conversation, data.summary);
    }

    private updateRelevantFiles(conversation: AutoComateConversation, codeChunks: CodeChunk[], paths: string[]) {
        if (!this.conversationContext?.active) {
            return;
        }
        const relevantFiles = codeChunks.map(item => item.path);
        const existing = this.conversationContext.session.relevantFiles || [];
        const updated = [...existing, ...relevantFiles];
        const updatedByPath = paths.length === 0
            ? updated
            // 只有前面指令了paths才需要返回
            : uniq(updated.filter(file => paths.some(path => file.includes(path))));
        this.conversationContext.setValue('relevantFiles', updatedByPath);
        this.rerenderChatMessage(conversation, this.conversationContext);
    }

    private localKeywordSearch(
        needCodeSearch: boolean,
        queries: string[],
        paths: string[],
        onConversationFileChange?: (codeChunks: CodeChunk[], paths: string[]) => void
    ): Promise<Array<{keyword: string, codeChunks: CodeChunk[]}>> {
        if (!needCodeSearch) {
            return new Promise(resolve => {
                resolve([]);
            });
        }

        const keywordSearchPromise = searchKeywords(queries);
        keywordSearchPromise.then(res => {
            const codeChunks = res.map(item => item.codeChunks);
            onConversationFileChange?.(flatten(codeChunks), paths);
        });
        return keywordSearchPromise;
    }

    private async autoDebugSearch(analyze: AnalyzeData, repoId?: string): Promise<CodeChunk[]> {
        const rootPath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!rootPath || !repoId || !analyze.extend?.context) {
            return [];
        }

        if (Array.isArray(analyze.searchStrategies)) {
            const chunks = await Promise.all(analyze.searchStrategies.map(
                strategy =>
                    autoDebugSearchTask(
                        repoId,
                        this.treeSitterProvider,
                        strategy.context as AutoDebugContext
                    )
            ));
            debug('autoDebugSearchTask', JSON.stringify(chunks));
            return chunks.filter(chunk => chunk !== null) as CodeChunk[];
        }
        return [];
    }

    async autoWorkAnalyze(
        conversationContext: AutoWorkConversationContext,
        needQueryRewrite: boolean,
        contexts: Array<Context | WebContext | TextContext>,
        conversationEvent: ReturnType<typeof this.createConversationEventProcessor>
    ) {
        const {onConversationStageChange, onConversationContentChange} = conversationEvent;
        const slash = conversationContext.slash;
        const selectedCode = conversationContext.selectedCode;
        const query = conversationContext.customizedPrompt || conversationContext.prompt;
        const cancelToken = conversationContext.cancelToken.token;
        const loginName = conversationContext.loginName;
        try {
            onConversationStageChange('THOUGHT', TaskStatus.INIT);
            onConversationStageChange('THOUGHT', TaskStatus.PROCESSING);
            const hasSelectedCode = slash === SlashType.IAPI && selectedCode;
            conversationContext.trace('start analyze');
            const repoId = await this.computeRepoId();
            const result = await analyze(
                {
                    query,
                    slash,
                    needQueryRewrite,
                    conversationId: conversationContext.session.conversationId,
                    contexts,
                    ide: 'vscode',
                    intent: conversationContext.chatIntentRecognition,
                    repoId,
                    // 使用「API指令」时，若框选了代码，则提供 selection 字段
                    ...(hasSelectedCode ? {selection: selectedCode} : {}),
                },
                loginName,
                cancelToken
            );
            conversationContext.trace(`end analyze, result=${JSON.stringify(result.data.data)}`);
            // 这里做一些检索策略的后处理，比如把策略合并，重复过滤，参数调整
            const analyzeResult: AnalyzeData = flow([
                modifyIntentCurrentFileStrategyContextPath(conversationContext.currentFile!),
            ])(result.data.data);

            onConversationStageChange('THOUGHT', TaskStatus.SUCCEED);
            if (this.conversationContext?.active) {
                this.conversationContext.setValue('conversationId', analyzeResult.conversationId);
                this.conversationContext.setValue('messageId', analyzeResult.messageId);
            }
            return analyzeResult;
        }
        catch (ex) {
            if (ex instanceof AnalyzeServerError) {
                onConversationContentChange(ex.message);
            }
            onConversationStageChange('THOUGHT', TaskStatus.FAIL);
            onConversationContentChange((ex as Error).message);
            throw error;
        }
    }

    createConversationEventProcessor(conversation: AutoComateConversation) {
        const onConversationStageChange = (taskType: AutoComateTask['taskType'], status: AutoComateTask['status']) => {
            this.updateMessageTasks(conversation, taskType, status);
        };
        const onConversationFileChange = (codeChunks: CodeChunk[], paths: string[]) => {
            this.updateRelevantFiles(conversation, codeChunks, paths);
        };
        const onConversationContentChange = (text: string) => {
            this.updateMessageContent(conversation, text);
        };
        const onConversationAnswerChange = (
            conversationContext: AutoWorkConversationContext,
            data: Partial<CodeSearchResultEvent['detail']>
        ) => {
            this.updateMessageWithAnswer(
                conversation,
                conversationContext,
                data,
                {onConversationStageChange}
            );
        };
        return {
            onConversationStageChange,
            onConversationFileChange,
            onConversationContentChange,
            onConversationAnswerChange,
        };
    }

    async autoWorkSearch(
        conversationContext: AutoWorkConversationContext,
        analyze: AnalyzeData,
        contexts: Array<Context | WebContext | TextContext>,
        conversationEvent: ReturnType<typeof this.createConversationEventProcessor>
    ) {
        const query = conversationContext.prompt;
        const slash = conversationContext.slash;
        const repoId = conversationContext.repoId;
        const cancelToken = conversationContext.cancelToken.token;
        const paths = conversationContext.filePathsContext;
        const knowledge = conversationContext.knowledgeContext;
        const webKnowledge = conversationContext.webKnowledgeContext;
        const codeSelections = conversationContext.codeSelections;

        const {onConversationStageChange, onConversationFileChange} = conversationEvent;

        try {
            conversationContext.trace('start search');
            const searchPaths = paths.filter(path => path !== 'repo' && !path.startsWith('github'));
            const analyzeIntentStrategies = !!analyze.intentStrategies?.length;
            // 如果意图识别结果存在，就必然不走#repo的本地检索
            const needCodeSearch = await getNeedCodeSearch(conversationContext);
            const searchParams = {
                query,
                needCodeSearch,
                knowledge,
                analyze,
                contexts,
                slash,
                repoId,
                conversationId: analyze.conversationId,
                messageId: analyze.messageId,
            };
            if (conversationContext.slash === SlashType.AUTO_DEBUG) {
                onConversationStageChange('SEARCH', TaskStatus.PROCESSING);
                const codeChunks = await this.autoDebugSearch(analyze, repoId);
                onConversationStageChange('SEARCH', TaskStatus.SUCCEED);
                return {codeChunks: [...codeSelections, ...codeChunks]};
            }

            const skipSearchStep = !needCodeSearch
                && !knowledge.length
                && !webKnowledge
                && !paths.length;
            if (skipSearchStep) {
                conversationContext.trace('end search without result');
                return {codeChunks: codeSelections};
            }
            onConversationStageChange('SEARCH', TaskStatus.PROCESSING);
            // 这个分支代表没有选择#repo, 没有选择联网检索，没有选择文档类知识时，且所有paths的尺寸之和小于38000
            if (!analyzeIntentStrategies && !needCodeSearch && knowledge.length === 0 && !webKnowledge?.length) {
                conversationContext.trace('start search files');
                const [folders, files] = partitionPathsByType(paths.map(path => {
                    if (path === 'currentFile') {
                        return vscode.workspace.asRelativePath(conversationContext.currentFile!);
                    }
                    return path;
                }));

                const codeChunks = await getCodeChunksFromFilesAndDirs(files, folders);
                conversationContext.trace(`end search files, codeChunkLength=${codeChunks.length}`);
                onConversationStageChange('SEARCH', TaskStatus.SUCCEED);
                return {codeChunks: [...codeSelections, ...codeChunks]};
            }
            const loginName = await this.getUserName();
            const shouldSearchUseSelectedContext = needCodeSearch && !analyze.intentStrategies?.length;
            const queries = [query, ...analyze.codeSearchQueries || []];
            conversationContext.trace('start workspace search');
            const [
                embeddingSearchResults,
                keywordSearchResult,
                {keywordSearchResult: intentKeywordSearchResult, otherSearchResult: otherIntentSearchResult},
            ] = await Promise.all([
                search(searchParams, loginName, cancelToken),
                this.localKeywordSearch(shouldSearchUseSelectedContext, queries, searchPaths, onConversationFileChange),
                adaptIntentStrategySearch2Original(analyze.intentStrategies),
            ]);
            conversationContext.setValue('showGraph', !!embeddingSearchResults.data.data.showGraph);
            conversationContext.trace('end workspace search and start merge codeChunks');
            const [longKeywordCodeChunks, shortKeywordCodeChunks] = splitKeywordSearchResult([
                ...keywordSearchResult,
                ...intentKeywordSearchResult,
            ]);
            const rootPath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
            const codeChunksWithContent = await getResultWithContent(
                embeddingSearchResults.data.data.codeChunks,
                rootPath
            );
            const architectureCodeChunks = await this.getArchitectureCodeChunk(analyze.queryType);
            const data = [
                ...longKeywordCodeChunks,
                ...codeChunksWithContent,
                ...shortKeywordCodeChunks,
                ...architectureCodeChunks,
                ...otherIntentSearchResult,
            ];
            const filterByPathData = searchPaths.length === 0
                ? data
                : data.filter(({path: filePath}) => searchPaths.some(path => filePath.includes(path)));
            const dedupedData = uniqBy(
                filterByPathData.filter(item => !!item.path),
                item => `${item.path.replace(/^\.?\//, '')}-${item.contentStart.line}-${item.contentEnd.line}`
            );
            const mergedCodeChunks = await mergeCodeChunks(dedupedData);
            conversationContext.trace(`end merge codeChunks, mergedCodeChunks=${mergedCodeChunks.length}`);
            onConversationStageChange('SEARCH', TaskStatus.SUCCEED);
            return {codeChunks: [...codeSelections, ...mergedCodeChunks.slice(0, 100)]};
        }
        catch (error) {
            onConversationStageChange('SEARCH', TaskStatus.FAIL);
            debug('autoWorkSearch error', (error as Error).message);
            throw error;
        }
    }

    private async chat(
        conversationContext: AutoWorkConversationContext,
        analyzeResult: AnalyzeData,
        searchResult: SearchData & {knowledgeChunks?: KnowledgeChunk[]},
        contexts: Array<Context | WebContext | TextContext>,
        conversationEvent: ReturnType<typeof this.createConversationEventProcessor>
    ) {
        const {onConversationAnswerChange} = conversationEvent;
        let isReasoning = false;
        try {
            const query = conversationContext.customizedPrompt || conversationContext.prompt;
            const slash = conversationContext.slash;
            const repoId = conversationContext.repoId;
            const selectedCode = conversationContext.selectedCode;
            const metadata = conversationContext.metadata;
            const cancelToken = conversationContext.cancelToken;

            conversationContext.trace('start build chat stream');
            const hasSelectedCode = slash === SlashType.IAPI && selectedCode;
            const loginName = await this.getUserName();
            const params = {
                conversationId: analyzeResult.conversationId,
                messageId: analyzeResult.messageId,
                slash,
                query,
                repoId,
                contexts,
                securityEnhance: metadata?.secure,
                selfDefineInstruction: metadata?.prompt,
                queryType: analyzeResult.queryType,
                device: isSaaS ? await getDeviceUUIDThrottled() : undefined,
                codeChunks: searchResult.codeChunks,
                knowledgeChunks: searchResult.knowledgeChunks,
                analyze: analyzeResult,
                // 使用「API指令」or「自动化测试指令」时，若框选了代码，则提供 selection 字段
                ...(hasSelectedCode ? {selection: selectedCode} : {}),
            };

            conversationContext.trace('start chat stream');
            const {conversationId} = await chatStreamCode(
                params,
                (data: Partial<CodeSearchResultEvent['detail']>) => {
                    isReasoning = Boolean(data.reasoningSummary && !data.reasoningEnd);
                    onConversationAnswerChange(conversationContext, data);
                },
                loginName,
                undefined,
                cancelToken
            );

            conversationContext.trace('end chat stream');
            if (conversationId) {
                conversationContext.setValue('conversationId', conversationId);
            }
        }
        catch (ex) {
            if (isReasoning) {
                conversationContext.setValue('reasoningStatus', 'canceled');
            }
            throw ex;
        }
    }

    async autoWorkChat(
        conversationContext: AutoWorkConversationContext,
        analyzeResult: AnalyzeData,
        searchResult: SearchData & {knowledgeChunks?: KnowledgeChunk[]},
        contexts: Array<Context | WebContext | TextContext>,
        conversationEvent: ReturnType<typeof this.createConversationEventProcessor>
    ) {
        const {onConversationStageChange, onConversationContentChange} = conversationEvent;
        try {
            onConversationStageChange('ANSWER', TaskStatus.PROCESSING);
            await this.chat(conversationContext, analyzeResult, searchResult, contexts, conversationEvent);
        }
        catch (ex) {
            onConversationStageChange('ANSWER', TaskStatus.FAIL);
            onConversationContentChange((ex as Error).message || L10n.t(AutoWorkText.ERROR));
            throw ex;
        }
    }

    serializeContext(conversationContext: AutoWorkConversationContext) {
        const paths = conversationContext.filePathsContext;
        const webKnowledge = conversationContext.contexts.filter(item => item.type === 'WEB');
        const terminalContext = conversationContext.contexts.find(item => item.type === 'TERMINAL');
        const terminalSelectionContext = conversationContext.contexts.filter(item =>
            item.type === 'TERMINAL_SELECTION'
        );
        const knowledges = conversationContext.contexts.filter(item =>
            !['FILE', 'WEB', 'CODE', 'TERMINAL', 'CURRENT_FILE', 'CODE_SELECTION', 'TERMINAL_SELECTION'].includes(
                item.type
            )
        );

        this.getIndexReminder().then(reminder => {
            if (conversationContext?.active && reminder) {
                conversationContext.setValue('reminder', reminder);
            }
        });

        const language = this.configProvider.getDisplayLanguage();
        const contexts = compact([
            conversationContext.currentFileContext,
            ...getAutoWorkChatGithubRepoContext(paths),
            ...getAutoWorkChatFileAndFolderContext(paths),
            ...getAutoWorkWebKnowledgeContext(conversationContext.query, language, webKnowledge),
            ...getAutoWorkTerminalContext(terminalSelectionContext),
            ...getAutoWorkDocContext(knowledges),
        ]);

        if (conversationContext.repoId) {
            if (paths.includes('repo')) {
                contexts.push(getAutoWorkChatCurrentRepoContext(conversationContext.repoId));
            }
        }

        if (terminalContext) {
            contexts.push(terminalContext);
        }
        return contexts;
    }

    async resolve(conversation: AutoComateConversation, conversationContext: AutoWorkConversationContext) {
        const contexts = this.serializeContext(conversationContext);
        const conversationEvent = this.createConversationEventProcessor(conversation);
        const codeSelections = conversationContext.codeSelections;

        if (contexts.length === 0 && conversationContext.skipAnalyze) {
            conversationContext.trace(`start chat, query=${conversationContext.prompt}`);
            conversationContext.setChatType('AutoWork-Ask');
            try {
                await this.chat(
                    conversationContext,
                    {conversationId: conversationContext.session.conversationId} as AnalyzeData,
                    {codeChunks: codeSelections},
                    [],
                    conversationEvent
                );
            }
            catch (ex) {
                // const message = (ex as Error).message;
                // this.updateMessageContent(getGenerationFailureText(message));
            }
        }
        else {
            conversationContext.setChatType(
                conversationContext.chatIntentRecognition ? 'AutoWork-IntentAsk' : 'AutoWork-KnowledgeAsk'
            );
            conversationContext.trace(`start analyze chat, query=${conversationContext.prompt}`);
            try {
                const needQueryRewrite = await getNeedQueryRewrite(conversationContext);
                const analyzeResult = await this.autoWorkAnalyze(
                    conversationContext,
                    needQueryRewrite,
                    contexts,
                    conversationEvent
                );
                const searchResult = await this.autoWorkSearch(
                    conversationContext,
                    analyzeResult,
                    contexts,
                    conversationEvent
                );
                await this.autoWorkChat(
                    conversationContext,
                    analyzeResult,
                    searchResult,
                    contexts,
                    conversationEvent
                );
            }
            catch (ex) {
                // throw ex;
            }
        }

        conversationContext.cancel();
        this.rerenderChatMessage(conversation, conversationContext);
    }

    // 回头看看，agent 应该从 webview 继承过来
    async askAutoComate(conversation: AutoComateConversation, ...args: any[]) {
        const {
            regenMessageId,
            query,
            prompt,
            agent,
            slash,
            skipAnalyze,
            metadata,
            chatIntentRecognition,
            selectedResources,
            responseReminder,
            disableCode,
        } = spreadAskAutoWorkParameters(...args as Parameters<typeof spreadAskAutoWorkParameters>);

        const loginName = await this.getUserName();
        const repoId = await this.computeRepoId();

        // const messages = this.chatViewProvider.messages;
        // const latestAssistantMessage = findLast(messages, ({role}) => role === 'assistant');
        // const latestIsCodelens = codelensWhiteList.includes(latestAssistantMessage?.type ?? '');
        // const previousContext = this.chatViewProvider.getPreviousContextByMessageId(latestAssistantMessage?.id);
        // const previousConversationId = previousContext?.session.conversationId
        //     || latestAssistantMessage?.conversationId;

        const {
            latestIsCodelens,
            previousContext,
            previousConversationId,
        } = this.chatViewProvider.getPreviousConversationId();
        const conversationContext = new AutoWorkConversationContext(
            iocContainer,
            this.conversationContext?.cancelToken
        )
            .setLoginName(loginName)
            .setRepoId(repoId)
            .setQuery(query)
            .setAgent(agent)
            .setSlash(slash)
            .setContexts(selectedResources)
            .setSkipAnalyze(!!skipAnalyze)
            .setMetadata(metadata)
            .setChatIntentRecognition(!!chatIntentRecognition)
            .setCustomizedPrompt(prompt)
            .setDisableCode(disableCode);

        // 对以下两种情况支持多轮
        //  - 上次也是autowork对话
        //  - 上次是codelens，在统一conversation前用type判断
        if (slash === SlashType.ASK_V2 && (previousContext?.slash === SlashType.ASK_V2 || latestIsCodelens)) {
            conversationContext.setValue('conversationId', previousConversationId);
        }

        this.chatViewProvider.generatedConversationContexts.push(conversationContext);

        const actions = conversationContext.actions;

        if (regenMessageId) {
            const replyTo = conversation.getReplyToWhom(regenMessageId);
            if (typeof replyTo !== 'number') {
                throw new Error('Internal Error: unable to get to which message to reply when regenerate');
            }
            const message = conversation.addResponse(replyTo, actions, agent, slash, metadata);
            const newMessageId = message.id;
            conversationContext.setMessageId(newMessageId);
            const previousContext = this.chatViewProvider.getPreviousContextByMessageId(regenMessageId);
            const oldSessionId = previousContext?.sessionId;
            if (oldSessionId) {
                const {conversationId, messageId} = previousContext.session;
                if (conversationId != null && messageId != null) {
                    conversationContext.setValue('conversationId', conversationId);
                    conversationContext.setValue('messageId', messageId);
                }
                conversationContext.setValue('reminder', responseReminder);
                this.resolve(conversation, conversationContext);
                return;
            }
        }
        // 新增问题
        const queryMessageId = conversation.addQuery({
            content: query,
            code: slash === SlashType.AUTO_DEBUG ? conversationContext.selectedCode : undefined,
            slash,
            knowledgeList: selectedResources,
        });
        const message = conversation.addResponse(queryMessageId, actions, agent, slash, metadata);
        if (slash === SlashType.ASK_V2) {
            conversationContext.setReplyMessagesForPromptTemplate(message.id, {
                id: queryMessageId,
                content: query,
                slash,
            });
        }
        const responseMessageId = message.id;
        conversationContext.setMessageId(responseMessageId);
        conversationContext.setValue('reminder', responseReminder);
        this.resolve(conversation, conversationContext);
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
