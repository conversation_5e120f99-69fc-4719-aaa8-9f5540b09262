/* eslint-disable max-statements */
/* eslint-disable max-lines */
import {IncomingMessage} from 'http';
import path, {basename} from 'path';
import {readdirSync} from 'fs';
import * as vscode from 'vscode';
import {CancelToken, CancelTokenSource} from 'axios';
import {SSEProcessor, getCurrentUserKnowledgeSet} from '@comate/plugin-shared-internals';
import {
    AutoWorkAPIProxyCollection,
    ContextType,
    ExtensionConfig,
    Feature,
    FeedbackOptions,
    KnowledgeList,
} from '@shared/protocols';
import {flow, omit, pick} from 'lodash';
import {isNotJunk} from 'junk';
import {debug, error} from '@/common/outputChannel';
import {Position} from '@/common/types';
import {createAxios} from '@/api/common';
import {getUserConfig} from '@/api';
import {isInternal, isSaaS} from '@/utils/features';
import {getIdeName} from '@/common/Fetcher';
import {getExtensionContextAsync} from '@/utils/extensionContext';
import {accessTo} from '@/api/codeSearch';
import {getTraceRepoInfo} from '@/utils/trace';
import {CodeChunk} from '../../common/types';
import consoleLogger from '../../common/consoleLogger';
import {sleep} from '../../utils/common';
import {CHAT_MODEL_ID} from '../ModelSelector';
import {AutoComateResponse, CodeSearchResultEvent} from './types';
import {IntentStrategy, IntentStrategyRule} from './types/indentStrategy';
import {getAutoWorkChatCurrentFileContext} from './utils';
import {isArchitectureIntentStrategy, isCurrentFileStrategy} from './utils/is';

const axiosInstance = createAxios({});

interface Res<T> {
    code: number;
    data: T;
    message: string;
}

type APICollection = {
    [K in keyof AutoWorkAPIProxyCollection]: (
        params: AutoWorkAPIProxyCollection[K]['params'] & {userName: string}
    ) => Promise<AutoWorkAPIProxyCollection[K]['response']>;
};

interface BaseParams {
    userName: string;
}

export const uuapHeader = async (userName?: string) => {
    const version = (await getExtensionContextAsync()).extension.packageJSON.version;
    const name = userName ? encodeURIComponent(userName) : '';

    return {
        'X-Source': 'COMATE',
        'Content-Type': 'application/json',
        'login-name': name,
        'Uuap-login-name': name,
        'plugin-version': version ? `VsCode-${version}` : '',
    };
};

const service = {
    get: async <T, P = any>(url: string, {userName}: P & BaseParams) => {
        const res = await axiosInstance.get<Res<T>>(url, {headers: await uuapHeader(userName), proxy: false});
        return res.data.data;
    },
    post: async <T, P = any>(url: string, {userName, ...data}: P & BaseParams) => {
        const res = await axiosInstance.post<Res<T>>(url, data, {headers: await uuapHeader(userName), proxy: false});
        if (res.data.code !== 200) {
            throw new Error(res.data.message);
        }
        return res.data.data;
    },
    put: async <T, P = any>(url: string, {userName, ...data}: P & BaseParams) => {
        const res = await axiosInstance.put<Res<T>>(url, data, {headers: await uuapHeader(userName), proxy: false});
        if (res.data.code !== 200) {
            throw new Error(res.data.message);
        }
        return res.data.data;
    },
};

/** 不同的链接会对应不同的识别规则，每个规则的request需要返回uuid和title */
const isTest = $features.ENVIRONMENT === 'test';
const whitelist = {
    iapi: {
        rules: [
            new RegExp(`^https://iapi${isTest ? '-test' : ''}.baidu-int.com/web/project/[0-9]+$`),
            new RegExp(`^https://iapi${isTest ? '-test' : ''}.baidu-int.com/web/project/[0-9]+/apis/(.*)+$`),
        ],
        request: async (url: string, userName: string) => {
            try {
                const response = await service.post<
                    {displayName: string, uuid: string, type: string, recognizeResult: 'workspace' | 'knowledge'},
                    {url: string}
                >(
                    '/api/v2/api/aidevops/knowledge/rest/v1/url/recognize',
                    {url, userName}
                );
                const {displayName: title, uuid, recognizeResult} = response;
                // recognizeResult是workspace 即知识集，对应于API_PROJECT 即IAPI项目；recognizeResult是knowledge 即知识，对应于API 即IAPI单个接口；
                const type = recognizeResult === 'workspace' ? 'API_PROJECT' : 'API';
                // 链接识别会有两个返回值，一种是单个知识，只需要检索uuid，一种是知识集，需要检索对应type的workspace
                return {title, uuid: `${type}-${uuid}`};
            }
            catch (ex) {
                return {title: (ex as Error).message, uuid: ''};
            }
        },
    },
    default: {
        rules: [/.*/],
        request: async (url: string, userName: string) => {
            // headers 是为了绕过反爬策略
            const requestLinkTitle = axiosInstance
                .get(url, {headers: {'User-Agent': 'comate-autowork', proxy: false}})
                .then(res => {
                    const matched = /<title[^>]*>(.*[^<])<\/title>/.exec(res.data);
                    const title = matched?.[1];
                    if (title) {
                        return title;
                    }
                    const ogTitle = /<meta\s+[^>]*property="og:title"\s+content="([^"]*)"\s*\/>/.exec(res.data)?.[1];
                    const ogTitleWithoutEnter = ogTitle?.replace(/[\r\n]/mg, '');
                    return ogTitleWithoutEnter;
                })
                .catch();

            type KnowledgeResponse = AutoWorkAPIProxyCollection['generateLinkToUuid']['response'];
            const requestUuid = service.post<{knowledge: KnowledgeResponse}, {userName: string, link: string}>(
                '/api/v2/api/aidevops/knowledge/rest/v1/workspace/knowledge/byLink',
                {userName, link: url}
            );

            const [response, title] = await Promise.all([
                requestUuid,
                requestLinkTitle,
            ]);
            return {uuid: response.knowledge.uuid, title: title || url};
        },
    },
};

export const apis: APICollection = {
    /** 把链接转换成知识的uuid */
    generateLinkToUuid: async ({userName, ...data}) => {
        const reqeustUrlInfo = (url: string) => {
            if (!isInternal) {
                return whitelist.default.request(url, userName);
            }

            const matched = Object.values(omit(whitelist, 'default')).find(
                value => value.rules.some(rule => rule.test(url))
            );

            if (matched) {
                return matched.request(url, userName);
            }

            return whitelist.default.request(url, userName);
        };

        try {
            const res = await reqeustUrlInfo(data.link);
            return res;
        }
        catch (ex) {
            return {title: (ex as Error).message, uuid: ''};
        }
    },
    /** 获取知识的状态 */
    getKnowledgeStatus: async ({userName, uuid}) => {
        const url = `/api/v2/api/aidevops/knowledge/rest/v1/knowledge/${uuid}`;
        type KnowledgeResponse = AutoWorkAPIProxyCollection['getKnowledgeStatus']['response'];
        return service.get<KnowledgeResponse>(url, {userName});
    },
    /** 等待知识爬取完成，按uuid的顺序，第一个完成再检索第二个，知道时间到了或者所有都是激活状态 */
    waitKnowledgeActivedInSecond: async ({second, uuids, ...params}) => {
        if (uuids.length === 0) {
            return;
        }

        const now = Date.now();
        const response = await apis.getKnowledgeStatus({...params, uuid: uuids[0]});
        const restSecond = second - (Date.now() - now);
        if (restSecond <= 0) {
            return;
        }
        // 如果当前是激活的就不等待了，可能下一个也完成了，或者就只有一个
        if (response.status === 'ACTIVATED') {
            return apis.waitKnowledgeActivedInSecond({...params, uuids: uuids.slice(1), second: restSecond - 2000});
        }

        await sleep(2000);
        return apis.waitKnowledgeActivedInSecond({...params, uuids, second: restSecond - 2000});
    },
    /** 上报日志数据 */
    reportTerminalLogLines: async ({userName, ...params}) => {
        const path = '/autodebug/api/v1/errorfix/record';
        const baseUrl = $features.ENVIRONMENT === 'test'
            ? 'http://auto-debug-web.test.auto-debug.appspace.baidu.com'
            : 'http://10.11.173.60:8101';
        const response = await axiosInstance.post(baseUrl + path, params, {headers: await uuapHeader(userName)});
        return response.data;
    },
    getExtensionConfig: async ({userName}) => {
        const remoteConfig = await getUserConfig(userName);
        return remoteConfig as unknown as Partial<ExtensionConfig>;
    },
    fetchKnowledgeList: async ({userName, query}) => {
        const knowledgeSet = await getCurrentUserKnowledgeSet(
            userName,
            undefined, // TODO 这里没传host，感觉会有问题
            userName,
            query
        );
        return knowledgeSet.map(v => ({
            id: v.uuid,
            name: v.name,
            type: v.type as ContextType,
            description: v.description,
        }));
    },
    getAccessTo: (() => {
        const cache: Record<string, boolean> = {};
        return async ({userName, featureName}) => {
            if (featureName === Feature.SmartApply) {
                return {status: true};
            }
            // saas 默认小流量隐藏AutoWork和意图识别开关都设置成false
            if (isSaaS) {
                return {status: false};
            }

            const index = `${userName}-${featureName}`;
            const enabled = cache[index];
            if (typeof enabled === 'boolean') {
                return {status: enabled};
            }
            try {
                const resposne = await accessTo({user: userName, type: featureName});
                cache[index] = resposne.data.status;
                return {status: cache[index]};
            }
            catch (ex) {
                cache[index] = false;
                return {status: false};
            }
        };
    })(),
    async setChatIntentRecognition({status, userName}) {
        await service.put('/api/aidevops/autocomate/rest/autowork/v1/intentSwitch', {userName, isOpen: status});
    },
    async getChatIntentRecognition({userName}) {
        const data = await service.get<{isOpen: boolean | null}>(
            '/api/aidevops/autocomate/rest/autowork/v1/intentSwitch',
            {userName}
        );
        return {status: !!data?.isOpen};
    },
};

interface File {
    path: string;
    name: string;
    start: Position;
    end: Position;
    content: string;
    repo?: string;
}

export interface OptimizeParams {
    file: File; // 文件信息
    username?: string;
    compiler?: string; // 当前工作区编译器版本
    device?: string;
    ide?: string;
}

export interface OptimizeData {
    id?: number;
    start: Position;
    end: Position;
    content: string;
    adoptionUuid?: string;
    streamEnd?: boolean;
    chatId?: string;
}

interface ResponseBase<T> {
    code: number;
    data: T;
    message?: string;
    traceId: string;
}

export type OptimizeResponse = ResponseBase<OptimizeData>;

export async function optimize(params: OptimizeParams) {
    const query = JSON.stringify(params);
    const result = await axiosInstance.post<OptimizeResponse>(
        '/api/aidevops/autocomate/rest/v1/shortcut/code/tuning',
        query,
        {
            headers: await uuapHeader(params.username),
            proxy: false,
        }
    );
    return result;
}

type StreamResponse = Partial<OptimizeData> & OptimizeResponse;

export async function optimizeStreamCode(
    params: OptimizeParams,
    update: (content: string, uuid: string, chatId: string) => void,
    cancellationToken?: vscode.CancellationToken,
    cancelToken?: CancelToken
) {
    const query = JSON.stringify(params);
    const res = await axiosInstance.post<IncomingMessage>(
        '/api/aidevops/autocomate/rest/v1/shortcut/code/tuning/stream',
        query,
        {
            headers: await uuapHeader(params.username),
            proxy: false,
            responseType: 'stream',
            cancelToken,
        }
    );
    const processor = new SSEProcessor<StreamResponse>(res.data, JSON.parse, cancellationToken);

    let uuid = '';
    let content = '';
    let chatId = '';
    const updateContent = (data: Partial<OptimizeData>) => {
        if (data.adoptionUuid && !uuid) {
            uuid = data.adoptionUuid;
            debug('chat response uuid:', uuid);
        }
        if (data.chatId && !chatId) {
            chatId = data.chatId;
        }
        if (data.content) {
            content += data.content;
            update(content, uuid, chatId);
        }
    };
    try {
        for await (const chunk of processor.processSSE()) {
            if (chunk.message) {
                processor.cancel();
                throw new Error((chunk as any).message);
            }
            updateContent(chunk.data ? chunk.data : chunk);
        }
        return {content, uuid, processor, chatId};
    }
    catch (e: any) {
        // 临时处理生成到一半然后异常的情况
        if (content) {
            error('request failed: ', e.message);
            return {content, uuid, processor, chatId};
        }
        throw e;
    }
}

export interface Context {
    id: string;
    name: string;
    type: ContextType;
}

export interface WebContext {
    name: string;
    type: ContextType;
    contextQuery: string; // 网络搜索用到的，用户原始的 query
}

export interface TextContext {
    type: ContextType;
    content: string;
}

export interface CurrentFileContext {
    type: ContextType.CURRENT_FILE;
    name: string;
    id: string;
    contents: string[]; // 当前文件的内容行
    cursorLine: number; // 光标所在行号，取不到则传0
    path: string; // 文件的相对路径
}

export interface FileContext<Params = undefined> {
    type: 'FILE';
    name: string;
    id: string;
    contents: string[]; // 当前文件的内容行
    cursorLine: number; // 光标所在行号，取不到则传0
    path: string; // 文件的相对路径
    params: Params;
}

export interface AnalyzeParams {
    slash: string; // 指令，根据slash判断分析阶段要执行哪些逻辑
    query: string;
    repoId?: string;
    /** 是否开启意图识别的开关 */
    intent?: boolean;
    conversationId?: number;
    needQueryRewrite: boolean;
    messageId?: number; // 重新生成时需要传入
    contexts: Array<Context | CurrentFileContext | WebContext | TextContext>;
    ide?: string;
}

export type AutoDebugClosestFunctionContext = FileContext<{
    filePath: string;
    lineNum: number;
    forwardRangeNum: number;
    backRangeNum: number;
    customRule: 'CLOSEST_FUNCTION';
    maxNum?: number;
}>;

export type AutoDebugFunctionDefinitionContext = FileContext<{
    filePath: string;
    lineNum: number;
    symbolName: string;
    customRule: 'DEFINITION';
}>;

export type AutoDebugSymbolTypeDefinitionContext = FileContext<{
    filePath: string;
    lineNum: number;
    offset?: number;
    symbolName: string;
    customRule: 'SYMBOL_TYPE_DEFINITION';
}>;

export type AutoDebugFileContext = FileContext<{
    filePath: string;
    lineNum: number;
    symbolName: string;
    retrieval: boolean; // 是否需要尝试检索文件
    customRule: 'FILE';
}>;

export type AutoDebugDirectoryTreeContext = FileContext<{
    filePath: string;
    lineNum: number;
    depth?: number;
    recursive: boolean;
    symbolName: string;
    customRule: 'DIRECTORY_TREE';
}>;

export type AutoDebugStructDefinitionContext = FileContext<{
    filePath: string;
    lineNum: number;
    recursive: boolean;
    symbolName: string;
    searchType: 'MULTILINE' | 'SINGLELINE';
    customRule: 'SYMBOLS_IN_STRUCT_OR_PACKAGE';
}>;

export type AutoDedugMetadataKey =
    | 'nodejsVersion'
    | 'pythonVersion'
    | 'gccVersion'
    | 'javaVersion'
    | 'goVersion'
    | 'platform'
    | 'arch';

export type AutoDebugMetadataContext = FileContext<{
    metadata: AutoDedugMetadataKey[];
    customRule: 'METADATA';
}>;

export type AutoDebugContext =
    | AutoDebugClosestFunctionContext
    | AutoDebugFunctionDefinitionContext
    | AutoDebugFileContext
    | AutoDebugDirectoryTreeContext
    | AutoDebugStructDefinitionContext
    | AutoDebugMetadataContext
    | AutoDebugSymbolTypeDefinitionContext;

interface SearchStrategy {
    rule: string; // 指令类型，要求插件端根据不同指令执行对应动作
    context: Context | AutoDebugContext;
    query: string;
    keys?: string[];
}

export interface AnalyzeData {
    conversationId: number;
    messageId: number;
    // isTech: boolean;
    queryType?: 'architecture' | 'other';
    /** 本地IDE检索代码以及远端向量检索用到的查询条件，不包括原始的query, #代码库会有 */
    codeSearchQueries?: string[] | null;
    searchStrategies?: SearchStrategy[];
    /** 意图识别的结果 */
    intentStrategies?: IntentStrategy[];
    extend?: {
        [key: string]: any;
    };
}

export type AnalyzeResponse = ResponseBase<AnalyzeData>;
// 分析接口需要手动调整下目录的name
function patchFolderContextName(params: AnalyzeParams) {
    return {
        ...params,
        contexts: params.contexts.map(context => {
            if (context.type === 'FOLDER') {
                return {
                    ...context,
                    name: `#${basename((context as Context).id)}`,
                };
            }
            return context;
        }),
    };
}

export class AnalyzeServerError extends Error {}
export async function analyze(params: AnalyzeParams, loginName?: string, cancelToken?: CancelToken) {
    debug('analyze params:', JSON.stringify(params));
    const query = JSON.stringify(
        flow([
            patchFolderContextName,
        ])(params)
    );
    const result = await axiosInstance.post<AnalyzeResponse>(
        '/api/aidevops/autocomate/rest/autowork/v1/analyze',
        query,
        {
            headers: await uuapHeader(loginName),
            cancelToken,
            proxy: false,
        }
    );
    // @wuweiqi 顺丰安全检索，返回400
    if (result.data.code === 400) {
        throw new AnalyzeServerError(result.data.message);
    }

    // TODO: saas账号过期，返回401。（暂时不确定code非200都会返回啥，只处理已知的吧)
    if (result.data.code === 401) {
        throw new AnalyzeServerError(result.data.message);
    }

    debug('analyze result:', JSON.stringify(result.data));
    return result;
}

/** 如果原来的检索策略里没有当前文件，就把意图识别的当前文件里的路径替换了 */
export const modifyIntentCurrentFileStrategyContextPath = (currentFilePath: string) => {
    return (data: AnalyzeData) => {
        if (!data.intentStrategies || !currentFilePath) {
            return data;
        }

        const strategies = data.intentStrategies.map(strategy => {
            if (strategy.rule as IntentStrategyRule === IntentStrategyRule.CURRENT_FILE_READ) {
                return {
                    ...strategy,
                    context: {
                        ...strategy.context,
                        id: currentFilePath,
                    },
                };
            }
            return strategy;
        });
        return {...data, intentStrategies: strategies};
    };
};

export interface SearchParams {
    conversationId: number;
    messageId: number;
    query: string;
    needCodeSearch?: boolean;
    slash: string;
    repoId?: string;
    knowledge?: KnowledgeList[];
    contexts: Array<Context | WebContext | TextContext>;
    analyze?: AnalyzeData;
}

export interface SearchData {
    codeChunks: CodeChunk[];
    /** 是否在检索时查询到可用的图谱信息 */
    showGraph?: boolean;
}

export type SearchResponse = ResponseBase<SearchData>;

export async function search(params: SearchParams, loginName?: string, cancelToken?: CancelToken) {
    consoleLogger.debug('search params:', params);
    const query = JSON.stringify(params);
    const result = await axiosInstance.post<SearchResponse>(
        '/api/aidevops/autocomate/rest/autowork/v1/search',
        query,
        {
            headers: await uuapHeader(loginName),
            cancelToken,
            proxy: false,
        }
    );
    consoleLogger.debug('search result:', result.data);
    return result;
}

export interface KnowledgeChunk {
    id: string;
    title: string;
    content: string;
    url?: string;
}

export interface ChatParams {
    conversationId: number;
    messageId: number;
    slash: string;
    query: string;
    /* (SAAS & VPC)自定义 prompt 内容 */
    selfDefineInstruction?: string;
    repoId?: string;
    device?: string; // 设备唯一ID
    contexts: Array<Context | WebContext | TextContext>;
    queryType?: string;
    codeChunks: CodeChunk[];
    knowledgeChunks?: KnowledgeChunk[];
    analyze?: AnalyzeData;
    extend?: any;
}

export type ChatResponse = ResponseBase<AutoComateResponse>;

export type ChatStreamResponse = Partial<AutoComateResponse> & ChatResponse;

const appendCurrentFileContextWhenAnalyzeResultHasCurrentFileIntentStrategy = (params: ChatParams) => {
    const clonedContexs = [...params.contexts];
    if (params.analyze?.intentStrategies?.find(isCurrentFileStrategy)) {
        if (!params.contexts.find(({type}) => type === ContextType.CURRENT_FILE)) {
            const currentFile = getAutoWorkChatCurrentFileContext();
            if (currentFile) {
                clonedContexs.push(currentFile);
            }
        }
    }
    return {
        ...params,
        contexts: clonedContexs,
    };
};

interface Folder {
    name: string;
    path: string;
    fileNames: string[];
    subFolders: Folder[];
}

const excludeFolderNames = [
    /node_modules/,
    /dist/,
    /build/,
    /site-packages/,
    /^\./,
];

function readdirRecursive(dir: string, limit: number) {
    const entry: Folder = {
        name: path.basename(dir),
        path: vscode.workspace.asRelativePath(dir),
        fileNames: [],
        subFolders: [],
    };
    if (limit < 0) {
        return entry;
    }
    const dirents = readdirSync(dir, {withFileTypes: true});
    const fileNames = dirents
        .filter(dirent => dirent.isFile())
        .filter(dirent => isNotJunk(dirent.name))
        .map(dirent => dirent.name);
    const subFolders = dirents
        .filter(dirent => dirent.isDirectory())
        .filter(dirent => excludeFolderNames.every(reg => !reg.test(dirent.name)));

    entry.fileNames.push(...fileNames);
    let appended = fileNames.length + subFolders.length;
    entry.subFolders.push(
        ...subFolders.map(folder => {
            const dirents = readdirSync(dir, {withFileTypes: true});
            appended = appended + dirents.length;
            return readdirRecursive(path.join(dir, folder.name), limit - appended);
        })
    );
    return entry;
}

const appendFolderParamsWhenAnalyzeResultHasArchitectureIntentStrategy = (params: ChatParams) => {
    try {
        if (params.analyze?.intentStrategies?.find(isArchitectureIntentStrategy)) {
            const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri?.fsPath;
            if (workspaceRoot) {
                const folders = readdirRecursive(workspaceRoot, 500);
                return {...params, folders: [folders]};
            }
        }
        return params;
    }
    catch (ex) {
        error('read workspaceFolders failed, reason:', (ex as Error).message);
        return params;
    }
};

// eslint-disable-next-line max-statements
export async function chatStreamCode(
    params: ChatParams,
    update: (data: Partial<CodeSearchResultEvent['detail'] & {activeSessionId?: string}>) => void,
    loginName?: string,
    cancellationToken?: vscode.CancellationToken,
    cancelToken?: CancelTokenSource
) {
    consoleLogger.debug('chatStreamCode params:', params);
    const context = await getExtensionContextAsync();
    const {repoName, currentFilePath} = await getTraceRepoInfo();
    // 二次修改参数
    const patchedParams = flow([
        appendCurrentFileContextWhenAnalyzeResultHasCurrentFileIntentStrategy,
        appendFolderParamsWhenAnalyzeResultHasArchitectureIntentStrategy,
    ])(params);
    const modelType = context.globalState.get<string>(CHAT_MODEL_ID);
    if (modelType) {
        patchedParams.modelKey = modelType;
    }
    const query = JSON.stringify({
        ...patchedParams,
        // 支持代码块返回带文件路径的标识
        useNewCodeWriteFormat: true,
        ide: getIdeName(),
        incremental: true,
        repoName,
        currentFilePath,
        ideVersion: vscode.version,
        pluginVersion: context?.extension.packageJSON.version,
    });
    let detail: Pick<CodeSearchResultEvent['detail'], 'summary' | 'exceptionMsg'> = {summary: ''};
    try {
        const res = await axiosInstance.post<IncomingMessage>(
            '/api/aidevops/autocomate/rest/autowork/v1/chat/stream',
            query,
            {
                headers: await uuapHeader(loginName),
                responseType: 'stream',
                cancelToken: cancelToken?.token,
                proxy: false,
            }
        );
        const processor = new SSEProcessor<ChatStreamResponse>(res.data, JSON.parse, cancellationToken);

        const adoptionUuid = '';
        // eslint-disable-next-line @typescript-eslint/init-declarations
        let conversationId: number | undefined;
        let previousReasoningText = '';
        let previousSummary = '';
        const chatId = '';
        const updateContent = (data: Partial<AutoComateResponse>) => {
            const content = data.content as CodeSearchResultEvent;
            if (content.detail) {
                detail = pick(content.detail, ['summary', 'exceptionMsg']);
                if (content.detail.reasoningDelta) {
                    if (!content.detail.reasoningEnd) {
                        previousReasoningText = previousReasoningText + content.detail.reasoningDelta;
                    }
                }
                else if (content.detail.reasoningSummary) {
                    previousReasoningText = content.detail.reasoningSummary;
                }

                if (content.detail.delta) {
                    previousSummary = previousSummary + content.detail.delta;
                }
                else if (content.detail.summary) {
                    previousSummary = content.detail.summary;
                }

                update({...content.detail, summary: previousSummary, reasoningSummary: previousReasoningText});
            }
            if (data.conversationId) {
                conversationId = data.conversationId;
            }
            // if (detail.adoptionUuid && !adoptionUuid) {
            //     adoptionUuid = detail.adoptionUuid;
            //     debug('chat response uuid:', adoptionUuid);
            // }
            // if (detail.summary) {
            //     summary += detail.summary;
            //     update({ ...detail, summary, adoptionUuid});
            // }
        };
        for await (const chunk of processor.processSSE()) {
            if (chunk.message) {
                processor.cancel();
                throw new Error((chunk as any).message);
            }
            consoleLogger.debug('chatStreamCode chunk:', chunk);
            updateContent(chunk.data ? chunk.data : chunk);
            // 第三方服务报错
            if (detail.exceptionMsg) {
                throw new Error(detail.exceptionMsg);
            }
            // SSE 报错
            if (processor.error) {
                throw new Error(processor.errorMsg);
            }
        }

        return {
            conversationId,
            summary: detail.summary,
            errorMsg: detail.exceptionMsg,
            uuid: adoptionUuid,
            processor,
            chatId,
        };
    }
    catch (e: any) {
        // 临时处理生成到一半然后异常的情况
        if (detail.summary) {
            error('request failed: ', e.message);
            throw new Error(detail.summary);
        }
        throw e;
    }
}

export async function cancelChatStream({messageId, userName}: {userName: string, messageId: number}) {
    return axiosInstance.post(
        '/api/aidevops/autocomate/rest/autowork/v1/chat/stream/cancel',
        {messageId},
        {headers: await uuapHeader(userName), proxy: false}
    );
}

export interface AdoptParams {
    slash: string;
    adoptionUuid: string;
    contentStr: string;
}

export type AdoptResponse = ResponseBase<string>;

export async function adopt(params: AdoptParams, loginName?: string, cancelToken?: CancelToken) {
    consoleLogger.debug('adopt params:', params);
    const query = JSON.stringify(params);
    const result = await axiosInstance.post<AdoptResponse>(
        '/api/aidevops/autocomate/rest/autowork/v1/adopt',
        query,
        {
            headers: await uuapHeader(loginName),
            cancelToken,
        }
    );
    consoleLogger.debug('adopt result:', result.data);
    return result;
}

/** 上报用户的粘贴行为 */
export async function logUserPasteMertrics({loginName, ...params}: any) {
    return axiosInstance.post(
        '/api/aidevops/autocomate/rest/v1/metric/paste/recordIdePasteMetric',
        params,
        {
            headers: await uuapHeader(loginName),
        }
    );
}

interface FeedbackParams extends FeedbackOptions {
    loginName: string;
    messageId: number;
    conversationId: number;
}

const formatLikeStatus = (isLike: FeedbackOptions['isLike']) => {
    switch (isLike) {
        case '0':
            return 'NONE';
        case '1':
            return 'LIKE';
        case '2':
            return 'UNLIKE';
        default:
            return undefined;
    }
};

export async function feedback({messageId, conversationId, isLike, userFeedback, loginName}: FeedbackParams) {
    const params = {
        messageId,
        conversationId,
        likeStatus: formatLikeStatus(isLike),
        feedBack: userFeedback?.otherFeedback,
    };
    consoleLogger.debug('AutoComate feedback params:', params);
    const result = await axiosInstance.post(
        '/api/aidevops/autocomate/rest/autowork/v1/conversation/like',
        params,
        {
            headers: await uuapHeader(loginName),
            proxy: false,
        }
    );
    consoleLogger.debug('AutoComate feedback result:', result.data);
    return result;
}

interface AutoPasteParams {
    loginName: string;
    ide: 'vscode';
    pluginVersion: string;
    ideVersion: string;
    device: string;
    path: string;
    code: string;
    codeStartLine: number;
    pasteStartLine: number;
    pasteEndLine: number;
}

export interface AutoPasteResponse {
    uuid: string;
    content: string;
}

export async function requestAutoPasteStream({loginName, ...params}: AutoPasteParams, cancelToken?: CancelToken) {
    const query = JSON.stringify(params);
    const result = await axiosInstance.post<Res<AutoPasteResponse>>(
        '/api/aidevops/autocomate/rest/v1/smartpaste',
        query,
        {
            headers: await uuapHeader(loginName),
            cancelToken,
        }
    );
    return result.data;
}
