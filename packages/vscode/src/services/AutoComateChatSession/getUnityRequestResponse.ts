import {UnityRequestType, HttpUnityResponseType} from '@shared/protocols';
import {createAxios} from '@/api/common';
import {uuapHeader} from './api';

const axiosInstance = createAxios({});
const requestMethods = {
    GET: axiosInstance.get,
    POST: axiosInstance.post,
    PUT: axiosInstance.put,
    DELETE: axiosInstance.delete,
};

export const getUnityRequestResponse = async <T>(
    params: UnityRequestType,
    userName: string
): Promise<HttpUnityResponseType<T>> => {
    const {
        method,
        url,
        body,
        options,
        headers,
        authorization,
    } = params;

    let realHeaders: Record<string, string> = headers || {};
    if (authorization) {
        const uuapHeaders = await uuapHeader(userName);
        realHeaders = {...(headers || {}), ...uuapHeaders};
    }

    const requestMethod = requestMethods[method];
    if (!requestMethod) {
        throw new Error('Unsupported method');
    }

    try {
        const response = await requestMethod(url, ...(['GET', 'DELETE'].includes(method) ? [] : [body]), {
            headers: realHeaders,
            ...options,
        });
        return {
            data: response.data,
            headers: response.headers as Record<string, string>,
            status: response.status,
        };
    }
    catch (error) {
        return {
            error: error as Error,
        };
    }
};
