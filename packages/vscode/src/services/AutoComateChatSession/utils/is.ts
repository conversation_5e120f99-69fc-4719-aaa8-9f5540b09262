import {SlashType} from '@shared/constants';
import {IntentStrategy, IntentStrategyRule} from '../types/indentStrategy';

export const isAutoDebugSlash = (input?: string) => input === SlashType.AUTO_DEBUG;
export const isAutoDebugCapability = (detail: {extra?: {capability?: string}}) => {
    return isAutoDebugSlash(detail.extra?.capability);
};

export const isKeywordIntentStrategySearch = (strategy: IntentStrategy) => {
    return strategy.rule === IntentStrategyRule.REPO_KEY
        || strategy.rule === IntentStrategyRule.FOLDER_KEY;
};

export const isCurrentFileStrategy = (strategy: IntentStrategy) => {
    return strategy.rule === IntentStrategyRule.CURRENT_FILE_READ;
};

export const isArchitectureIntentStrategy = (strategy: IntentStrategy) => {
    return strategy.rule === IntentStrategyRule.ARCHITECTURE_READ;
};
