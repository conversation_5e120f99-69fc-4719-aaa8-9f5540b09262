import {cloneDeep, maxBy} from 'lodash';
import {MessageStatus, TaskStatus} from '@shared/protocols';
import {L10n} from '@/common/L10nProvider/L10n';
import {AutoWorkText} from '@/common/L10nProvider/constants';
import {CodeChunk, KnowledgeChunk, WebChunk} from '../../common/types';
import {AutoComateResponse, AutoComateTask} from './types';

export interface InternalSession {
    conversationId?: number;
    workflowBuildId?: number;
    jobBuildId?: number;
    messageId?: number;
    tasks?: AutoComateTask[];
    summary?: string;
    error?: string;
    status: MessageStatus;
    codeChunks?: CodeChunk[];
    knowledgeChunks?: KnowledgeChunk[]; // 知识集检索结果
    webChunks?: WebChunk[]; // 网络检索结果
    ctime: number;
    relevantFiles?: string[]; // 相关文件由本地选出，快速展示给用户看，减少等待焦虑
    files?: string[]; // 圈选的检索文件范围
    folders?: string[]; // 圈选的检索目录范围
    reminder?: string; // 提示信息
    showGraph?: boolean;
    /** 一些不在规范里小范围使用的数据 */
    extend?: any;

    /** 用于记录当前会话的推理过程 */
    reasoningSummary?: string;
    reasoningStatus?: MessageStatus;
    reasoningError?: string;
}

export class ChatSessionsManager {
    private readonly sessions = new Map</* id */ string, InternalSession>();

    get(sessionId: string) {
        return cloneDeep(this.sessions.get(sessionId));
    }

    getLastSession() {
        const session = maxBy([...this.sessions.values()], 'ctime');
        return cloneDeep(session);
    }

    clear() {
        this.sessions.clear();
    }

    add(sessionId: string, content?: Partial<InternalSession>) {
        const added: InternalSession = {
            ...content,
            status: 'inProgress',
            ctime: Date.now(),
        };
        this.sessions.set(sessionId, added);
        return cloneDeep(added);
    }

    interrputSession(sessionId: string) {
        const session = this._getSession(sessionId);
        if (session?.status === 'inProgress') {
            session.status = 'failed';
            session.error = L10n.t(AutoWorkText.CONNECTION_INTERRUPT);
            for (const task of session?.tasks || []) {
                if (task.status === TaskStatus.PROCESSING) {
                    task.status = TaskStatus.FAIL;
                }
            }
        }
    }

    cancelSession(sessionId: string) {
        const session = this._getSession(sessionId);
        if (session?.status === 'inProgress') {
            session.status = 'canceled';
            for (const task of session?.tasks || []) {
                if (task.status === TaskStatus.PROCESSING) {
                    task.status = TaskStatus.INIT;
                }
            }
        }
    }

    setValue<T extends keyof InternalSession>(sessionId: string, field: T, value: InternalSession[T]) {
        const session = this._getSession(sessionId);
        if (session) {
            session[field] = value;
        }
    }

    private _getSession(sessionId: string) {
        if (!this.sessions.has(sessionId)) {
            this.sessions.set(sessionId, {
                status: 'inProgress',
                ctime: Date.now(),
            });
        }
        return this.sessions.get(sessionId);
    }

    // eslint-disable-next-line complexity, max-statements
    async handleEvent(sessionId: string, event: AutoComateResponse) {
        const session = this.sessions.get(sessionId);
        if (!session) {
            throw new Error(`Internal error: session ${sessionId} not found`);
        }
        if (session.jobBuildId === undefined) {
            session.jobBuildId = event.jobBuildId;
        }
        if (session.workflowBuildId === undefined) {
            session.workflowBuildId = event.workflowBuildId;
        }
        if (session.conversationId === undefined) {
            session.conversationId = event.conversationId;
        }
        // 当前消息不是当前会话的响应，不处理
        if (event.workflowBuildId !== session.workflowBuildId) {
            return;
        }
        if (event.conversationId !== session.conversationId) {
            return;
        }
        if (session.status === 'canceled') {
            // 如果当前已经 cancel 了，不处理 message
            return;
        }
        // Event(THOUGHT): 同步AC思考过程
        if (event.content.type === 'THOUGHT') {
            if (session.status === 'failed') {
                // 状态为failed时又收到了更新事件，说明又恢复了，因此重置状态
                session.status = 'inProgress';
                session.error = undefined;
            }
            session.tasks = event.tasks;
            if (event.content.detail?.summary) {
                session.summary = event.content.detail.summary;
            }
            if (event.content.detail?.data) {
                session.codeChunks = event.content.detail.data;
            }
            session.files = event.content.detail?.files;
            session.folders = event.content.detail?.folders;
        }
        // Event(CODE_SEARCH_RESULT): 同步AC代码检索结果
        else if (event.content.type === 'CODE_SEARCH_RESULT') {
            if (session.status === 'failed') {
                // 状态为failed时又收到了更新事件，说明又恢复了，因此重置状态
                session.status = 'inProgress';
                session.error = undefined;
            }
            session.codeChunks = event.content.detail.data;
            session.knowledgeChunks = event.content.detail.knowledge;
            session.summary = event.content.detail.summary;
            session.tasks = event.tasks;
            if (event.content.detail.end) {
                session.status = 'success';
                session.files = event.content.detail?.files;
                session.folders = event.content.detail?.folders;
            }
        }
        // Event(EXCEPTION): 同步AC异常输出
        else if (event.content.type === 'EXCEPTION') {
            session.error = event.content.detail.exceptionMsg;
            session.status = 'failed';
            for (const task of session?.tasks || []) {
                if (task.status === TaskStatus.PROCESSING) {
                    task.status = TaskStatus.FAIL;
                }
            }
        }
    }
}
