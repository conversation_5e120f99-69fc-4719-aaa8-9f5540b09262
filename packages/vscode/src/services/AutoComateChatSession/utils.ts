import fs from 'node:fs/promises';
import {constants as fsConstants} from 'node:fs';
import * as path from 'path';
import * as vscode from 'vscode';
import {
    AssistantMessage,
    ContextType,
    DynamicSection,
    ExtensionDisplayLanguages,
    KnowledgeList,
    placeholderChar,
} from '@shared/protocols';
import {L10n} from '@/common/L10nProvider/L10n';
import {GlobalText, AutoWorkText} from '@/common/L10nProvider/constants';
import {inferLanguage} from '../../utils/languages';
import {getRepoHostAndName} from '../../utils/git';
import {
    partitionPathsByType,
    isCharacterOverLimit,
    getCurrentFileRelativePath,
    getCurrentFileContent,
} from '../../utils/files';
import {CodeChunk} from '../../common/types';
import {Context, CurrentFileContext, WebContext} from './api';
import {AutoComateTask} from './types';
import {InternalSession} from './ChatSessionsManager';
import {isAutoDebugSlash} from './utils/is';
import AutoWorkConversationContext from './ConversationContext';

export async function getWorkspaceIdentifier() {
    const workspaces = vscode.workspace.workspaceFolders;
    if (workspaces && workspaces.length > 0) {
        const target = workspaces[0];
        const id = await getRepoHostAndName(target.uri.fsPath);
        return id || target.name;
    }
    return '';
}

/**
 * 将关键字搜索结果按长度分类，并确保longKeywordCodeChunks数组长度不超过3
 *
 * @param keywordSearchResult 关键字搜索结果数组，每个元素包含关键字和对应的代码块
 * @returns 返回一个包含两个数组元素的元组，第一个数组包含长度大于等于5且最大长度为3的关键字对应代码块，其余内容和长度小于等于5的关键字对应的代码块放在第二个数组
 */
export function splitKeywordSearchResult(keywordSearchResult: Array<{keyword: string, codeChunks: CodeChunk[]}>) {
    const chunksDescSortedByKeyword = keywordSearchResult.sort((a, b) => b.keyword.length - a.keyword.length);
    const headCount = 3;
    return chunksDescSortedByKeyword.reduce<[CodeChunk[], CodeChunk[]]>(
        ([longKeywordCodeChunks, shortKeywordCodeChunks], item) => {
            if (item.keyword.length <= 4 || longKeywordCodeChunks.length >= headCount) {
                return [longKeywordCodeChunks, [...shortKeywordCodeChunks, ...item.codeChunks]];
            }

            const appendCount = headCount - longKeywordCodeChunks.length;
            return [
                [...longKeywordCodeChunks, ...item.codeChunks.slice(0, appendCount)],
                [...shortKeywordCodeChunks, ...item.codeChunks.slice(appendCount)],
            ];
        },
        [[], []]
    );
}
// eslint-disable-next-line complexity
export function mapSessionToMessage(session: Partial<InternalSession>) {
    const dynamicSections: DynamicSection[] = [];
    if (session.files?.length !== 0 || session.folders?.length !== 0) {
        dynamicSections.push({
            // label: '关联',
            label: L10n.t(GlobalText.COMMON_RELATED),
            collapsible: true,
            done: session.status === 'success',
            type: 'relativeFiles',
            files: [...(session.files || []), ...(session.folders || [])],
        });
    }
    if (session.reminder) {
        dynamicSections.push({
            // label: "提示"
            label: L10n.t(GlobalText.COMMON_PROMPT),
            collapsible: true,
            done: session.status === 'success',
            type: 'reminder',
            reminder: session.reminder,
        });
    }
    if (session.tasks) {
        dynamicSections.push({
            // label: '思考过程',
            label: L10n.t(GlobalText.COMMON_THINKING),
            collapsible: true,
            done: Boolean(session.summary || session.reasoningSummary),
            type: 'actionItems',
            relevantFiles: session.relevantFiles,
            tasks: session.tasks,
        });
    }

    if (session.codeChunks || session.knowledgeChunks || session.webChunks) {
        const codeChunks = session.codeChunks?.map(item => ({
            filePath: item.path,
            code: item.content,
            language: inferLanguage(item.path) || 'plaintext',
            startLine: item.contentStart?.line,
            endLine: item.contentEnd?.line,
        })) || [];
        dynamicSections.push({
            // label: '搜索结果',
            label: L10n.t(GlobalText.COMMON_SEARCH_RESULT),
            collapsible: true,
            showGraph: session.showGraph,
            done: session.status === 'success',
            type: 'codeChunks',
            codeChunks,
            knowledgeChunks: session.knowledgeChunks || [],
            webChunks: session.webChunks || [],
        });
    }

    if (session.reasoningSummary) {
        dynamicSections.push({
            // 这里的label其实并不会使用到，但是为了保持数据的一致性，暂时设置为空字符串
            label: '',
            collapsible: true,
            done: session.reasoningStatus === 'success',
            status: session.reasoningStatus ?? 'success',
            type: 'reasoning',
            content: session.reasoningSummary,
        });
    }

    // TODO: Message 需要专门加一个字段展示 error
    const content = [session.summary, session.error].filter(Boolean).join('\n\n');
    const messageUpdates: Partial<AssistantMessage> = {
        dynamicSections,
        content,
        status: session.status,
    };
    return messageUpdates;
}

/**
 * 优先级从上往下
 * 1. 如果选择了当前代码库，返回 true
 * 2. 统计所有选中文件的字符数，如果超过38000则使用关键字检索，放弃选中的chunk
 */
export async function getNeedCodeSearch(conversationContext: AutoWorkConversationContext) {
    const paths = conversationContext.filePathsContext;
    const maxCodeChunkToken = conversationContext.maxCodeChunkToken;
    const useGlobalSearch = paths.includes('repo');
    // 搜索范围为当前代码库
    if (useGlobalSearch) {
        return true;
    }

    const [folders, files] = partitionPathsByType(paths);
    const isOverLimit = await isCharacterOverLimit(files, folders, maxCodeChunkToken);
    // 搜索范围原文字符数超过38000
    if (isOverLimit) {
        return true;
    }
    return false;
}

export async function getNeedQueryRewrite(conversationContext: AutoWorkConversationContext) {
    // 选择知识集时autowork侧进行query改写
    if (conversationContext.knowledgeContext.length !== 0) {
        return true;
    }
    const needCodeSearch = await getNeedCodeSearch(conversationContext);
    // 需要代码检索时进行query改写
    if (needCodeSearch) {
        return true;
    }
    return false;
}

const constructFolderCodeChunk = (files: string[], rootPath: string): CodeChunk => ({
    path: '项目根目录结构',
    content: files.join('\n'),
    contentStart: {line: 0, column: 0},
    contentEnd: {line: files.length, column: 0},
    repo: rootPath,
    type: 'architecture',
});

const constructReadmeCodeChunk = (data: string, rootPath: string): CodeChunk => {
    const contentLines = data.split('\n');
    return {
        path: 'README.md',
        content: data,
        contentStart: {line: 0, column: 0},
        contentEnd: {line: contentLines.length - 1, column: 0},
        repo: rootPath,
        type: 'architecture',
    };
};

// 构造项目架构相关代码块，帮助模型回答架构相关问题
export const constructArchitectureCodeChunk = async (rootPath: string): Promise<CodeChunk[]> => {
    const codeChunks: CodeChunk[] = [];
    try {
        const files = await fs.readdir(rootPath);
        codeChunks.push(constructFolderCodeChunk(files, rootPath));
        const readmePath = path.join(rootPath, 'README.md');
        await fs.access(readmePath, fsConstants.F_OK);
        const stats = await fs.stat(readmePath);
        // 处理文件大小超过限制的情况
        const maxFileSize = 1024 * 1024; // 设置读取文件大小限制为1MB
        if (stats.size > maxFileSize) {
            console.warn(
                // eslint-disable-next-line max-len
                `File size of ${readmePath} (${stats.size} bytes) exceeds maximum size of ${maxFileSize} bytes. Skipping...`
            );
            return codeChunks;
        }
        const data = await fs.readFile(readmePath, 'utf8');
        codeChunks.push(constructReadmeCodeChunk(data, rootPath));
    }
    catch (error) {
        console.error(`Error accessing file or directory: ${error}`);
    }
    return codeChunks;
};

/**
 * 网络检索特有逻辑
 * 当前没有互斥逻辑，所以在有 #网络检索 时，需要额外提取从 #网络检索 到下一个选取的知识之间的内容，单独将这部分 query 塞入 context 的 contextQuery 中
 */
export const extractWebSearchText = (text: string, language?: ExtensionDisplayLanguages) => {
    const startTag = language === 'en'
        ? `${placeholderChar}#Web Search${placeholderChar}`
        : `${placeholderChar}#网络检索${placeholderChar}`;
    let startTagIndex = text.indexOf(startTag);
    if (startTagIndex === -1) {
        return text;
    }
    startTagIndex += startTag.length; // 开始提取的位置是startTag索引加上它的长度
    const endTagIndex = text.indexOf('#', startTagIndex);
    if (endTagIndex === -1) {
        return text.slice(startTagIndex).trim(); // 如果没有endTag，返回从startTag到字符串末尾的部分
    }
    return text.slice(startTagIndex, endTagIndex).trim(); // 从startTag到endTag之间的子字符串
};

export const getAutoWorkChatGithubRepoContext = (paths: string[]) => {
    const githubPaths = paths.filter(path => path.startsWith('github'));
    const githubContexts: Context[] = githubPaths.map(path => ({
        id: path,
        name: path,
        type: ContextType.REPO,
    }));
    return githubContexts;
};

export const getAutoWorkWebKnowledgeContext = (
    query: string,
    language: ExtensionDisplayLanguages,
    webKnowledge: KnowledgeList[]
): WebContext[] => {
    return webKnowledge.map(knowledge => ({
        name: knowledge.name,
        type: knowledge.type,
        contextQuery: extractWebSearchText(query, language),
    }));
};

export const getAutoWorkDocContext = (knowledges: KnowledgeList[]): Array<Context | CurrentFileContext> => {
    return knowledges.map(knowledge => ({
        id: knowledge.id,
        // @ts-expect-error 网页检索有bug
        name: knowledge.display || knowledge.name,
        type: knowledge.type,
        retrievalType: knowledge.retrievalType,
    }));
};

export const getAutoWorkChatCurrentFileContext = () => {
    const currentFile = getCurrentFileRelativePath();
    if (currentFile) {
        const currentFileContent = getCurrentFileContent();
        const cursorLine = vscode.window.activeTextEditor?.selection.start.line || 0;
        const currentFileContext: CurrentFileContext = {
            id: currentFile,
            name: '当前文件',
            type: ContextType.CURRENT_FILE,
            cursorLine,
            contents: currentFileContent,
            path: currentFile,
        };
        return currentFileContext;
    }

    return null;
};

export const getAutoWorkChatCurrentRepoContext = (repoId: string): Context => {
    return {
        id: repoId,
        name: '当前代码库',
        type: ContextType.REPO,
    };
};


// TODO 如果能单拆出来更好
export const getAutoWorkChatFileAndFolderContext = (paths: string[]) => {
    const [folders, files] = partitionPathsByType(paths);
    const folderContexts: Context[] = folders.map(path => ({
        id: path,
        name: path,
        type: ContextType.FOLDER,
    }));
    const fileContexts: Context[] = files.map(path => ({
        id: path,
        name: path,
        type: ContextType.FILE,
    }));

    return [
        ...folderContexts,
        ...fileContexts,
    ];
};

export const getAutoWorkTerminalContext = (
    knowledges: KnowledgeList[]
): Array<{type: ContextType, content: string}> => {
    return knowledges.map(knowledge => ({
        type: ContextType.TERMINAL,
        content: knowledge.content || '',
    }));
};


export function getAutoDebugDescription(taskType: AutoComateTask['taskType']) {
    if (taskType === 'THOUGHT') {
        return L10n.t(AutoWorkText.AUTO_DEBUG_STAGE_THOUGHT);
    }
    // 检索阶段，直接展示query
    if (taskType === 'SEARCH') {
        return L10n.t(AutoWorkText.AUTO_DEBUG_STAGE_SEARCH);
    }
    if (taskType === 'ANSWER') {
        return L10n.t(AutoWorkText.AUTO_DEBUG_STAGE_ANSWER);
    }
    return '';
}

export function getTaskDesc(taskType: AutoComateTask['taskType'], query: string, slash?: string) {
    if (isAutoDebugSlash(slash)) {
        return getAutoDebugDescription(taskType);
    }
    if (taskType === 'THOUGHT') {
        return L10n.t(AutoWorkText.TASK_THOUGHT);
    }
    // 检索阶段，直接展示query
    if (taskType === 'SEARCH') {
        return query;
    }
    if (taskType === 'ANSWER') {
        return L10n.t(AutoWorkText.TASK_ANSWER);
    }
    return '';
}
