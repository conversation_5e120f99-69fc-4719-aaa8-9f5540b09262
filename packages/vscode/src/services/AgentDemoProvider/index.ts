// powered by zulu
import fs from 'fs/promises';
import path from 'path';
import os from 'os';
import {injectable} from 'inversify';
import * as vscode from 'vscode';
import {AgentConfigItem, EventMessage, WebviewAgentConversationType} from '@shared/protocols';
import {isFileExist} from '@/utils/fs';
import {getExtensionContextAsync} from '@/utils/extensionContext';
import {iocContainer} from '@/iocContainer';
import {RegisteredCommand} from '@/constants';
import {ChatViewProvider} from '../ChatViewProvider';
import {KernelProvider} from '../KernelProvider';

const filePrefix = 'comate-zulu-demo';
const CONFIG_FILE_NAME = '.comate-zulu-config.json';
const GLOBAL_STORAGE_KEY = 'comate-agent-demo-workspace-configs';

type TaskStatus = 'pending' | 'done';

// 定义配置文件的接口
export interface ComateConfig extends AgentConfigItem {
    status: TaskStatus;
}

@injectable()
export class AgentDemoProvider implements vscode.Disposable {
    context!: vscode.ExtensionContext;

    async init() {
        if (!(vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0)) {
            return;
        }
        this.context = await getExtensionContextAsync();
        try {
            // 获取当前打开的工作区路径
            const currentWorkspacePath = vscode.workspace.workspaceFolders[0].uri.fsPath;
            // 检查当前工作区是否是我们的 demo 工作区
            if (path.basename(currentWorkspacePath).startsWith(filePrefix)) {
                const config = await this.readConfigFile(currentWorkspacePath);
                if (!config || config.status !== 'pending') {
                    return;
                }
                switch (config.type) {
                    case 'figma2code':
                        await this.startF2cDemo(config, currentWorkspacePath);
                        return;
                    // 默认走原来的逻辑
                    default:
                        await this.startDemo(config, currentWorkspacePath);
                }
            }
        }
        catch (error) {
            // eslint-disable-next-line no-console
            console.log('Can\'t find workspace', error);
        }
    }

    async startF2cDemo(config: AgentConfigItem, currentWorkspacePath: string) {
        if (config.figmaInfo) {
            await iocContainer.get(KernelProvider).serviceReady;
            vscode.commands.executeCommand(RegisteredCommand.showChatPanel, {activeTabKey: 'AGENT'});
            await iocContainer.get(ChatViewProvider).webviewServiceInitialized;

            iocContainer.get(ChatViewProvider).sendDataToWebview(
                EventMessage.AgentConversationAddFromIdeEvent,
                {
                    conversationId: '',
                    conversationType: WebviewAgentConversationType.F2cBotConversation,
                    messageType: 'add-conversation',
                    payload: {
                        knowledgeList: [],
                        query: config.prompt,
                        figmaInfo: config.figmaInfo,
                    },
                }
            );
            const configPath = path.join(currentWorkspacePath, CONFIG_FILE_NAME);
            this.writeConfig(configPath, {
                ...config,
                status: 'done',
            });
        }
    }
    async startDemo(config: AgentConfigItem, currentWorkspacePath: string) {
        await iocContainer.get(KernelProvider).serviceReady;
        vscode.commands.executeCommand(RegisteredCommand.showChatPanel, {activeTabKey: 'AGENT'});
        await iocContainer.get(ChatViewProvider).webviewServiceInitialized;
        vscode.commands.executeCommand(RegisteredCommand.showChatPanel, {
            activeTabKey: 'AGENT',
            params: {query: config.prompt, knowledgeList: config.knowledgeList || []},
        });
        const configPath = path.join(currentWorkspacePath, CONFIG_FILE_NAME);
        this.writeConfig(configPath, {
            ...config,
            status: 'done',
        });
    }
    async createDemo(config: AgentConfigItem) {
        const workspacePath = await this.createWorkspace();
        const configPath = path.join(workspacePath, CONFIG_FILE_NAME);
        const newConfig = {
            ...config,
            status: 'pending' as TaskStatus,
        };
        await this.writeConfig(configPath, newConfig);
        await this.updateGlobalStorage(configPath, newConfig);
        vscode.commands.executeCommand('vscode.openFolder', vscode.Uri.file(workspacePath));
    }

    async readConfigFile(workspacePath: string): Promise<ComateConfig | undefined> {
        const configPath = path.join(workspacePath, CONFIG_FILE_NAME);

        try {
            // 检查配置文件是否存在
            if (await isFileExist(configPath)) {
                const configContent = await fs.readFile(configPath, 'utf-8');
                return JSON.parse(configContent);
            }
            else {
                const storedConfig = await this.getConfigFromGlobalStorage(workspacePath);
                if (storedConfig) {
                    await this.writeConfig(configPath, storedConfig);
                    return storedConfig;
                }
                return undefined;
            }
        }
        catch (error: unknown) {
            const errorMessage = error instanceof Error
                ? error.message
                : 'An unknown error occurred';

            console.error('Error handling config file:', errorMessage);
            throw new Error(`Failed to handle config file: ${errorMessage}`);
        }
    }

    private async updateGlobalStorage(workspacePath: string, config: ComateConfig): Promise<void> {
        try {
            const existingConfigs = this.context.globalState.get<Record<string, ComateConfig>>(GLOBAL_STORAGE_KEY)
                || {};
            existingConfigs[workspacePath] = config;
            await this.context.globalState.update(GLOBAL_STORAGE_KEY, existingConfigs);
        }
        catch (error) {
            console.error('Error updating global storage:', error);
        }
    }

    private async getConfigFromGlobalStorage(workspacePath: string): Promise<ComateConfig | undefined> {
        const configs = this.context.globalState.get<Record<string, ComateConfig>>(GLOBAL_STORAGE_KEY);
        return configs?.[workspacePath];
    }

    private async writeConfig(configPath: string, config: ComateConfig): Promise<void> {
        try {
            await fs.writeFile(
                configPath,
                JSON.stringify(config, null, 2),
                'utf-8'
            );
        }
        catch (error: unknown) {
            const errorMessage = error instanceof Error
                ? error.message
                : 'An unknown error occurred';

            throw new Error(`Failed to write config file: ${errorMessage}`);
        }
    }

    async createWorkspace(): Promise<string> {
        try {
            const projectPath = await this.findAvailablePath();
            await fs.mkdir(projectPath, {recursive: true});
            return projectPath;
        }
        catch (error: unknown) {
            const errorMessage = error instanceof Error
                ? error.message
                : 'An unknown error occurred';

            console.error('Error creating demo folder:', errorMessage);
            throw new Error(`Failed to create demo folder: ${errorMessage}`);
        }
    }

    private async findAvailablePath(): Promise<string> {
        const homeDir = process.env.HOME || process.env.USERPROFILE || os.homedir();
        // 检查基础路径是否可用
        const basePath = path.join(homeDir, filePrefix);
        if (!await isFileExist(basePath)) {
            return basePath;
        }

        // 使用时间戳作为后缀
        const timestamp = Date.now();
        const currentPath = path.join(homeDir, `${filePrefix}-${timestamp}`);
        return currentPath;
    }

    dispose() {
        // nothing
    }
}
