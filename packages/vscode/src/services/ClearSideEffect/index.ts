import * as fs from 'fs/promises';
import * as path from 'path';
import {getExtensionContextAsync} from '@/utils/extensionContext';

export default class ClearSideEffect {
    constructor() {
        this.clearCoreFiles().catch(error => {
            console.error('Error clearing core files:', error);
        });
    }
    private async clearCoreFiles(): Promise<void> {
        try {
            const context = await getExtensionContextAsync();
            const extensionPath = context.extensionPath;

            // Read all files in the extension directory
            const files = await fs.readdir(extensionPath);

            // Filter and check files that start with "core"
            const coreFilePaths: string[] = [];

            for (const file of files) {
                if (file.startsWith('core')) {
                    const filePath = path.join(extensionPath, file);
                    try {
                        const stat = await fs.stat(filePath);
                        if (stat.isFile()) {
                            coreFilePaths.push(filePath);
                        }
                    }
                    catch (error) {
                        console.error(`Error checking file ${filePath}:`, error);
                    }
                }
            }

            if (coreFilePaths.length <= 1) {
                // eslint-disable-next-line no-console
                console.log('Found only one or zero core files. No files will be deleted.');
                return;
            }

            const filesToDelete = coreFilePaths.slice(1);

            const deletePromises = filesToDelete.map(async filePath => {
                try {
                    await fs.unlink(filePath);
                    // eslint-disable-next-line no-console
                    console.log(`Deleted file: ${filePath}`);
                }
                catch (error) {
                    console.error(`Error deleting file ${filePath}:`, error);
                }
            });

            await Promise.all(deletePromises);
            // eslint-disable-next-line no-console
            console.log(
                `Cleared ${filesToDelete.length} core files from extension directory. Kept ${coreFilePaths[0]}.`
            );
        }
        catch (error) {
            console.error('Error in clearCoreFiles:', error);
            throw error;
        }
    }
}
