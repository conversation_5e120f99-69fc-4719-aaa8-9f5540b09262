import {injectable} from 'inversify';
import * as vscode from 'vscode';
import {L10n} from '@/common/L10nProvider/L10n';
import {ChatTipsProviderText} from '@/common/L10nProvider/constants';
const metaKey = process.platform === 'win32' ? 'Ctrl' : '⌘';
import {isPoc} from '@/utils/features';
import {isComateIDE} from '@/common/ide';

@injectable()
export class CodeSelectionTipProvider implements vscode.Disposable {
    private decorationType: vscode.TextEditorDecorationType;
    private readonly disposables: vscode.Disposable[] = [];
    private timeoutId: NodeJS.Timeout | null = null;

    constructor() {
        this.decorationType = vscode.window.createTextEditorDecorationType({});

        if (isPoc || isComateIDE) {
            return;
        }

        this.decorationType = vscode.window.createTextEditorDecorationType({
            after: {
                contentText: L10n.t(ChatTipsProviderText.CHAT_TIPS_TITLE, metaKey), // 显示的文本
                color: new vscode.ThemeColor('editorCodeLens.foreground'),
                margin: '0 0 0 1.5rem',
            },
        });

        vscode.window.onDidChangeTextEditorSelection(
            this.onTextSelectionChange,
            this,
            this.disposables
        );
        vscode.window.onDidChangeActiveTextEditor(
            this.onActiveEditorChanged,
            this,
            this.disposables
        );
    }
    private onActiveEditorChanged() {
        vscode.window.visibleTextEditors.forEach(editor => {
            editor.setDecorations(this.decorationType, []);
        });
    }

    private onTextSelectionChange(event: vscode.TextEditorSelectionChangeEvent) {
        const activeEditor = vscode.window.activeTextEditor;
        if (!activeEditor || event.textEditor !== activeEditor || activeEditor?.document?.uri?.scheme !== 'file') {
            return;
        }

        if (this.timeoutId) {
            clearTimeout(this.timeoutId);
        }

        this.timeoutId = setTimeout(
            () => {
                this.applyDecorationIfNeeded(activeEditor);
            },
            300
        ); // 设置延迟为300毫秒
    }

    private applyDecorationIfNeeded(activeEditor: vscode.TextEditor) {
        const selection = activeEditor.selection;
        const selectedText = activeEditor.document.getText(selection);
        if (
            selection.isEmpty || selectedText.trim() === ''
            || activeEditor.selections.length > 1 || selectedText.trim().length < 10
        ) {
            activeEditor.setDecorations(this.decorationType, []);
        }
        else {
            const line = selection.isReversed ? selection.start.line : selection.end.line;
            const range = new vscode.Range(
                line,
                Number.MAX_SAFE_INTEGER,
                line,
                Number.MAX_SAFE_INTEGER
            ); // 最大范围，确保总是在行尾添加

            activeEditor.setDecorations(this.decorationType, []);

            this.decorationType = vscode.window.createTextEditorDecorationType({
                after: {
                    contentText: L10n.t(ChatTipsProviderText.CHAT_TIPS_TITLE, metaKey), // 显示的文本
                    color: new vscode.ThemeColor('editorCodeLens.foreground'),
                    margin: '0 0 0 1.5rem',
                },
            });

            activeEditor.setDecorations(this.decorationType, [range]);
        }
    }

    dispose() {
        this.decorationType.dispose();
        this.disposables.forEach(d => d.dispose());
        if (this.timeoutId) {
            clearTimeout(this.timeoutId);
        }
    }
}
