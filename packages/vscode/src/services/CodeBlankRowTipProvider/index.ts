import {injectable} from 'inversify';
import * as vscode from 'vscode';
import {L10n} from '@/common/L10nProvider/L10n';
import {ChatTipsProviderText} from '@/common/L10nProvider/constants';
import {isPoc} from '@/utils/features';
const metaKey = process.platform === 'win32' ? 'Ctrl' : '⌘';

@injectable()
export class CodeBlankRowTipProvider implements vscode.Disposable {
    private readonly decorationType: vscode.TextEditorDecorationType;
    private readonly disposables: vscode.Disposable[] = [];
    private timeoutId: NodeJS.Timeout | null = null;
    private showTipFlag: boolean = true;
    /**
     * CodeBlankRowTipProvider构造函数
     * 编辑器光标发生变化时，添加幽灵字符
     * 编辑器标签页切换时，清除幽灵字符
     *
     * @constructor
     * @memberof CodeBlankRowTipProvider
     */
    constructor() {
        this.decorationType = vscode.window.createTextEditorDecorationType({});
        this.showTipFlag = true;

        if (isPoc) {
            return;
        }

        this.decorationType = vscode.window.createTextEditorDecorationType({
            after: {
                contentText: L10n.t(ChatTipsProviderText.CHAT_TIPS_CODE_BLANK_ROW_TITLE, metaKey),
                color: new vscode.ThemeColor('editorCodeLens.foreground'),
                margin: '0 0 0 1.5rem',
            },
        });

        vscode.window.onDidChangeTextEditorSelection(
            this.onTextSelectionChange,
            this,
            this.disposables
        );
        vscode.window.onDidChangeActiveTextEditor(
            this.onActiveEditorChanged,
            this,
            this.disposables
        );

    }

    /**
     * onActiveEditorChanged
     *
     * 编辑器标签页切换变化时，清除所有编辑器上的装饰
     *
     * @private
     */
    private onActiveEditorChanged() {
        vscode.window.visibleTextEditors.forEach(editor => {
            editor.setDecorations(this.decorationType, []);
        });
    }

    /**
     * 监听文本选择变化事件，为当前活动的文件类型编辑器时，触发后续空行幽灵字符判断逻辑
     *
     * @param event vscode.TextEditorSelectionChangeEvent 文本选择变化事件对象
     * @returns void 无返回值
     */
    private onTextSelectionChange(event: vscode.TextEditorSelectionChangeEvent) {
        const activeEditor = vscode.window.activeTextEditor;
        if (!activeEditor || event.textEditor !== activeEditor || activeEditor?.document?.uri?.scheme !== 'file') {
            return;
        }

        this.showTipFlag = true;
        this.applyDecorationIfNeeded(activeEditor);
    }

    /**
     * 如果符合空行条件，2秒后生成幽灵字符提示
     *
     * @param activeEditor vscode.TextEditor 当前活动的文本编辑器
     * @private
     */
    private applyDecorationIfNeeded(activeEditor: vscode.TextEditor) {
        const document = activeEditor.document;
        const cursorPosition = activeEditor.selection.active;
        const lineText = document.lineAt(cursorPosition.line).text;
        const isBlankRow = lineText.trim().length === 0;

        if (this.timeoutId) {
            clearTimeout(this.timeoutId);
        }

        if (isBlankRow) {
            activeEditor.setDecorations(this.decorationType, []);
            this.timeoutId = setTimeout(() => {
                if (!this.showTipFlag) {
                    return;
                }
                activeEditor.setDecorations(this.decorationType, [
                    {
                        range: new vscode.Range(cursorPosition, cursorPosition),
                    },
                ]);
            }, 2000);
        } else {
            activeEditor.setDecorations(this.decorationType, []);
        }
    }

    clearTip() {
        const activeEditor = vscode.window.activeTextEditor;
        if (!activeEditor || activeEditor?.document?.uri?.scheme !== 'file') {
            return;
        }
        this.showTipFlag = false;
        activeEditor.setDecorations(this.decorationType, []);
    }

    /**
     * 销毁对象，回收资源。
     *
     * @returns {void} 无返回值
     */
    dispose() {
        this.decorationType.dispose();
        this.disposables.forEach(d => d.dispose());
        if (this.timeoutId) {
            clearTimeout(this.timeoutId);
        }
    }

}
