/* eslint-disable max-lines */
/* eslint-disable  complexity, no-console */
import path from 'node:path';
import * as vscode from 'vscode';
import {LazyServiceIdentifer, inject, injectable} from 'inversify';
import {
    Message,
    AgentList,
    AgentListWithId,
    CommandListWithId,
    MessageType,
    EventMessage,
    ContextType,
} from '@shared/protocols';
import {
    PluginDescription,
    PermissionType,
    getCurrentUserPluginConfigSet,
    localPluginConfig,
    mergePluginConfig,
    getCurrentUserKnowledgeSet,
    NotificationMessage,
    ACTION_COMATE_PLUS_DRAW_CHAT_UPDATE,
    ACTION_COMATE_PLUS_DRAW_CHAT_FINISH,
    ACTION_COMATE_PLUS_DRAW_CHAT_FAIL,
    ACTION_COMATE_PLUS_REQUEST_PERMISSION,
    ACTION_COMATE_PLUS_INITIALIZED,
    ACTION_USER_DETAIL_ERROR,
    ACTION_COMATE_PLUS_CHAT_QUERY,
    ACTION_COMATE_PLUS_WEBVIEW_INIT_DATA,
    CustomCommandPayload,
    ACTION_COMATE_PLUS_CUSTOM_COMMAND,
    ACTION_COMATE_PLUS_AGENT_NOTIFICATION,
    ACTION_COMATE_PLUS_SECTION_CHAT_UPDATE,
    ACTION_COMATE_ADD_CACHE,
    ACTION_COMATE_GET_CACHE,
    ACTION_COMATE_PLUS_CODE_SEARCH,
    ACTION_COMATE_PLUS_BATCH_ACCEPT,
    ACTION_COMATE_PLUS_RECREATE_INDEX,
} from '@comate/plugin-shared-internals';
import {isNumber, debounce} from 'lodash';
import {KnowledgeList, PromptTemplateList} from '@shared/protocols';
import {FUNCTION_COMMENT, INLINE_COMMENT, CODE_EXPLAIN, FUNCTION_SPLIT, OPTIMIZE} from '@shared/constants';
import {
    CMD_ADD_COMMENT,
    CMD_EXPLAIN_FUNCTION,
    CMD_GENERATE_DOCSTRING,
    CMD_HANDLE_INPUT_FOCUS,
    CMD_SPLIT_FUNCTION,
    CMD_OPTIMIZE_FUNCTION,
} from '@/constants';
import {findDocstringInsertionLocation} from '@/utils/docString';
import {isInternal, isSaaS} from '@/utils/features';
import {getDeviceUUIDThrottled} from '@/utils/deviceUUID';
import {getExtensionContextAsync} from '@/utils/extensionContext';
import {L10n} from '@/common/L10nProvider/L10n';
import {ComatePlusText, GlobalText} from '@/common/L10nProvider/constants';
import {LogLevel, info, log} from '@/common/outputChannel';
import {getIdeName} from '@/common/Fetcher';
import {getRepoName} from '@/utils/git';
import {sleep} from '../../utils/common';
import {activeFileContext, getCurrentSelection} from '../../utils/document';
import {Conversation} from '../ChatViewProvider/Conversation';
import {ConfigKey, VSCodeConfigProvider} from '../ConfigProvider';
import {ChatViewProvider} from '../ChatViewProvider';
import {PermissionStorage} from '../PermissionStorage';
import {KernelProvider} from '../KernelProvider';
import {CMD_COMATE_PLUS_CLEAR_PERMISSION} from '../NL2CodeProvider/constants';
import {advancedFeatureUsageCount, generateTrackUuid, licenseValidate, modifyCode, acceptCode} from '../../api';
import {DiffProvider} from '../DiffProvider';
import {TreeSitterProvider} from '../TreeSitterProvider';
import {filterSplitFunctionNode} from '../FunctionSplitProvider';
import {chatActionDelayedReporter} from '../ChatViewProvider/chatActionDelayedReporter';
import {ChatBaseProvider, ChatResponseProxy} from '../ChatBaseProvider';
import {QueryVisibilitySelectorProvider} from '../QueryVisibilitySelectorProvider';
import {TextResponse} from '../ChatViewProvider/TextResponse';
import {LogCategory, LogUploaderProvider} from '../LogUploaderProvider';
import {CustomizeProvider} from '../CustomizeProvider';
import {computeRepoId} from '../EmbeddingsService/embeddingUtils';
import {PromptTemplateProvider} from '../PromptTemplateProvider';
import {searchKeywords} from '../AutoComateChatSession/tools';
import {EmbeddingsService} from '../EmbeddingsService';
import {EmbeddingsController, CMD_RECREATE_INDEX} from '../EmbeddingsService/controller';
import {
    CMD_BUILTIN_COMMAND,
    CMD_COMATE_PLUS_START_COMMAND,
    CMD_COMATE_PLUS_START_SESSION,
    CMD_WEBVIEW_INIT,
    CMD_COMATE_PLUS_UPDATE_KNOWLEDGE,
    SHOW_CHAT_PANEL_AND_CMD_COMATE_PLUS_START,
    CMD_PADDLE_CONVERT,
    CMD_ADD_CACHE,
    CMD_GET_CACHE,
} from './constants';
import {getPluginTipConfig, getPluginTipContent, ifShowTip} from './pluginTip';
import {preProcessKnowledge, extractJsxGeneratedContent, getFileContentsFromList} from './utils';

const ENGINE_INIT_SUCCESS_KEY = 'baidu.comate.engineInitSuccess';

interface GenerateParams {
    query: string;
    data?: unknown;
    pluginName: string;
    capability?: string;
    name: string;
    icon: string;
    extra: {
        capability?: string;
        knowledgeList?: KnowledgeList[];
        renderJSX?: boolean;
        capabilityDisplayName?: string;
        isRegenerated?: boolean;
    };
}

export interface PluginConfigs {
    [key: string]: {
        enabled: boolean;
        config: Record<string, unknown>;
    };
}

const getPermissionText = (permissionType: string) => {
    switch (permissionType) {
        case PermissionType.WorkspaceFileSystem:
            return L10n.t(ComatePlusText.PERMISSION_WORKSPACE);
        case PermissionType.FullDiskFileSystem:
            return L10n.t(ComatePlusText.PERMISSION_DISK);
        default:
            return L10n.t(ComatePlusText.PERMISSION_USER);
    }
};

export interface KnowledgeSetBase {
    uuid: string;
    name: string;
    type: string;
    description: string;
}

const slashMap = {
    [FUNCTION_COMMENT]: CMD_GENERATE_DOCSTRING,
    [INLINE_COMMENT]: CMD_ADD_COMMENT,
    [CODE_EXPLAIN]: CMD_EXPLAIN_FUNCTION,
    [FUNCTION_SPLIT]: CMD_SPLIT_FUNCTION,
    [OPTIMIZE]: CMD_OPTIMIZE_FUNCTION,
};

const cababilityToMessageTypeMap = {
    functionComment: 'docstring',
    inlineComment: 'inlineComment',
    explain: 'explain',
    functionSplit: 'splitFunction',
    optimize: 'optimizeFunction',
};

@injectable()
export class ComatePlusChatSession extends ChatBaseProvider implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];
    private readonly replyMap: Map<number, TextResponse> = new Map();
    private readonly pluginMap: Map<string, PluginDescription> = new Map();
    private readonly permissionStorage: PermissionStorage = new PermissionStorage();
    private readonly messageIdQueue: Set<number> = new Set<number>();
    constructor(
        @inject(new LazyServiceIdentifer(() => ChatViewProvider)) private readonly chatViewProvider: ChatViewProvider,
        @inject(KernelProvider) private readonly kernelProvider: KernelProvider,
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider,
        @inject(DiffProvider) readonly diffProvider: DiffProvider,
        @inject(QueryVisibilitySelectorProvider) private readonly queryVisibilitySelectorProvider:
            QueryVisibilitySelectorProvider,
        @inject(TreeSitterProvider) private readonly treeSitterProvider: TreeSitterProvider,
        @inject(LogUploaderProvider) private readonly logUploader: LogUploaderProvider,
        @inject(CustomizeProvider) readonly customizeProvider: CustomizeProvider,
        @inject(PromptTemplateProvider) readonly promptTemplateProvider: PromptTemplateProvider,
        @inject(EmbeddingsService) readonly embeddingsService: EmbeddingsService,
        @inject(EmbeddingsController) private readonly embeddingsController: EmbeddingsController
    ) {
        super(diffProvider);
    }

    private getChatResponseProxy(reply: TextResponse): ChatResponseProxy {
        const chatResponseProxy = {
            getMessageId: () => String(reply.message.id),
            getChatId: () => undefined,
            getMessageContent: () => reply.message.content,
            getTrackUuid: () => reply.message.extra?.uuid,
            getResponseType: 'async' as const,
        };
        return chatResponseProxy;
    }

    start() {
        let content = '';
        this.kernelProvider.client?.onRequest(ACTION_COMATE_PLUS_DRAW_CHAT_UPDATE, e => {
            log(LogLevel.Debug, 'COMATE PLUSE CLIENT REC <--------', ACTION_COMATE_PLUS_DRAW_CHAT_UPDATE);
            const reply = this.replyMap.get(e.messageId);

            if (reply && reply?.message.status === 'inProgress') {
                this.advancedFeatureUsageCount(e.messageId);
                content = e.chunk.content || '';
                const chatResponseProxy = this.getChatResponseProxy(reply);
                const {diff, accept, copy, smartApply} = this.defaultActions(chatResponseProxy, undefined, undefined, {
                    skipCodeRetention: true,
                    ignoreSmartApplyFeature: !e.enableSmartApply,
                });
                const {
                    newFile,
                    insertIntoTerminal,
                    replaceToFile,
                    insertToFile,
                    showFileInsertDiff,
                    showFileReplaceDiff,
                    acceptDir,
                    openZulu,
                    viewHtml,
                    acceptDependentFiles,
                    acceptWithDependentFiles,
                    viewFile,
                    batchAccept,
                } = this.extraActions(chatResponseProxy);
                reply.updateWithExtra(e.chunk.content || '', {
                    capability: e.capabilityName,
                }, {
                    diff,
                    accept,
                    copy,
                    newFile,
                    insertIntoTerminal,
                    replaceToFile,
                    insertToFile,
                    showFileInsertDiff,
                    showFileReplaceDiff,
                    acceptDir,
                    openZulu,
                    viewHtml,
                    acceptDependentFiles,
                    acceptWithDependentFiles,
                    viewFile,
                    smartApply,
                    batchAccept,
                });
            }
            console.log('DRAW_CHAT comate receive message', e, reply);
        });

        // this.kernelProvider.client?.onRequest(ACTION_COMATE_PLUS_DRAW_CHAT_APPEND, e => {
        //     const reply = this.replyMap.get(e.messageId);
        //     this.advancedFeatureUsageCount(e.messageId);
        //     if (reply) {
        //         content += e.chunk.content || '';
        //         const chatResponseProxy = this.getChatResponseProxy(reply);
        //         const {diff, accept, copy} = this.defaultActions(chatResponseProxy);
        //         const {newFile, insertIntoTerminal} = this.extraActions(chatResponseProxy);
        //         reply.updateWithExtra(content || '', {
        //             capability: e.capabilityName,
        //         }, {diff, accept, copy, newFile, insertIntoTerminal});
        //     }
        //     console.log('DRAW_CHAT comate receive message', e);
        // });

        this.kernelProvider.client?.onRequest(ACTION_COMATE_PLUS_SECTION_CHAT_UPDATE, e => {
            log(LogLevel.Debug, 'COMATE PLUSE CLIENT REC <--------', ACTION_COMATE_PLUS_SECTION_CHAT_UPDATE);
            const reply = this.replyMap.get(e.messageId);

            if (reply && reply?.message.status === 'inProgress') {
                content = e.chunk.content || '';
                this.advancedFeatureUsageCount(e.messageId);
                const chatResponseProxy = this.getChatResponseProxy(reply);
                const {diff, accept, copy} = this.defaultActions(chatResponseProxy, undefined, undefined, {
                    skipCodeRetention: true,
                });
                const {
                    newFile,
                    insertIntoTerminal,
                    replaceToFile,
                    insertToFile,
                    showFileInsertDiff,
                    showFileReplaceDiff,
                    batchAccept,
                } = this.extraActions(chatResponseProxy);
                reply.updateWithSections(
                    content,
                    e.chunk.sections,
                    undefined,
                    {
                        diff,
                        accept,
                        copy,
                        newFile,
                        insertIntoTerminal,
                        replaceToFile,
                        insertToFile,
                        showFileInsertDiff,
                        showFileReplaceDiff,
                        batchAccept,
                        ...e.chunk.actionSet.actions,
                    },
                    e.chunk.actionSet.actionConfigs
                );
            }
            console.log('SECTION_CHAT comate receive message', e, reply);
        });

        this.kernelProvider.client?.onRequest(ACTION_COMATE_PLUS_DRAW_CHAT_FINISH, async e => {
            log(LogLevel.Debug, 'COMATE PLUSE CLIENT REC <--------', ACTION_COMATE_PLUS_DRAW_CHAT_FINISH);
            const reply = this.replyMap.get(e.messageId);

            if (reply) {
                const chatResponseProxy = this.getChatResponseProxy(reply);
                const {diff, accept, copy} = this.defaultActions(chatResponseProxy, undefined, undefined, {
                    skipCodeRetention: true,
                });
                const {newFile, insertIntoTerminal} = this.extraActions(chatResponseProxy);
                reply.success(content || '', {accept, diff, copy, newFile, insertIntoTerminal}, String(e.messageId));
                content = '';
            }
            console.log('DRAW_CHAT_FINISH comate receive message', e);
        });

        this.kernelProvider.client?.onRequest(ACTION_COMATE_PLUS_DRAW_CHAT_FAIL, (e: any) => {
            log(LogLevel.Debug, 'COMATE PLUSE CLIENT REC <--------', ACTION_COMATE_PLUS_DRAW_CHAT_FAIL);
            const reply = this.replyMap.get(e.messageId);
            if (reply) {
                reply.fail(e.chunk.content || '');
                content = '';
            }
            console.log('DRAW_CHAT_FAIL comate receive message', e);
        });

        this.kernelProvider.client?.onRequest(ACTION_COMATE_PLUS_REQUEST_PERMISSION, async (e: any) => {
            log(LogLevel.Debug, 'COMATE PLUSE CLIENT REC <--------', ACTION_COMATE_PLUS_REQUEST_PERMISSION);
            // true: 用户授权过；false: 用户拒绝授权过；undefined: 用户未授权，需要弹框请求授权
            const isAccepted = await this.permissionStorage.get(e.data.payload.pluginName, e.data.payload.type);

            const params = {
                status: 'success',
                payload: {
                    sessionId: e.sessionId,
                    execution: e.execution,
                    data: {
                        payload: {
                            pluginName: e.data.payload.pluginName,
                            type: e.data.payload.type,
                            granted: isAccepted,
                        },
                        action: e.data.action,
                    },
                },
            };
            if (isAccepted === undefined || !isAccepted) {
                const acceptText = L10n.t(GlobalText.COMMON_AUTHORIZE);
                const res = await vscode.window.showInformationMessage(
                    L10n.t(
                        ComatePlusText.AUTH_MESSAGE,
                        e.data.payload.pluginName,
                        getPermissionText(e.data.payload.type)
                    ),
                    acceptText,
                    L10n.t(GlobalText.COMMON_DENY)
                );
                const isAccepted = res === acceptText;
                await this.permissionStorage.update(e.data.payload.pluginName, e.data.payload.type, isAccepted);
                params.payload.data.payload.granted = isAccepted;
                return params;
            }
            return params;
        });

        this.kernelProvider.client?.onRequest(ACTION_COMATE_PLUS_INITIALIZED, async () => {
            log(LogLevel.Debug, 'COMATE PLUSE CLIENT REC <--------', ACTION_COMATE_PLUS_INITIALIZED);
            console.log('Comate Engine initialized');
            this.chatViewProvider.engineInitEvent();
            await this.updateWebviewData();

            const context = await getExtensionContextAsync();
            const prevDate = context.globalState.get<string>(ENGINE_INIT_SUCCESS_KEY);
            const nowDate = new Date().toLocaleDateString();
            if (prevDate !== nowDate) {
                this.logUploader.logUserAction({
                    category: LogCategory.EngineInit,
                    content: 'Comate Engine initialized',
                });
                context.globalState.update(ENGINE_INIT_SUCCESS_KEY, nowDate);
            }

            return {
                status: 'success',
                payload: {},
            };
        });

        this.kernelProvider.client?.onRequest(ACTION_COMATE_PLUS_RECREATE_INDEX, async (e: any) => {
            log(LogLevel.Debug, 'COMATE PLUSE CLIENT REC <--------', ACTION_COMATE_PLUS_RECREATE_INDEX);
            vscode.commands.executeCommand(CMD_RECREATE_INDEX, {command: 'recreate'});
            try {
                const maxDuration = e.data.payload.codeSearchPayload.maxDuration;
                const isProgressComplete = await this.checkProgress(maxDuration ? maxDuration * 1000 : undefined);
                return isProgressComplete;
            }
            catch (error) {
                console.error('Index recreation failed', error);
                return false;
            }

        });

        this.kernelProvider.client?.onRequest(ACTION_COMATE_PLUS_CODE_SEARCH, async (e: any) => {
            log(LogLevel.Debug, 'COMATE PLUSE CLIENT REC <--------', ACTION_COMATE_PLUS_CODE_SEARCH);
            const rootPath = vscode.workspace.workspaceFolders?.[0].uri.fsPath ?? '';
            const [
                embeddingSearchResults,
                keywordSearchResult,
            ] = await Promise.all([
                this.embeddingsService.search(
                    e.data.payload.codeSearchPayload.queries,
                    rootPath,
                    e.data.payload.codeSearchPayload.path
                ),
                // 关键字检索暂时没有限制检索范围
                searchKeywords(e.data.payload.codeSearchPayload.queries),
            ]);
            const embeddingCodeChunks = embeddingSearchResults.data.map(item => {
                return {...item, type: 'embedding'};
            });
            const keywordCodeChunks = keywordSearchResult.map(item => item.codeChunks).flat();
            const codeChunks = [...embeddingCodeChunks, ...keywordCodeChunks];
            return {
                codeChunks,
            };
        });

        this.kernelProvider.client?.onRequest(ACTION_COMATE_PLUS_BATCH_ACCEPT, async (e: any) => {
            log(LogLevel.Debug, 'COMATE PLUSE CLIENT REC <--------', ACTION_COMATE_PLUS_BATCH_ACCEPT);
            const generatedContent = getFileContentsFromList(e.data.payload.batchAcceptPayload.paths);
            const context = await getExtensionContextAsync();
            const rootPath = vscode.workspace.workspaceFolders?.[0].uri.fsPath ?? '';
            const params = {
                username: this.configProvider.getConfig<string>(ConfigKey.Username, ''),
                content: 'UNDEFINED',
                generatedContent,
                model: e.data.payload?.pluginName ?? '',
                function: e.data.payload?.capabilityName ?? '',
                path: 'UNDEFINED.js',
                col: '1',
                row: '1',
                ide: getIdeName() ?? '',
                repo: await getRepoName(rootPath),
                shown: true,
                multiline: true,
                pluginVersion: context.extension.packageJSON.version,
            };

            try {
                const track = await generateTrackUuid(params);

                if (track.status === 500) {
                    this.logUploader.logError(
                        LogCategory.TrackUUIDGenerateFailed,
                        `COMATE_PLUS_BATCH_ACCEPT GENERATE TRACK UUID FAILED (STATUS 500): ${track.data?.message}`
                    );
                }
                if (track.data.data?.uuid) {
                    acceptCode({
                        uuid: track.data.data?.uuid,
                        accepted: true,
                        content: '',
                        generatedContent,
                    });
                }
            }
            catch (e) {
                this.logUploader.logError(
                    LogCategory.TrackUUIDGenerateFailed,
                    `COMATE_PLUS_BATCH_ACCEPT GENERATE TRACK UUID FAILED (EXCEPTION): ${e}`
                );
            }
        });

        this.kernelProvider.client?.onNotification(ACTION_USER_DETAIL_ERROR, () => {
            // saas版本已经有通知了
            !isSaaS && info('获取用户信息失败，请检查 Comate 配置中的用户名是否正确');
            this.logUploader.logUserAction({
                category: LogCategory.UserApiError,
            });
        });

        this.kernelProvider.client?.onNotification(
            ACTION_COMATE_PLUS_AGENT_NOTIFICATION,
            (params: NotificationMessage) => {
                const reason = params?.reason ?? 'Comate Agent异常，请联系 Comate 团队反馈。';
                switch (params?.type) {
                    case 'info':
                        vscode.window.showInformationMessage(reason);
                        break;
                    case 'warning':
                        vscode.window.showWarningMessage(reason);
                        break;
                    case 'error':
                        vscode.window.showErrorMessage(reason);
                        break;
                    default:
                        vscode.window.showErrorMessage('Comate Agent异常，请联系 Comate 团队反馈。');
                }
            }
        );

        const debounceSendActionQuerySelectorRequest = debounce(
            this.queryVisibilitySelectorProvider.sendActionQuerySelectorRequest.bind(this),
            2000,
            {leading: true}
        );

        this.disposables.push(
            vscode.commands.registerCommand(CMD_BUILTIN_COMMAND, this.handleComateAgent.bind(this)),
            vscode.commands.registerCommand(CMD_COMATE_PLUS_START_SESSION, this.handleAskComatePlus.bind(this)),
            vscode.commands.registerCommand(
                SHOW_CHAT_PANEL_AND_CMD_COMATE_PLUS_START,
                this.handleShowChatPanelAndAskComatePlus
            ),
            vscode.commands.registerCommand(CMD_COMATE_PLUS_START_COMMAND, this.handleSubmitCommand.bind(this)),
            vscode.commands.registerCommand(CMD_COMATE_PLUS_CLEAR_PERMISSION, this.clearPermission),
            vscode.commands.registerCommand(CMD_WEBVIEW_INIT, () => {
                console.log('Comate Webview initialized');
                this.updateWebviewData();
            }),
            vscode.commands.registerCommand(CMD_HANDLE_INPUT_FOCUS, () => {
                debounceSendActionQuerySelectorRequest();
                this.promptTemplateProvider.updatePromptTemplates();
                vscode.workspace.saveAll();
            }),
            vscode.commands.registerCommand(CMD_COMATE_PLUS_UPDATE_KNOWLEDGE, () => {
                console.log('Comate Webview updated');
                this.updateKnowledgeList();
            }),
            vscode.commands.registerCommand(CMD_ADD_CACHE, this.addCache.bind(this)),
            vscode.commands.registerCommand(CMD_GET_CACHE, this.getCache.bind(this))
        );
    }

    private readonly handleShowChatPanelAndAskComatePlus = async (
        {data, query, pluginName, capability, source, originURI, traceId}: {
            query: string;
            pluginName: string;
            capability?: string;
            data?: any;
            source?: string;
            originURI?: string;
            traceId?: string;
        }
    ) => {
        try {
            this.logUploader.logUserAction({
                category: LogCategory.OpenComatePlusFromUrlSchema,
                source,
                content: {
                    query,
                    originURI,
                    pluginName,
                    capability,
                    data,
                    traceId,
                },
            });

            vscode.commands.executeCommand('baidu.comate.showChatPanel', {
                source: source ?? SHOW_CHAT_PANEL_AND_CMD_COMATE_PLUS_START,
            });
            this.handleAskComatePlus(query, pluginName, capability, [], data);
        }
        catch (err) {
            console.error(err);
        }
    };


    updateWebviewData = async () => {
        await this.chatViewProvider.waitingEngineInit();
        const [commandList, pluginConfigs] = await this.fetchIntegratedCommandList();
        commandList[0] = await Promise.all(commandList[0].map(async item => ({
            ...item,
            icon: await this.redirectIconPath(item),
        })));
        commandList[1] = await Promise.all(commandList[1].map(async item => ({
            ...item,
            owner: {
                ...item.owner,
                icon: await this.redirectIconPath(item.owner),
            },
        })));
        await this.chatViewProvider.updateChatCompletionList(commandList, pluginConfigs);
    };

    clearPermission = async () => {
        await this.permissionStorage.clearAll();
        vscode.window.showInformationMessage(L10n.t(ComatePlusText.CLEAR_PERMISSION));
    };

    log = (value: unknown) => {
        this.chatViewProvider.sendDataToWebview(EventMessage.WebviewConsoleLogEvent, value);
    };
    async generate(input: string, conversation: Conversation, chatHistories: Message[], context: string | undefined, {
        query,
        pluginName,
        capability,
        name,
        icon,
        extra,
        data,
    }: GenerateParams) {
        const reply = conversation.pluginAddResponse('stream', '', 'inProgress', {
            name,
            icon,
            extra,
        }, {
            regenerate: () => {
                this.generate(input, conversation, chatHistories, context, {
                    query,
                    pluginName,
                    capability,
                    name,
                    icon,
                    extra: {
                        ...extra,
                        isRegenerated: true,
                    },
                    data,
                });
            },
            // 打开全文复制按钮
            copyAll: () => {},
        });

        this.replyMap.set(reply.message.id, reply);
        const userName = this.configProvider.getConfig<string>(ConfigKey.Username);
        // saas 先校验 license
        if (isSaaS) {
            // 能使用开放平台，说明至少有license，所以只可能是失效了
            const key = this.configProvider.getLicense();
            let isActive = false;
            let errMsg = '';
            if (key) {
                const res = await licenseValidate(key);
                isActive = res.data ?? false;
                errMsg = res.message ?? 'Please check your license';
            }
            if (!isActive) {
                reply.fail(errMsg);
                return;
            }
        }
        // 这里的uuid使用messageId是为了在消息结束时，根据这个messageid去执行chatActionDelayedReporter中的回调
        // 回调也就是下文的上传完整的message 内容
        reply.updateWithExtra('', {
            uuid: String(reply.message.id),
        });

        const rootPath = vscode.workspace.workspaceFolders?.[0].uri.fsPath ?? '';
        const activeContext = activeFileContext();
        // chatActionDelayedReporter 队列
        chatActionDelayedReporter.add(String(reply.message.id), async () => {
            const assistantMessage = reply.conversation.getMessageById(reply.message.id);
            const content = assistantMessage?.content;
            const userMessage = (assistantMessage && isNumber(assistantMessage?.replyTo))
                ? reply.conversation.getMessageById(assistantMessage.replyTo)
                : undefined;
            // 上报的content的中新增用户选择的代码 即query+code
            const queryWithCode = (userMessage?.content || userMessage?.code)
                ? (userMessage?.content || '') + (userMessage?.code || '')
                : 'UNDEFINED';
            const context = await getExtensionContextAsync();
            const rootPath = vscode.workspace.workspaceFolders?.[0].uri.fsPath ?? '';
            const params = {
                username: this.configProvider.getConfig<string>(ConfigKey.Username, ''),
                content: queryWithCode,
                generatedContent: assistantMessage?.content || 'UNDEFINED',
                model: assistantMessage?.name ?? '',
                // 只有自定义指令采集数据的时候需要重写function字段值
                function: assistantMessage?.name === 'PromptTemplate'
                    ? (assistantMessage?.extra?.capabilityDisplayName ?? '')
                    : (assistantMessage?.extra?.capability ?? ''),
                path: activeContext.activeFileName || 'UNDEFINED.js',
                col: '1',
                row: '1',
                ide: getIdeName() ?? '',
                repo: await getRepoName(rootPath),
                shown: true,
                multiline: true,
                pluginVersion: context.extension.packageJSON.version,
            };
            if (isSaaS) {
                // @ts-ignore
                params.device = (await getDeviceUUIDThrottled()) ?? '';
                // @ts-ignore
                params.key = this.configProvider.getConfig<string>(ConfigKey.Key);
            }
            try {
                // jsx上报生成行需要单独做处理
                if (typeof params.generatedContent !== 'string') {
                    params.generatedContent = extractJsxGeneratedContent(params?.generatedContent);
                }
                // PV
                const track = await generateTrackUuid(params);

                if (track.status === 500) {
                    this.logUploader.logError(
                        LogCategory.TrackUUIDGenerateFailed,
                        `GENERATE TRACK UUID FAILED (STATUS 500): ${track.data?.message}`
                    );
                }

                const uuid = track.data?.data?.uuid;
                reply.updateWithExtra(content || '', {
                    // 采纳传uuid
                    uuid,
                });
                const chatResponseProxy = this.getChatResponseProxy(reply);
                const chatId = chatResponseProxy.getChatId();
                reply.updateWithExtra(content || '', {}, {
                    feedback: options => {
                        // 好心人，记得考虑一下一方插件是不是也需要更新
                        // packages/vscode/src/services/ChatBaseProvider.ts
                        modifyCode({uuid: uuid, chatId: chatId, ...options});
                    },
                });
            }
            catch (e) {
                this.logUploader.logError(
                    LogCategory.TrackUUIDGenerateFailed,
                    `GENERATE TRACK UUID FAILED (EXCEPTION): ${e}`
                );
            }
        });
        const knowledgeList = await preProcessKnowledge(extra.knowledgeList || []);
        const repoId = await computeRepoId(rootPath);

        return this.kernelProvider.sendRequest(ACTION_COMATE_PLUS_CHAT_QUERY, {
            isRegenerated: extra?.isRegenerated,
            messageId: reply.message.id,
            pluginName,
            capability,
            query,
            data,
            knowledgeList: knowledgeList?.map(v => ({...v, uuid: v.id})) ?? [],
            sessionUuid: this.chatViewProvider.sessionUuid,
            context: {
                query,
                ...activeContext,
            },
            systemInfo: {
                // 这里的信息从 engine 插入
                repoId,
                userDetail: {
                    name: userName,
                    displayName: userName, // 中文名从接口来
                    email: userName + '@baidu.com',
                },
                cwd: rootPath,
            },
        });
    }

    async submitCommand(payload: CustomCommandPayload, conversation: Conversation, {
        pluginName,
        name,
        icon,
        extra,
    }: Omit<GenerateParams, 'query'>) {
        const reply = conversation.pluginAddResponse('stream', '', 'inProgress', {
            name,
            icon,
            extra,
        });
        this.replyMap.set(reply.message.id, reply);
        // saas 先校验 license
        if (isSaaS) {
            // 能使用开放平台，说明至少有license，所以只可能是失效了
            const key = this.configProvider.getLicense();
            let isActive = false;
            let errMsg = '';
            if (key) {
                const res = await licenseValidate(key);
                isActive = res.data ?? false;
                errMsg = res.message ?? 'Please check your license';
            }
            if (!isActive) {
                reply.fail(errMsg);
                return;
            }
        }
        // 这里的uuid使用messageId是为了在消息结束时，根据这个messageid去执行chatActionDelayedReporter中的回调
        // 回调也就是下文的上传完整的message 内容
        reply.updateWithExtra('', {
            uuid: String(reply.message.id),
        });
        const activeContext = activeFileContext();
        chatActionDelayedReporter.add(String(reply.message.id), async () => {
            const assistantMessage = reply.conversation.getMessageById(reply.message.id);
            const content = assistantMessage?.content;
            const userMessage = (assistantMessage && isNumber(assistantMessage?.replyTo))
                ? reply.conversation.getMessageById(assistantMessage.replyTo)
                : undefined;
            const context = await getExtensionContextAsync();
            const params = {
                username: this.configProvider.getConfig<string>(ConfigKey.Username, ''),
                content: userMessage?.content || 'UNDEFINED',
                generatedContent: assistantMessage?.content || 'UNDEFINED',
                model: assistantMessage?.name ?? '',
                function: assistantMessage?.extra?.capability ?? '',
                path: activeContext.activeFileName || 'UNDEFINED.js',
                col: '1',
                row: '1',
                ide: assistantMessage?.name ?? '',
                repo: '',
                shown: true,
                multiline: true,
                pluginVersion: context.extension.packageJSON.version,
            };

            if (isSaaS) {
                // @ts-ignore
                params.device = (await getDeviceUUIDThrottled()) ?? '';
                // @ts-ignore
                params.key = this.configProvider.getConfig<string>(ConfigKey.Key);
            }
            if (typeof params.generatedContent !== 'string') {
                params.generatedContent = extractJsxGeneratedContent(params.generatedContent);
            }
            try {
                const track = await generateTrackUuid(params);

                if (track.status === 500) {
                    this.logUploader.logError(
                        LogCategory.TrackUUIDGenerateFailed,
                        `GENERATE TRACK UUID FAILED (STATUS 500): ${track.data?.message}`
                    );
                }

                reply.updateWithExtra(content || '', {
                    uuid: track.data?.data?.uuid,
                });
            }
            catch (e) {
                this.logUploader.logError(
                    LogCategory.TrackUUIDGenerateFailed,
                    `GENERATE TRACK UUID FAILED (EXCEPTION): ${e}`
                );
            }
        });
        return this.kernelProvider.sendRequest(ACTION_COMATE_PLUS_CUSTOM_COMMAND, {
            messageId: reply.message.id,
            context: activeContext,
            customCommandPayload: payload,
            pluginName,
        });
    }

    private async handleAskComatePlus(
        query: string,
        pluginName: string,
        capability?: string,
        knowledgeList?: KnowledgeList[],
        data?: unknown,
        // 是否禁用自动携带划选代码作为提问上下文
        disableCode?: boolean
    ) {
        if (pluginName === 'f2c' && !vscode.workspace.workspaceFolders) {
            vscode.commands.executeCommand('baidu.comate.f2c.createWorkspace', query);
            return;
        }

        if (pluginName === 'paddle' && capability === 'code-convert') {
            vscode.commands.executeCommand(CMD_PADDLE_CONVERT, query, pluginName, capability, knowledgeList);
            return;
        }
        const tipConfig = getPluginTipConfig(pluginName, capability || '', this.chatViewProvider.chatCompletionList[0]);
        const tipContent = getPluginTipContent(tipConfig);
        const [code] = getCurrentSelection();
        const conversationCode = ifShowTip(tipConfig) ? tipContent : undefined;
        const conversation = this.chatViewProvider.createConversation(
            query,
            'comatePlus',
            disableCode ? undefined : conversationCode
        );
        conversation.updateView();
        // NOTE: 需要等待插件加载完成后再触发消息，否则例如三方插件可能会卡住
        await this.chatViewProvider.waitingEngineInit();
        await this.generate(query, conversation, [], disableCode ? undefined : code, {
            query,
            data,
            pluginName,
            capability,
            name: pluginName,
            icon: this.pluginMap.get(pluginName)?.icon ?? '',
            extra: {
                capability,
                knowledgeList,
                renderJSX: true,
                capabilityDisplayName: (data as any)?.commandDisplayName,
            },
        });
    }

    private async handleSubmitCommand(payload: CustomCommandPayload) {
        const conversation = this.chatViewProvider.createConversation(
            payload.replyText,
            'comatePlus',
            undefined
        );
        conversation.updateView();
        const {pluginName} = payload;
        await this.submitCommand(payload, conversation, {
            pluginName,
            name: pluginName,
            icon: this.pluginMap.get(pluginName)?.icon ?? '',
            extra: {
                renderJSX: true,
            },
        });
    }

    // 获取插件、能力、知识列表
    async fetchIntegratedCommandList(): Promise<[
        [AgentListWithId[], CommandListWithId[], KnowledgeList[], PromptTemplateList[]],
        PluginConfigs,
    ]> {
        try {
            const {
                payload: [
                    [
                        agentListWithId,
                        commandListWithId,
                        knowledgeList,
                        promptTemplateList,
                    ],
                    pluginConfigs,
                ],
            }: any = await this.kernelProvider.client?.sendRequest(ACTION_COMATE_PLUS_WEBVIEW_INIT_DATA, {});
            return [[agentListWithId, commandListWithId, knowledgeList, promptTemplateList], pluginConfigs];
        }
        catch (e) {
            if (e instanceof Error) {
                log(LogLevel.Error, 'GetEngineWebviewInitDataFailed', e.message);
                this.logUploader.logUserAction(
                    {category: LogCategory.getEngineWebviewInitDataFailed, content: e.message},
                    'error'
                );
            }
            // 如果engine没拉起来，保证插件配置可用
            const userName = this.configProvider.getConfig<string>(ConfigKey.Username);
            const license = this.configProvider.getConfig<string>(ConfigKey.Key);
            const userIdentifier = isInternal ? userName : license;
            const customizeService = this.customizeProvider.getComputedHost();
            const workspace = vscode.workspace.workspaceFolders;
            const configSet = userIdentifier
                ? await getCurrentUserPluginConfigSet(userIdentifier)
                : [];
            // @ts-ignore
            const localConfig = await localPluginConfig(workspace);
            const cloudConfig = Object.fromEntries(
                configSet.map((v: any) => [
                    v.pluginName,
                    {
                        enabled: v.configValues.enabled ?? true,
                        config: v.configValues.values,
                    },
                ])
            );
            const mergedConfig = mergePluginConfig(cloudConfig, localConfig);
            const originalKnowledgeSet = userIdentifier
                ? await getCurrentUserKnowledgeSet(
                    userIdentifier,
                    customizeService,
                    userName
                )
                : [];
            const knowledgeSet = originalKnowledgeSet.map(v => ({
                id: v.uuid,
                name: v.name,
                type: v.type as ContextType,
                retrievalType: v.fileContentType || 'TEXT',
            }));
            return [[[], [], knowledgeSet || [], []], mergedConfig || {}];
        }
    }

    // 重定向icon路径
    async redirectIconPath(item: AgentList) {
        const baseName = path.basename(item.icon || '');
        const res = await this.chatViewProvider.resourcePath(`assets/plugins/${item.name}/${baseName}`);
        return res?.toString() || '';
    }

    handleComateAgent(slash?: keyof typeof slashMap) {
        const activeEditor = vscode.window.activeTextEditor;
        const document = activeEditor?.document;
        const selection = activeEditor?.selection;
        if (selection) {
            this.lastSelection = selection;
        }

        if (document && this.lastSelection?.active) {
            if (slash === FUNCTION_COMMENT || slash === FUNCTION_SPLIT || slash === INLINE_COMMENT) {
                let functionNodes = this.treeSitterProvider.functionNodeOfPosition(
                    document,
                    this.lastSelection.active
                );
                if (functionNodes.length === 0) {
                    // !【重要】functionNodeOfSelection 和 functionNodeOfPosition 都会调用 treeSitterProvider的getDocumentTree方法，
                    // getDocumentTree内部会调用tree.delete()，导致nodes内部子节点被删除，后续计算会出问题
                    // 所以这里只在functionNodes为空时，才计算functionNodeOfSelection，否则会出现问题
                    const selectedFunctionNodes = this.treeSitterProvider.functionNodeOfSelection(document);
                    // eslint-disable-next-line max-depth
                    if (selectedFunctionNodes.length > 0) {
                        functionNodes = selectedFunctionNodes;
                    }
                    else {
                        const createConversation = this.chatViewProvider.createConversation(
                            '',
                            cababilityToMessageTypeMap[slash] as MessageType
                        );
                        createConversation.addResponse(
                            'text',
                            L10n.t(ComatePlusText.NOT_A_FUNCTION),
                            'failed'
                        );
                        return;
                    }
                }
                const firstNode = functionNodes[0];
                if (slash === FUNCTION_SPLIT && !filterSplitFunctionNode(firstNode)) {
                    const createConversation = this.chatViewProvider.createConversation(
                        '',
                        'splitFunction'
                    );
                    createConversation.addResponse(
                        'text',
                        L10n.t(ComatePlusText.SPLIT_ERROR),
                        'failed'
                    );
                    return;
                }
                const range = new vscode.Range(
                    new vscode.Position(firstNode.startPosition.row, firstNode.startPosition.column),
                    new vscode.Position(firstNode.endPosition.row, firstNode.endPosition.column)
                );
                const location = findDocstringInsertionLocation(document, firstNode);
                const {position, padding} = location;
                vscode.commands.executeCommand(
                    slashMap[slash as keyof typeof slashMap],
                    document,
                    range,
                    position,
                    padding,
                    functionNodes.length > 1
                );
            }
            else {
                vscode.commands.executeCommand(
                    slashMap[slash as keyof typeof slashMap],
                    document,
                    activeEditor.selection,
                    false
                );
            }
        }
        else {
            const createConversation = this.chatViewProvider.createConversation(
                '',
                slash ? cababilityToMessageTypeMap[slash] as MessageType : 'comatePlus'
            );
            createConversation.addResponse(
                'text',
                L10n.t(ComatePlusText.NO_SELECTION),
                'failed'
            );
        }
    }

    // 使用开放平台要记次，只在saas生效
    advancedFeatureUsageCount(messageId: number) {
        if (isSaaS) {
            if (!this.messageIdQueue.has(messageId)) {
                try {
                    advancedFeatureUsageCount(this.configProvider.getLicense());
                }
                catch {
                    // nothing to do
                }
            }
            this.messageIdQueue.add(messageId);
            // 超过50个就清理一下
            if (this.messageIdQueue.size > 50) {
                const iterator = this.messageIdQueue.values();
                for (let i = 0; i < 40; i++) {
                    const item = iterator.next().value;
                    item && this.messageIdQueue.delete(item);
                }
            }
        }
    }

    async updateKnowledgeList() {
        try {
            const userName = this.configProvider.getConfig<string>(ConfigKey.Username);
            const license = this.configProvider.getConfig<string>(ConfigKey.Key);
            const customizeService = this.customizeProvider.getComputedHost();
            const userIdentifier = isInternal ? userName : license;
            const originalKnowledgeSet = userIdentifier
                ? await getCurrentUserKnowledgeSet(
                    userIdentifier,
                    customizeService,
                    userName
                )
                : [];
            const knowledgeSet = originalKnowledgeSet.map(v => ({
                id: v.uuid,
                name: v.name,
                type: v.type,
            }));
            await this.chatViewProvider.sendDataToWebview(EventMessage.UpdateKnowledgeListEvent, knowledgeSet || []);
        }
        catch (e) {
            console.error('UpdateKnowledgeListEvent Error', e);
            await this.chatViewProvider.sendDataToWebview(EventMessage.UpdateKnowledgeListEvent, []);
        }
    }

    async addCache(payload: {key: string, value: any}) {
        try {
            await this.kernelProvider.sendRequest(ACTION_COMATE_ADD_CACHE, {data: payload});
        }
        catch (e) {
            console.error('add cache Error', (e as Error).message);
        }
    }

    async getCache(key: string) {
        try {
            const res = await this.kernelProvider.sendRequest(ACTION_COMATE_GET_CACHE, {data: key});
            return res;
        }
        catch (e) {
            console.error('get cache Error', (e as Error).message);
            return undefined;
        }
    }

    async checkProgress(maxDuration = 30000): Promise<boolean> {
        const startTime = Date.now();
        const rootPath = vscode.workspace.workspaceFolders?.[0].uri.fsPath ?? '';
        while (Date.now() - startTime < maxDuration) {
            try {
                const {progress} = await this.embeddingsController.getProgress(rootPath);

                if (progress === 100) {
                    return true;
                }

                await sleep(1000);
            }
            catch (error) {
                console.error('Progress check failed', error);
                await sleep(1000);
            }
        }
        return false;
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
