import * as path from 'path';
import * as fs from 'fs';
import {ContextType, KnowledgeList} from '@shared/protocols';
import {DrawElement} from '@comate/plugin-shared-internals';
import {compact} from 'lodash';
import {getCodeChunkFromFilePath, getCurrentFileRelativePath, partitionPathsByType} from '@/utils/files';
import {Position} from '@/common/types';
export interface SelectedKnowledge {
    id: string;
    name: string;
    type: ContextType;
    content?: string;
    contentStart?: Position;
    contentEnd?: Position;
    path?: string;
}
// 获取文件和文件夹的上下文 文件获取详细内容，文件夹仅仅获取相对路径
const getFileAndFolderContext = async (paths: string[]): Promise<SelectedKnowledge[]> => {
    const [folders, files] = partitionPathsByType(paths);
    const folderContexts: SelectedKnowledge[] = folders.map(path => ({
        id: path,
        name: path,
        type: ContextType.FOLDER,
        path,
    }));
    const fileContexts: SelectedKnowledge[] = await Promise.all(files.map(async path => {
        const {content, contentStart, contentEnd} = await getCodeChunkFromFilePath(path);
        return {
            id: path,
            name: path,
            type: ContextType.FILE,
            content,
            contentStart,
            contentEnd,
            path,
        };
    }));
    if (paths.includes('currentFile')) {
        const currentFile = getCurrentFileRelativePath();
        if (currentFile) {
            const {content, contentStart, contentEnd} = await getCodeChunkFromFilePath(currentFile);
            fileContexts.push({
                id: currentFile,
                name: currentFile,
                type: ContextType.CURRENT_FILE,
                content: content,
                contentStart,
                contentEnd,
                path: currentFile,
            });
        }
    }
    if (paths.includes('repo')) {
        folderContexts.push({
            id: 'repo',
            name: '当前代码库',
            type: ContextType.REPO,
        });
    }

    return [
        ...folderContexts,
        ...fileContexts,
    ];
};

export const preProcessKnowledge = async (knowledgeList: KnowledgeList[]): Promise<SelectedKnowledge[]> => {
    const paths = knowledgeList.filter(item => item.type === 'FILE').map(item => item.id);
    const knowledges = knowledgeList.filter(item => !['FILE', 'WEB', 'CODE', 'TERMINAL'].includes(item.type));
    const contexts = compact([
        ...(await getFileAndFolderContext(paths)),
        ...knowledges,
    ]);
    return contexts;
};

const processCodeBlock = (items: DrawElement[]): any[] => {
    return items.map((item: DrawElement) => {
        if (item.type === 'code-block') {
            const newTexts: string[] = [];
            if (item.readonly) {
                return item;
            }
            if (item.insertToFileData) {
                newTexts.push(item.insertToFileData.newText);
            }
            if (item.replaceToFileData) {
                newTexts.push(item.replaceToFileData.to);
            }
            if (!item.insertToFileData && !item.replaceToFileData) {
                newTexts.push(item.children.toString());
            }
            return {...item, generatedContent: `\`\`\`${newTexts.join('\n')}\n\`\`\``};
        } else if (Array.isArray(item.children) && item.children.length > 0) {
            // 对于非code-block类型但包含子元素的元素，递归处理其子元素
            return {...item, children: processCodeBlock(item.children)};
        }
        return item;
    });
};

export const extractJsxGeneratedContent = (data: DrawElement[]): string => {
    const result = processCodeBlock(data);
    return JSON.stringify(result).replace(/\\n/g, '\n');
};

/**
 * 递归获取指定目录下所有文件的内容拼接为字符串
 * @param directoryPath 要遍历的目录路径
 * @param excludePatterns 可选的排除模式数组，用于过滤不需要的文件
 * @returns 包含所有文件内容的字符串
 */
export const getAllFileContentsInDirectory = (directoryPath: string, excludePatterns: string[] = []): string => {
    const fileContents: string[] = [];

    const traverseDirectory = (currentPath: string) => {
        const files = fs.readdirSync(currentPath);

        for (const file of files) {
            const fullPath = path.join(currentPath, file);
            const stat = fs.statSync(fullPath);

            if (excludePatterns.some(pattern => fullPath.includes(pattern))) {
                continue;
            }

            if (stat.isDirectory()) {
                traverseDirectory(fullPath);
            } else {
                try {
                    const fileContent = fs.readFileSync(fullPath, 'utf-8');
                    fileContents.push(fileContent);
                } catch (error) {
                    console.warn(`无法读取文件 ${fullPath}: ${error}`);
                }
            }
        }
    };

    traverseDirectory(directoryPath);

    return `\`\`\`${fileContents.join('\n')}\n\`\`\``;
};

interface FileContentOptions {
    maxFileSize?: number; // 可选的最大文件大小
    maxTotalSize?: number; // 可选的最大总大小
    notAllowedExtensions?: string[]; // 默认不上报的文件类型
}

export const getFileContentsFromList = (
    filePaths: string[],
    options: FileContentOptions = {}
): string => {
    const {
        maxFileSize = 1024 * 1024, // 默认最大文件大小为1MB
        maxTotalSize = 10 * 1024 * 1024, // 默认总大小限制为10MB
        notAllowedExtensions = [],
    } = options;

    const fileContents: string[] = [];

    const totalSize = 0;

    for (const filePath of filePaths) {
        try {
            if (!fs.existsSync(filePath)) {
                console.warn(`文件不存在: ${filePath}`);
                continue;
            }

            const stats = fs.statSync(filePath);

            if (stats.size > maxFileSize) {
                console.warn(`文件过大，已跳过: ${filePath} (${stats.size} 字节)`);
                continue;
            }

            const ext = path.extname(filePath).toLowerCase();
            if (notAllowedExtensions.includes(ext)) {
                console.warn(`不支持的文件类型: ${filePath}`);
                continue;
            }

            if (totalSize + stats.size > maxTotalSize) {
                console.warn('总文件大小超过限制，已停止读取更多文件');
                break;
            }

            const fileContent = fs.readFileSync(filePath, 'utf-8');
            fileContents.push(fileContent);
        }
        catch (error) {
            console.error(`读取文件出错: ${filePath}`, error);
        }
    }
    return `\`\`\`${fileContents.join('\n')}\n\`\`\``;
};
