/* eslint-disable max-len */
import path from 'node:path';
import * as vscode from 'vscode';
import axios from 'axios';
import {injectable, inject} from 'inversify';
import {
    InformationQueryType,
    getKnowledgeQueryResult,
    localPluginConfig,
    formatPrompt,
    KnowledgeChunk,
    mergePluginConfig,
} from '@comate/plugin-shared-internals';
import {Feature, KnowledgeList} from '@shared/protocols';
import {TYPES} from '@/inversify.config';
import {CMD_EXPLAIN_FUNCTION, CMD_EXPLAIN_SELECTED_CODE, SUFFIX_LANG_MAP} from '@/constants';
import {getCompleteFirstLine} from '@/utils/document';
import {stripExtraIndent} from '@/utils/indent';
import {addMarkdownCodeBlock} from '@/utils/common';
import {isPoc, isSaasOrPoc} from '@/utils/features';
import {L10n, getGenerationFailureText, getRequestFailureText} from '@/common/L10nProvider/L10n';
import {ExplainProviderText, GlobalText} from '@/common/L10nProvider/constants';
import {iocContainer} from '../../iocContainer';
import {ChatViewProvider} from '../ChatViewProvider';
import {UserService} from '../UserService';
import {GenerateCodeOptions, generateCode} from '../../api';
import {buildChatHistory, buildParams, fetchAndStreamCodelens} from '../../common/Fetcher';
import {CodelensConfig, ConfigKey, VSCodeConfigProvider} from '../ConfigProvider';
import {Conversation} from '../ChatViewProvider/Conversation';
import {ITimeTracker} from '../TimeTracker/types';
import {codeExplainDefaultConfig} from '../../utils/defaultConfig';
import {FeatureFlags} from '../FeatureFlags';
import {ICodeLensProvider} from '../BaseCodeLensProvider';
import {ChatBaseProvider, ChatResponseProxy} from '../ChatBaseProvider';
import {DiffProvider} from '../DiffProvider';
import {TextResponse} from '../ChatViewProvider/TextResponse';
import {PerformanceLogProvider} from '../PerformanceLogProvider';
import {SyntaxNode} from 'web-tree-sitter';

@injectable()
export class CodeExplainProvider extends ChatBaseProvider implements ICodeLensProvider, vscode.Disposable {
    static readonly supportedLanguages = [
        'python',
        'java',
        'go',
        'cpp',
        'c',
        'javascript',
        'typescript',
        'javascriptreact',
        'jsx',
        'typescriptreact',
        'vue',
    ];
    private disposables: vscode.Disposable[] = [];

    // 实现 ICodeLensProvider 接口要求的 configKey
    configKey: keyof CodelensConfig = 'enableInlineExplain';

    constructor(
        @inject(ChatViewProvider) private readonly chatViewProvider: ChatViewProvider,
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider,
        @inject(TYPES.ITimeTracker) private readonly timeTracker: ITimeTracker,
        @inject(FeatureFlags) private readonly featureFlags: FeatureFlags,
        @inject(DiffProvider) readonly diffProvider: DiffProvider,
        @inject(PerformanceLogProvider) private readonly performanceLog: PerformanceLogProvider
    ) {
        super(diffProvider);
        this.disposables.push(
            vscode.commands.registerCommand(
                CMD_EXPLAIN_FUNCTION,
                (document: vscode.TextDocument, range: vscode.Range, isSelection: boolean) => {
                    vscode.commands.executeCommand('baidu.comate.showChatPanel', {source: CMD_EXPLAIN_FUNCTION});
                    return this.explainCode(document, range, isSelection);
                }
            ),
            vscode.commands.registerCommand(
                CMD_EXPLAIN_SELECTED_CODE,
                () => {
                    return this.explainSelectedCode();
                }
            )
        );
    }
    generateCodeLenses(nodes: SyntaxNode[], document: vscode.TextDocument): vscode.CodeLens[] {
        const codeLenses = nodes
            .map(node => {
                const {startPosition, endPosition} = node;
                const start = new vscode.Position(startPosition.row, startPosition.column);
                const end = new vscode.Position(endPosition.row, endPosition.column);
                const range = new vscode.Range(start, end);
                const codeLens = new vscode.CodeLens(
                    new vscode.Range(
                        new vscode.Position(startPosition.row, 0),
                        new vscode.Position(endPosition.row, 0)
                    ),
                    {
                        title: L10n.t(ExplainProviderText.CODELENS_TITLE),
                        tooltip: L10n.t(ExplainProviderText.CODELENS_TOOLTIP),
                        command: CMD_EXPLAIN_FUNCTION,
                        arguments: [document, range],
                    }
                );
                return codeLens;
            });
        return codeLenses;
    }

    private async resolveParamsFallBack(document: vscode.TextDocument, range: vscode.Range, isSelection: boolean) {
        const functionContent = document.getText(range);
        const firstLine = getCompleteFirstLine(document, range);
        const code = addMarkdownCodeBlock(
            stripExtraIndent(functionContent, !isSelection, firstLine),
            document.languageId
        );
        return {
            query: L10n.t(
                ExplainProviderText.PROMPT,
                isSelection ? L10n.t(GlobalText.COMMON_CODE) : L10n.t(GlobalText.COMMON_FUNCTION)
            ),
            code,
            pluginConfig: {codeExplainPrompt: '解释下面的代码：{{selectedCode}}'},
            knowledgeList: [],
        };
    }

    private async resolveParams(document: vscode.TextDocument, range: vscode.Range, isSelection: boolean) {
        const functionContent = document.getText(range);
        let knowledgeList: KnowledgeList[] = [];
        let pluginConfig = {
            codeExplainPrompt: '',
        };

        let code = '';
        // 开关没开启或语言是英文时，走默认逻辑
        if (L10n.isEnglish || !await this.featureFlags.hasAccessTo(Feature.SaaSV0301)) {
            return this.resolveParamsFallBack(document, range, isSelection);
        }
        try {
            knowledgeList = (await this.chatViewProvider.currentQuery())?.knowledgeList ?? [];
            pluginConfig = await this.pluginConfig();
            code = this.displayPrompt(pluginConfig, functionContent, knowledgeList);
        }
        catch {
            return this.resolveParamsFallBack(document, range, isSelection);
        }
        return {
            query: ' ',
            code,
            pluginConfig,
            knowledgeList,
        };
    }

    private async explainCode(
        document: vscode.TextDocument,
        range: vscode.Range,
        isSelection = false,
        startTime: number = performance.now()
    ) {
        // TODO: 用户发送的消息内容支持以markdown渲染，适配开放平台展示更丰富的信息
        const functionContent = document.getText(range);
        if (!functionContent) {
            const createConversation = this.chatViewProvider.createConversation('', 'explain', '');
            createConversation.addResponse(
                'text',
                L10n.t(ExplainProviderText.NO_SELECTION),
                'failed'
            );
            return;
        }
        const {query, code, pluginConfig, knowledgeList} = await this.resolveParams(document, range, isSelection);
        const conversation = this.chatViewProvider.createConversation(query, 'explain', code);
        this.processExplain(
            document,
            conversation,
            range,
            isSelection,
            pluginConfig,
            functionContent,
            knowledgeList,
            startTime || performance.now()
        );
    }

    // eslint-disable-next-line complexity
    async processExplain(
        document: vscode.TextDocument,
        conversation: Conversation,
        range: vscode.Range,
        isSelection: boolean,
        pluginConfig: {codeExplainPrompt: string},
        functionContent: string,
        knowledgeList: KnowledgeList[],
        startTime: number
    ) {
        // 在添加response之前获取上次对话的id
        const {previousConversationId} = this.chatViewProvider.getPreviousConversationId();

        const streamMode = this.configProvider.getConfig<boolean>(ConfigKey.EnableStreamingSession);
        const reply = conversation.addResponse(streamMode ? 'stream' : 'text', '', 'inProgress', {
            regenerate: async () => {
                this.processExplain(
                    document,
                    conversation,
                    range,
                    isSelection,
                    pluginConfig,
                    functionContent,
                    knowledgeList,
                    performance.now()
                );
            },
            // 打开全文复制按钮
            copyAll: () => {},
        });
        let knowledgeResult: {chunks: KnowledgeChunk[]} = {chunks: []};
        const userIdentifier = isSaasOrPoc
            ? this.configProvider.getConfig<string>(ConfigKey.Key)
            : this.configProvider.getConfig<string>(ConfigKey.Username);
        // 没选择知识就跳过检索
        if (knowledgeList.length !== 0 && userIdentifier) {
            knowledgeResult = await getKnowledgeQueryResult(
                userIdentifier,
                functionContent.slice(0, 200), // TODO 接口支持的字符长度有限
                InformationQueryType.Text,
                knowledgeList.map(v => ({uuid: v.id, type: v.type}))
            );
        }
        const paramsRes = await buildParams(
            document,
            new vscode.Position(0, 0),
            iocContainer.get(UserService)
        );
        if (paramsRes.type !== 'success') {
            reply.fail(getGenerationFailureText(paramsRes.reason));
            return;
        }
        const params = {
            ...paramsRes.value,
            conversationId: previousConversationId,
            content: document.getText(range),
            userInput: document.getText(range),
            model: 'ERNIE_BOT',
            function: 'CODE_EXPLAIN',
            userInputHistory: buildChatHistory(this.chatViewProvider),
        };
        // 语言为英文时屏蔽开放平台能力
        // poc也屏蔽这个逻辑避免默认配置不同导致的参数异常
        if (!isPoc && !L10n.isEnglish && !this.isDefaultConfig(pluginConfig)) {
            params.userInput = '。';
            params.content = this.realPrompt(pluginConfig, document.getText(range), knowledgeResult.chunks);
            params.function = 'COMMENT_TO_CODE';
        }
        try {
            if (streamMode) {
                await this.fetchAndStream(reply, params, startTime);
            }
            else {
                await this.fetchWithParams(reply, params, startTime);
            }
        }
        catch (e: any) {
            reply.fail(getGenerationFailureText(e.message));
        }
    }

    async explainSelectedCode() {
        const startTime = performance.now();
        const document = vscode.window.activeTextEditor?.document;
        if (!document) {
            return;
        }
        const selection = vscode.window.activeTextEditor?.selection;
        if (!selection || !selection.start || !selection.end || !selection.start.isBefore(selection.end)) {
            return;
        }
        vscode.commands.executeCommand('baidu.comate.showChatPanel', {source: CMD_EXPLAIN_SELECTED_CODE});
        this.explainCode(document, new vscode.Range(selection.start, selection.end), true, startTime);
    }

    private async fetchWithParams(reply: TextResponse, params: GenerateCodeOptions, startTime: number) {
        const axiosTokenSource = axios.CancelToken.source();
        let canceled = false;
        reply.message.cancelTokenSource?.token.onCancellationRequested(() => {
            canceled = true;
            return axiosTokenSource.cancel();
        });
        const replyTo = reply.message.replyTo ?? -1;
        this.timeTracker.recordChatStart(replyTo);
        const result = await generateCode(params, axiosTokenSource.token);
        if (result.data.status !== 'OK') {
            reply.fail(getGenerationFailureText(result.data.message));
            return;
        }
        const generatedContent = result.data.data?.content;
        if (!generatedContent) {
            reply.fail(L10n.t(ExplainProviderText.GENERATE_ERROR));
            return;
        }
        const uuid = result.data.data?.uuid;
        this.timeTracker.bindChatId(uuid ?? '', replyTo);
        const chatResponseProxy: ChatResponseProxy = {
            getMessageId: () => String(reply.message.id),
            getChatId: () => result.data.data?.chatId,
            getMessageContent: () => reply.message.content,
            getTrackUuid: () => uuid || reply.message.extra?.uuid,
        };
        reply.success(
            generatedContent,
            {
                ...this.defaultActions(chatResponseProxy),
                ...this.extraActions(chatResponseProxy),
            },
            uuid
        );
        const endTime = performance.now();

        if (startTime && !canceled) {
            this.performanceLog.log({
                plugin: 'comate',
                skill: 'explain',
                duration: endTime - startTime,
                uuid: uuid || reply.message.extra?.uuid as any,
            });
        }
    }

    private async fetchAndStream(reply: TextResponse, params: GenerateCodeOptions, startTime: number) {
        const axiosTokenSource = axios.CancelToken.source();
        let canceled = false;
        reply.message.cancelTokenSource?.token.onCancellationRequested(() => {
            canceled = true;
            return axiosTokenSource.cancel();
        });
        const baseChatResponseProxy = {
            getMessageId: () => String(reply.message.id),
            getMessageContent: () => reply.message.content,
        };
        let firstToken = true;
        const {content, uuid, processor, chatId, conversationId} = await fetchAndStreamCodelens(
            params,
            (content: string, uuid: string, chatId: string, conversationId?: number) => {
                const chatResponseProxy: ChatResponseProxy = {
                    ...baseChatResponseProxy,
                    getChatId: () => chatId,
                    getTrackUuid: () => uuid || reply.message.extra?.uuid,
                };
                if (content && firstToken && startTime) {
                    this.performanceLog.log({
                        plugin: 'comate',
                        skill: 'explain',
                        duration: performance.now() - startTime,
                        type: 'first-token',
                        uuid: uuid || reply.message.extra?.uuid as any,
                    });

                    firstToken = false;
                }
                reply.update(
                    content,
                    {...this.defaultActions(chatResponseProxy), ...this.extraActions(chatResponseProxy)},
                    conversationId
                );
            },
            reply.message.cancelTokenSource?.token,
            axiosTokenSource.token
        );
        if (processor.error) {
            const msg = processor.errorMsg;
            reply.fail(getRequestFailureText(msg));
            return;
        }
        if (!content) {
            reply.fail(L10n.t(ExplainProviderText.GENERATE_ERROR));
            return;
        }
        const chatResponseProxy: ChatResponseProxy = {
            ...baseChatResponseProxy,
            getChatId: () => chatId,
            getTrackUuid: () => uuid || reply.message.extra?.uuid,
        };
        reply.success(
            content,
            {...this.defaultActions(chatResponseProxy), ...this.extraActions(chatResponseProxy)},
            uuid,
            undefined,
            conversationId
        );
        const endTime = performance.now();

        if (startTime && !canceled) {
            this.performanceLog.log({
                plugin: 'comate',
                skill: 'explain',
                duration: endTime - startTime,
                uuid: uuid || reply.message.extra?.uuid as any,
            });
        }
    }

    async pluginConfig() {
        const workspace = vscode.workspace.workspaceFolders;
        const pluginConfigs = this.chatViewProvider.pluginConfigs;
        // @ts-ignore
        const localConfig = await localPluginConfig(workspace);
        const {config} = await mergePluginConfig(pluginConfigs['comate'], localConfig);
        if (!config.codeExplainPrompt) {
            config.codeExplainPrompt = codeExplainDefaultConfig.codeExplainPrompt;
        }
        return config || {};
    }

    isDefaultConfig(config: Record<string, any>) {
        if (
            config.codeExplainPrompt === codeExplainDefaultConfig.codeExplainPrompt
        ) {
            return true;
        }
        return false;
    }

    realPrompt({codeExplainPrompt}: {codeExplainPrompt: string}, code: string, knowledgeReuslt: KnowledgeChunk[]) {
        if (codeExplainPrompt.trim() === '') {
            // eslint-disable-next-line no-param-reassign
            codeExplainPrompt = codeExplainDefaultConfig.codeExplainPrompt;
        }
        return formatPrompt(codeExplainPrompt, {
            knowledge: knowledgeReuslt?.[0]?.content || '',
            selectedCode: `
\`\`\`${SUFFIX_LANG_MAP[path.extname(vscode.window.activeTextEditor?.document.fileName || '').slice(1)]}
${code}
\`\`\`
`,
            activeFileName: path.extname(vscode.window.activeTextEditor?.document.fileName || ''),
        });
    }

    displayPrompt({codeExplainPrompt}: {codeExplainPrompt: string}, code: string, knowledgeList: KnowledgeList[]) {
        return formatPrompt(codeExplainPrompt, {
            knowledge: knowledgeList.map(v => `《${v.name}》`).join(' '),
            selectedCode: `
\`\`\`${SUFFIX_LANG_MAP[path.extname(vscode.window.activeTextEditor?.document.fileName || '').slice(1)]}
${code}
\`\`\`
`,
            activeFileName: path.basename(vscode.window.activeTextEditor?.document.fileName || ''),
        });
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
