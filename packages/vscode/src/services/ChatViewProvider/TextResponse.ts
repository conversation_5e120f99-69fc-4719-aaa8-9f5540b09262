import {Actions, ActionConfigs, Message, MessageExtra, MessageStatus, DynamicSection} from '@shared/protocols';
import {isNil, omitBy} from 'lodash';
import {chatActionDelayedReporter} from './chatActionDelayedReporter';
import {Conversation} from './Conversation';

export class TextResponse {
    private responseUuid: string | undefined;
    constructor(readonly message: Message, readonly conversation: Conversation) {}

    // eslint-disable-next-line complexity
    updateMessage(
        content: string,
        status?: MessageStatus,
        actions?: Actions,
        actionConfigs?: ActionConfigs,
        conversationId?: number
    ) {
        if (status === 'canceled' || status === 'success' || status === 'failed') {
            if (this.responseUuid) {
                chatActionDelayedReporter.execute(this.responseUuid);
            }
        }
        if (
            this.message.status === 'canceled'
            || this.message.cancelTokenSource?.token.isCancellationRequested
        ) {
            return;
        }
        this.message.content = content;
        if (status) {
            this.message.status = status;
        }
        if (actions) {
            this.message.actions = {
                ...this.message.actions,
                ...actions,
            };
        }
        if (actionConfigs) {
            this.message.actionConfigs = {
                ...this.message.actionConfigs,
                ...actionConfigs,
            };
        }
        if (conversationId !== undefined) {
            this.message.conversationId = conversationId;
        }
        if (this.responseUuid) {
            this.message.uuid = this.responseUuid;
        }
    }

    updateWithExtra(content: string, extra: Partial<MessageExtra>, actions?: Actions, actionConfigs?: ActionConfigs) {
        this.updateMessage(content, undefined, omitBy(actions, isNil), actionConfigs);

        if (extra) {
            // @ts-ignore
            this.message.extra = {
                ...this.message.extra,
                ...extra,
            };
            if (extra.uuid) {
                this.responseUuid = extra.uuid;
            }
        }
        this.conversation.updateView(this.responseUuid);
    }

    updateWithSections(
        content: string,
        section?: {dynamicSections?: DynamicSection[], dynamicFooterSections?: DynamicSection[]},
        status?: MessageStatus,
        actions?: Actions,
        actionConfigs?: ActionConfigs,
        conversationId?: number
    ) {
        this.updateMessage(content, status, actions, actionConfigs, conversationId);

        if (section?.dynamicSections && this.message.role === 'assistant') {
            this.message.dynamicSections = section?.dynamicSections;
        }
        if (section?.dynamicFooterSections && this.message.role === 'assistant') {
            this.message.dynamicFooterSections = section?.dynamicFooterSections;
        }
        this.conversation.updateView(this.responseUuid);
    }

    update(content: string, actions?: Actions, conversationId?: number) {
        this.updateMessage(content, 'inProgress', actions, undefined, conversationId);
        this.conversation.updateView(this.responseUuid);
    }

    fail(msg: string, conversationId?: number) {
        const helpfulMessage = $features.PLATFORM === 'internal'
            ? '（可在如流群 8323187 获得帮助）'
            : '';
        this.updateMessage(msg + helpfulMessage, 'failed', undefined, undefined, conversationId);
        this.conversation.updateView(this.responseUuid);
    }

    success(content: string, actions?: Actions, uuid?: string, actionConfigs?: ActionConfigs, conversationId?: number) {
        this.responseUuid = uuid;
        this.updateMessage(content, 'success', actions, actionConfigs, conversationId);
        this.conversation.updateView(this.responseUuid);
        return {uuid};
    }
}

export class StreamingResponse extends TextResponse {
    update(content: string, actions?: Actions) {
        if (this.message.status === 'canceled') {
            return;
        }
        this.message.content = content;
        this.message.actions = {
            ...(this.message.actions || {}),
            ...actions,
        };
        this.conversation.updateView();
    }
}
