import * as vscode from 'vscode';
import {inject, injectable} from 'inversify';
import {throttle} from 'lodash';
import {
    ACTION_COMATE_PLUS_DIAGNOSTIC_SCAN,
    DiagnosticCacheValue,
} from '@comate/plugin-shared-internals';
import {activeFileContext} from '@/utils/document';
import {KernelProvider} from '../KernelProvider';
import {ConfigKey, VSCodeConfigProvider} from '../ConfigProvider';
import {
    SHOW_CHAT_PANEL_AND_CMD_COMATE_PLUS_START,
} from '../ComatePlusChatSession/constants';
import {ChatViewProvider} from '../ChatViewProvider';

/**
 * code-style 平台规则等级与 vscode DiagnosticSeverity 映射
 */
const severityMap = {
    ERROR: vscode.DiagnosticSeverity.Error,
    WARNING: vscode.DiagnosticSeverity.Warning,
};

@injectable()
export class DiagnosticProvider implements vscode.CodeActionProvider, vscode.Disposable {
    private disposables: vscode.Disposable[] = [];
    private readonly collection = vscode.languages.createDiagnosticCollection('comate-diagnostic-collection');

    constructor(
        @inject(ChatViewProvider) private readonly chatViewProvider: ChatViewProvider,
        @inject(KernelProvider) private readonly kernelProvider: KernelProvider,
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider
    ) {
        this.start();
    }

    async refreshDiagnostics(editor?: vscode.TextEditor) {
        try {
            const document = editor?.document || vscode.window.activeTextEditor?.document;
            if (!document || document.uri.scheme !== 'file') {
                return;
            }

            const userName = this.configProvider.getConfig<string>(ConfigKey.Username);
            const context = activeFileContext(document);
            const result: DiagnosticCacheValue | undefined = await this.kernelProvider.client?.sendRequest(
                ACTION_COMATE_PLUS_DIAGNOSTIC_SCAN,
                {
                    scanId: Date.now(),
                    context: {
                        ...context,
                        // TODO 验证内容超长引起的崩溃率问题
                        activeFileContent: '',
                    },
                    systemInfo: {
                        // 这里的信息从 engine 插入
                        userDetail: {
                            name: userName,
                            displayName: userName, // 中文名从接口来
                            email: userName + '@baidu.com',
                        },
                        cwd: vscode.workspace.workspaceFolders?.[0].uri.fsPath ?? '',
                    },
                }
            );

            if (result) {
                this.collection.set(document.uri, this.createDiagnostics(document, result.diagnostics));
            }
        }
        catch (e) {
            vscode.window.showErrorMessage(e as string);
        }
    }

    async start() {
        const enableSecurityScanBox = this.configProvider.getConfig<string>(ConfigKey.EnableSecurityScanBox);

        if (!enableSecurityScanBox) {
            return;
        }

        await this.chatViewProvider.waitingEngineInit();

        setInterval(() => {
            this.refreshDiagnostics();
        }, 5000);

        const throttledRefreshDiagnostics = throttle(editor => this.refreshDiagnostics(editor), 3000, {
            leading: true,
            trailing: true,
        });

        this.disposables.push(
            vscode.languages.registerCodeActionsProvider('*', this, {
                providedCodeActionKinds: [
                    vscode.CodeActionKind.QuickFix,
                ],
            }),
            vscode.window.onDidChangeActiveTextEditor(throttledRefreshDiagnostics),
            vscode.workspace.onDidChangeTextDocument(throttledRefreshDiagnostics),
            vscode.workspace.onDidCloseTextDocument(doc => {
                this.collection.delete(doc.uri);
            })
        );
    }

    /**
     * 根据服务端扫描结果生成文件扫描结果对象
     */
    createDiagnostics(
        document: vscode.TextDocument,
        diagnostics: DiagnosticCacheValue['diagnostics']
    ): vscode.Diagnostic[] {
        return diagnostics.reduce(
            (result: vscode.Diagnostic[], {range, textMessage, severity, source, sourceCode, repairData, code}) => {
                const diagnosticRange = new vscode.Range(
                    range.startLine - 1,
                    range.startCharacter - 1,
                    range.endLine - 1,
                    range.endCharacter - 1
                );

                const rangeContent = document.getText(diagnosticRange);

                if (!rangeContent.includes(sourceCode)) {
                    return result;
                }

                const diagnostic = new vscode.Diagnostic(diagnosticRange, textMessage, severityMap[severity]);
                diagnostic.source = source;
                diagnostic.code = {
                    value: code?.value || 'comate',
                    target: vscode.Uri.parse(code?.target || 'https://comate.baidu-int.com/'),
                    // @ts-expect-error
                    fromEngine: true,
                    repairData,
                };
                result.push(diagnostic);
                return result;
            },
            []
        );
    }

    provideCodeActions(document: vscode.TextDocument, range: vscode.Range | vscode.Selection): vscode.CodeAction[] {
        const diagnostic = vscode.languages.getDiagnostics(document.uri).find(d => d.range.intersection(range));

        // @ts-expect-error
        if (diagnostic?.code?.fromEngine) {
            const fix = new vscode.CodeAction('Comate 漏洞修复', vscode.CodeActionKind.QuickFix);

            fix.command = {
                command: SHOW_CHAT_PANEL_AND_CMD_COMATE_PLUS_START,
                title: '开始对话',
                arguments: [{
                    // @ts-expect-error
                    ...diagnostic.code.repairData,
                }],
            };
            fix.diagnostics = [diagnostic];
            return [fix];
        }

        return [];
    }

    // @ts-ignore
    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
