import * as vscode from 'vscode';
import {inject, injectable} from 'inversify';
import {debounce} from 'lodash';
import {
    SAScanDiagnosticResult,
    ACTION_COMATE_PLUS_SA_SCAN_DIAGNOSTIC,
    ACTION_COMATE_PLUS_SA_SCAN_INIT,
} from '@comate/plugin-shared-internals';
import {Feature} from '@shared/protocols';
import {KernelProvider} from '../KernelProvider';
import {ConfigKey, VSCodeConfigProvider} from '../ConfigProvider';
import {ChatViewProvider} from '../ChatViewProvider';
import {apis} from '../AutoComateChatSession/api';
import {SHOW_CHAT_PANEL_AND_CMD_COMATE_PLUS_START} from '../ComatePlusChatSession/constants';
/**
 * code-style 平台规则等级与 vscode DiagnosticSeverity 映射
 */
const severityMap = {
    ERROR: vscode.DiagnosticSeverity.Error,
    WARNING: vscode.DiagnosticSeverity.Warning,
};

@injectable()
export class SADiagnosticProvider implements vscode.CodeActionProvider, vscode.Disposable {
    private disposables: vscode.Disposable[] = [];
    private readonly collection = vscode.languages.createDiagnosticCollection('comate-SA-diagnostic-collection');

    constructor(
        @inject(ChatViewProvider) private readonly chatViewProvider: ChatViewProvider,
        @inject(KernelProvider) private readonly kernelProvider: KernelProvider,
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider
    ) {
        this.start();
        vscode.commands.registerCommand(
            'comate.ignoreDiagnostic',
            (diagnostics: SAScanDiagnosticResult['diagnostics']) => {
                const editor = vscode.window.activeTextEditor;
                const document = editor?.document;
                if (!document || document.uri.scheme !== 'file') {
                    return;
                }
                this.collection.set(document.uri, this.createDiagnostics(document, diagnostics));

            });
        this.disposables.push(
            vscode.languages.registerCodeActionsProvider('*', this, {
                providedCodeActionKinds: [
                    vscode.CodeActionKind.QuickFix,
                ],
            })
        );
    }

    async onFileSaved(editor: vscode.TextEditor) {
        try {
            const document = editor?.document;
            if (!document || document.uri.scheme !== 'file') {
                return;
            }

            const userName = this.configProvider.getConfig<string>(ConfigKey.Username);
            const result: SAScanDiagnosticResult | undefined = await this.kernelProvider.client?.sendRequest(
                ACTION_COMATE_PLUS_SA_SCAN_DIAGNOSTIC,
                {
                    scanId: Date.now(),
                    data: {
                        absolutePath: document.uri.fsPath,
                    },
                    systemInfo: {
                        userDetail: {
                            name: userName,
                            displayName: userName,
                            email: userName + '@baidu.com',
                        },
                        cwd: vscode.workspace.workspaceFolders?.[0].uri.fsPath ?? '',
                    },
                }
            );

            if (result && result.absolutePath === document.uri.fsPath) {
                this.collection.set(document.uri, this.createDiagnostics(document, result.diagnostics));
            }
        }
        catch (e) {
            console.error(`SA诊断失败${(e as Error).message}`);
        }
    }

    createDiagnostics(document: vscode.TextDocument, diagnostics: SAScanDiagnosticResult['diagnostics']) {
        const rawDiagnostics = diagnostics.map(({
            range,
            textMessage,
            severity,
            sourceCode,
            source,
            code,
            repairData,
        }) => {
            const diagnosticRange = new vscode.Range(
                range.startLine - 1,
                range.startCharacter - 1,
                range.endLine - 1,
                range.endCharacter - 1
            );
            const rangeContent = document.getText(diagnosticRange);

            if (!rangeContent.includes(sourceCode)) {
                return null;
            }

            const diagnostic = new vscode.Diagnostic(diagnosticRange, textMessage, severityMap[severity]);
            diagnostic.source = source;
            diagnostic.code = {
                value: code?.value || 'comate',
                target: vscode.Uri.parse(code?.target || 'https://comate.baidu-int.com/'),
                // @ts-expect-error
                fromSA: true,
                repairData,
                ignoreData: diagnostics.filter(item => item.sourceCode !== sourceCode),
            };
            return diagnostic;
        });
        return rawDiagnostics.filter(Boolean) as vscode.Diagnostic[];
    }

    provideCodeActions(document: vscode.TextDocument, range: vscode.Range | vscode.Selection): vscode.CodeAction[] {
        const diagnostic = vscode.languages.getDiagnostics(document.uri).find(d => d.range.intersection(range));

        // @ts-expect-error
        if (diagnostic?.code?.fromSA) {
            const fix = new vscode.CodeAction('Comate WBOX代码缺陷修复', vscode.CodeActionKind.QuickFix);
            const ignore = new vscode.CodeAction('Comate WBOX代码缺陷忽略', vscode.CodeActionKind.QuickFix);
            fix.command = {
                command: SHOW_CHAT_PANEL_AND_CMD_COMATE_PLUS_START,
                title: '开始对话',
                arguments: [{
                    // @ts-expect-error
                    ...diagnostic.code.repairData,
                }],
            };
            ignore.command = {
                command: 'comate.ignoreDiagnostic',
                title: '忽略代码缺陷',
                // @ts-expect-error
                arguments: [diagnostic.code.ignoreData],
            };
            fix.diagnostics = [diagnostic];
            ignore.diagnostics = [diagnostic];
            return [fix, ignore];
        }

        return [];
    }


    async start() {
        const userName = this.configProvider.getConfig<string>(ConfigKey.Username) || '';
        const res = await apis.getAccessTo({featureName: Feature.SA, userName});
        if (!res.status) {
            return;
        }

        await this.chatViewProvider.waitingEngineInit();

        const serverStatus = await this.initScanService();
        if (!serverStatus) {
            return;
        }

        const debouncedSaveHandler = debounce(
            () => {
                const editor = vscode.window.activeTextEditor;
                if (editor) {
                    this.onFileSaved(editor);
                }
            },
            60000, // 1 minute debounce
            {leading: true, trailing: false}
        );

        this.disposables.push(
            vscode.workspace.onWillSaveTextDocument(debouncedSaveHandler)
        );
    }

    async initScanService() {
        const userName = this.configProvider.getConfig<string>(ConfigKey.Username) || '';
        const status: boolean | undefined = await this.kernelProvider.client?.sendRequest(
            ACTION_COMATE_PLUS_SA_SCAN_INIT,
            {
                scanId: Date.now(),
                systemInfo: {
                    userDetail: {
                        name: userName,
                        displayName: userName,
                        email: userName + '@baidu.com',
                    },
                    cwd: vscode.workspace.workspaceFolders?.[0].uri.fsPath ?? '',
                },
            }
        );
        return status;
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
        this.collection.dispose();
    }
}
