import * as vscode from 'vscode';
import {CodeChunk} from '@/common/types';
import {addMarkdownCodeBlock} from '@/utils/common';
import {error} from '@/common/outputChannel';
import {autoDebugSearchTask} from '../AutoComateChatSession/autoDebugSearchTask';
import {computeRepoId} from '../EmbeddingsService/embeddingUtils';
import {TreeSitterProvider} from '../TreeSitterProvider';
import {AutoDebugContext} from '../AutoComateChatSession/api';
import {TerminalManager} from '../TerminalLink/TerminalManager';
import {TerminalLinkProvider} from '../TerminalLink/TerminalLinkProvider';

export type DebugEventAction = 'run' | 'search' | 'commandName' | 'restart';

export interface DebugEventData {
    action: DebugEventAction;
    data?: any;
}

type AnalyzeContext = Record<string, any[]>;

interface ExecutableCommand {
    pwd: string;
    commandLine: string;
}

function formatContextKey(key: string) {
    switch (key) {
        case 'closestFunctions':
            return 'CLOSEST_FUNCTION';
        case 'definitions':
            return 'DEFINITION';
        case 'files':
            return 'FILE';
        case 'directoryTrees':
            return 'DIRECTORY_TREE';
        case 'symbolsInStructOrPackage':
            return 'SYMBOLS_IN_STRUCT_OR_PACKAGE';
        case 'metadata':
            return 'METADATA';
        case 'symbolTypeDefinition':
            return 'SYMBOL_TYPE_DEFINITION';
        default:
            return key;
    }
}

function analyzeContextToStrategy(analyzeContext: AnalyzeContext) {
    const searchStrategies = [];
    for (const [key, list] of Object.entries(analyzeContext)) {
        for (const value of list) {
            const context = {
                cursorLine: 0,
                id: value.filePath,
                contents: [],
                path: value.filePath,
                name: value.filePath,
                type: 'FILE',
                key,
                params: {...value, customRule: formatContextKey(key)},
            };
            searchStrategies.push({key, context});
        }
    }
    return searchStrategies;
}

function searchResultToContext(searchResult: Array<readonly [string, CodeChunk | null]>) {
    const codeContexts: Record<string, any> = {};
    for (const [key, value] of searchResult) {
        if (!value) {
            continue;
        }
        const {contentStart, contentEnd, path, ...rest} = value;
        const list = codeContexts[key] ?? [];
        list.push({
            ...rest,
            type: key === 'closestFunctions' && list.length === 0 ? 'errorCode' : 'relatedCode',
            filePath: path,
            startLineNum: contentStart?.line,
            endLineNum: contentEnd?.line,
        });
        codeContexts[key] = list;
    }
    return codeContexts;
}

async function searchCode(
    analyzeContext: AnalyzeContext,
    treeSitterProvider: TreeSitterProvider
) {
    try {
        const repoId = await computeRepoId(vscode.workspace.workspaceFolders?.[0]?.uri.fsPath) ?? '';
        const strategies = analyzeContextToStrategy(analyzeContext);
        const searchResult = await Promise.all(strategies.map(async ({key, context}) => {
            const res = await autoDebugSearchTask(
                repoId,
                treeSitterProvider,
                context as AutoDebugContext
            );
            return [key, res] as const;
        }));
        return searchResultToContext(searchResult);
    }
    catch (e) {
        error('AutoDebug Agent search code error:', (e as Error).message);
        return {};
    }
}

function buildCommandResult(
    result: 'noCommand' | 'error' | 'failed' | 'timeout' | 'unknown' | 'success' | 'killed',
    extraData?: Record<string, any>
) {
    switch (result) {
        case 'noCommand':
            return {status: 'error', description: '❌ 无法获取运行命令，验证失败 (╥﹏╥)', runResult: 'ERROR'};
        case 'killed':
            return {status: 'error', description: '🚫 任务已终止，验证失败 (；一_一)', runResult: 'ABORT'};
        case 'timeout':
            return {status: 'error', description: '⏰ 运行超时，验证失败 (｡•́︿•̀｡)', runResult: 'TIMEOUT'};
        case 'unknown':
            return {status: 'error', description: '❓ 运行结束，无法获取运行结果，验证失败 (｡•́︿•̀｡)', runResult: 'ERROR'};
        case 'success':
            return {status: 'success', description: '✨ 运行成功，修复完成 (ﾉ*>∀<)ﾉ♡', runResult: 'SUCCESS'};
        case 'error':
            return {status: 'error', description: '🚨 运行结束，但未监测到错误，验证失败 T_T', runResult: 'ERROR'};
        case 'failed':
            return {status: 'failed', description: '🛠️ 运行失败，发现新错误，是否继续修复？', runResult: 'FAILED', ...extraData};
    }
}

async function executeCommand(
    terminalManager: TerminalManager,
    terminalLinkProvider: TerminalLinkProvider,
    command?: ExecutableCommand
) {
    if (!command) {
        return buildCommandResult('noCommand');
    }
    const {output, exitCode, completed, killed} = await terminalManager.runAutoDebugCommand(
        command.commandLine,
        command.pwd
    );

    if (killed) {
        return buildCommandResult('killed');
    }
    else if (!completed) {
        return buildCommandResult('timeout');
    }
    else if (typeof exitCode !== 'number') {
        return buildCommandResult('unknown');
    }
    else if (exitCode === 0) {
        return buildCommandResult('success');
    }
    else {
        const matchError = output && terminalLinkProvider.matchRule(output);
        if (matchError) {
            const restartPayload = {
                code: addMarkdownCodeBlock(matchError.chunk.join('\n'), 'text'),
            };
            return buildCommandResult('failed', {restartPayload});
        }
        return buildCommandResult('error');
    }
}

export async function handleDebugAgentAction(
    {action, data}: DebugEventData,
    treeSitterProvider: TreeSitterProvider,
    terminalManager: TerminalManager,
    terminalLinkProvider: TerminalLinkProvider
) {
    if (action === 'search') {
        // 智能体请求 IDE 检索任务
        const analyzeContext = data as AnalyzeContext;
        return searchCode(analyzeContext, treeSitterProvider);
    }
    else if (action === 'run') {
        // 智能体修复后运行命令检查
        return executeCommand(terminalManager, terminalLinkProvider, data);
    }
    else if (action === 'commandName') {
        // vscode 里边不应该调这个命令，返回空字符串适配
        return '';
    }
    return null;
}
