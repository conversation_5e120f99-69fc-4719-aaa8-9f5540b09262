import * as vscode from 'vscode';
import {LazyServiceIdentifer, inject, injectable} from 'inversify';
import {
    AGENT_DEBUG_CUSTOM_ACTION,
    // ACTION_COMATE_GET_AGENT_TASKS,
    // ACTION_COMATE_ADD_AGENT_TASK,
    // ACTION_COMATE_SET_FOREGROUND_TASK,
    // ACTION_COMATE_UPDATE_AGENT_TASK_MESSAGES,
    AgentPayload,
    AgentConversationType,
    UPDATE_RULE_PANEL_EVENT,
    // AgentMessage,
    // Task,
} from '@comate/plugin-shared-internals';
import {EventMessage, AgentConfig} from '@shared/protocols';
import {iocContainer} from '@/iocContainer';
import {error} from '@/common/outputChannel';
import {isInternal} from '@/utils/features';
import {getExtensionContextAsync} from '@/utils/extensionContext';
import {login} from '@/utils/internalLogin';
import {KernelProvider} from '../KernelProvider';
import {ChatViewProvider} from '../ChatViewProvider';
import {VSCodeConfigProvider, ConfigKey} from '../ConfigProvider';
import {TreeSitterProvider} from '../TreeSitterProvider';
import {TerminalManager} from '../TerminalLink/TerminalManager';
import {TerminalLinkProvider} from '../TerminalLink/TerminalLinkProvider';
import {TRUSTED_USER_KEY} from '../UserService';
import {
    CMD_GET_AGENT_CONVERSATIONS,
    CMD_ADD_AGENT_TASK_CONVERSATION,
    CMD_ADD_SET_FOREGROUND,
    CMD_ADD_NEW_MESSAGE,
    CMD_ADD_GET_AGENT_STATUS,
} from './constants';
import {handleDebugAgentAction} from './debugAgent';

export const CMD_MESSAGE_GENERATE = 'baidu.comate.sourceControlGenerateMessage';

@injectable()
export class AgentProvider implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];

    constructor(
        @inject(new LazyServiceIdentifer(() => ChatViewProvider)) private readonly chatViewProvider: ChatViewProvider,
        @inject(KernelProvider) private readonly kernelProvider: KernelProvider,
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider,
        @inject(TreeSitterProvider) private readonly treeSitterProvider: TreeSitterProvider,
        @inject(TerminalManager) private readonly terminalManager: TerminalManager,
        @inject(TerminalLinkProvider) private readonly terminalLinkProvider: TerminalLinkProvider
    ) {
        this.disposables.push(
            vscode.commands.registerCommand(CMD_GET_AGENT_CONVERSATIONS, this.getAgentConversations.bind(this)),
            vscode.commands.registerCommand(CMD_ADD_AGENT_TASK_CONVERSATION, this.addAgentConversation.bind(this)),
            vscode.commands.registerCommand(CMD_ADD_SET_FOREGROUND, this.setForeground.bind(this)),
            vscode.commands.registerCommand(CMD_ADD_NEW_MESSAGE, this.newMessage.bind(this)),
            vscode.commands.registerCommand(CMD_ADD_GET_AGENT_STATUS, this.getAgentConfig.bind(this)),
            vscode.workspace.onDidChangeConfiguration(async e => {
                if (e.affectsConfiguration('baidu.comate.license')) {
                    this.updateAgentApplyStatusAndConfig();
                }
                if (
                    e.affectsConfiguration('baidu.comate.enableSecurityIntelligence')
                    || e.affectsConfiguration('baidu.comate.enableDebugIntelligence')
                ) {
                    this.updateAgentConfigByIDE();
                }
            })
        );
        this.listenConversationMessagesUpdate();
        this.listenToastMessageChange();
        this.listenDebugAgentActions();
        this.listenUpdateRulePanelEvent();
    }

    private async updateAgentApplyStatusAndConfig() {
        const agentConfig = this.getAgentConfig();
        this.chatViewProvider.sendDataToWebview(EventMessage.AgentSwitchChangeFromIdeEvent, agentConfig);
    }

    private async updateAgentConfigByIDE() {
        const agentConfig = this.configProvider.getAgentConfig();
        this.chatViewProvider.sendDataToWebview(EventMessage.AgentSwitchChangeFromIdeEvent, agentConfig);
    }

    private async getAgentConfig() {
        const agentConfig = {
            enableIntelligenceAgent: {
                enableSecurityIntelligence: isInternal || !!this.configProvider.getConfig<AgentConfig>(
                    ConfigKey.EnableSecurityIntelligence
                ),
                enableDebugIntelligence: !!this.configProvider.getConfig<AgentConfig>(
                    ConfigKey.EnableDebugIntelligence
                ),
            },
        };
        return agentConfig;
    }

    async getAgentConversations() {
        try {
            const agentConversations = await this.kernelProvider.client?.sendRequest(
                'COMATE_AGENT_GET_CONVERSATIONS',
                {}
            );

            return agentConversations;
        }
        catch (ex) {
            error('getAgentConversations error:', (ex as Error).message);
            return [];
        }
    }

    async addAgentConversation(agentPayload: {agentPayload: AgentPayload}) {
        try {
            const extensionContext = await getExtensionContextAsync();
            if (agentPayload?.agentPayload.conversationType === AgentConversationType.SecuBotConversation
                && !extensionContext.globalState.get<string>(TRUSTED_USER_KEY)) {
                const loginSuccess = await login(iocContainer);
                if (!loginSuccess) {
                    return null;
                }
            }
            const res = await this.kernelProvider.client?.sendRequest('COMATE_AGENT_START_NEW_CHAT', agentPayload);
            return res;
        }
        catch (ex) {
            error('addAgentConversation error:', (ex as Error).message);
            return null;
        }
    }

    setForeground(agentPayload: {agentPayload: AgentPayload}) {
        try {
            const messages = this.kernelProvider.client?.sendRequest(
                'COMATE_AGENT_SET_FOREGROUND_CONVERSATION',
                agentPayload
            );
            return messages;
        }
        catch (ex) {
            error('setForeground error:', (ex as Error).message);
            return null;
        }
    }

    async newMessage(agentPayload: {agentPayload: AgentPayload}) {
        try {
            const res = await this.kernelProvider.client?.sendRequest('COMATE_AGENT_NEW_MESSAGE', agentPayload);
            return res;
        }
        catch (ex) {
            error('stopGeneration error:', (ex as Error).message);
            throw ex;
        }
    }

    listenConversationMessagesUpdate() {
        this.kernelProvider.client?.onRequest(
            'COMATE_AGENT_UPDATE_MESSAGE',
            (res: any) => {
                this.chatViewProvider.sendDataToWebview(
                    EventMessage.AgentConversationMessageUpdateEvent,
                    res.data
                );
            }
        );
    }
    listenToastMessageChange() {
        this.kernelProvider.client?.onRequest(
            'TOAST_MESSAGE_CHANGE_MESSAGE',
            (res: any) => {
                this.chatViewProvider.sendDataToWebview(
                    EventMessage.ToastMessageChangeEvent,
                    res.data
                );
            }
        );
    }

    /**
     * Debug智能体自定义IDE事件
     */
    listenDebugAgentActions() {
        this.kernelProvider.client?.onRequest(
            AGENT_DEBUG_CUSTOM_ACTION,
            (payload: any) => {
                return handleDebugAgentAction(
                    payload,
                    this.treeSitterProvider,
                    this.terminalManager,
                    this.terminalLinkProvider
                );
            }
        );
    }

    listenUpdateRulePanelEvent() {
        this.kernelProvider.client?.onRequest(
            UPDATE_RULE_PANEL_EVENT,
            (payload: any) => {
                this.chatViewProvider.sendDataToWebview(
                    EventMessage.UpdateRulePanelEvent,
                    payload
                );
            }
        );
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
