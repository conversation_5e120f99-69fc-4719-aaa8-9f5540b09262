/* eslint-disable max-len */
import * as vscode from 'vscode';
import {injectable, inject} from 'inversify';
import DiffMatchPatch, {DIFF_DELETE, DIFF_INSERT, DIFF_EQUAL} from 'diff-match-patch';
import {L10n} from '@/common/L10nProvider/L10n';
import {DiffProviderText} from '@/common/L10nProvider/constants';
import {acceptCode} from '@/api';
import {RegisteredCommand} from '@/constants';
import {splitEol} from '@/utils/common';
import {VisualStateManagerProvider} from '../VisualStateManagerProvider';
import {
    COMATE_DIFF_ALL_ACCEPT,
    COMATE_DIFF_ALL_REJECT,
    COMATE_DIFF_DUAL_SCREEN_OPEN,
} from './constant';

type DiffType = 'block' | 'line';

export interface DiffRange {
    range: vscode.Range;
    originalContent: string;
    modifiedContent: string;
    type: DiffType;
    diffs: DiffMatchPatch.Diff[];
    uuid: string;
    isFullLineReplace?: boolean;
    onCancel?: () => void;
}

export const isWin = process.platform === 'win32';

@injectable()
export class DiffHighlightProvider implements vscode.Disposable {
    private readonly insertedLineDecorationType: vscode.TextEditorDecorationType;
    private readonly modifiedLineDecorationType: vscode.TextEditorDecorationType;
    private readonly removedLineDecorationType: vscode.TextEditorDecorationType;
    private readonly diffRanges: Map<string, DiffRange> = new Map();
    private readonly customHandlers: Map<string, {accept: () => void, reject: () => void}> = new Map();
    private currentDualScreenDiff: ((text: string) => Promise<void>) | undefined = undefined;
    private currentDiffInfoForDualScreen: DiffRange | undefined;

    private readonly disposables: vscode.Disposable[] = [];
    private readonly _onDidChangeCodeLenses = new vscode.EventEmitter<void>();
    private readonly _onDidDiffAcceptedChanged = new vscode.EventEmitter<{accepted: boolean}>();
    onDidChangeCodeLenses = this._onDidChangeCodeLenses.event;
    onDidDiffAcceptedChanged = this._onDidDiffAcceptedChanged.event;

    constructor(@inject(VisualStateManagerProvider) readonly visualStateManagerProvider: VisualStateManagerProvider) {
        // 定义装饰类型
        this.insertedLineDecorationType = vscode.window.createTextEditorDecorationType({
            backgroundColor: 'var(--vscode-diffEditor-insertedLineBackground)',
            isWholeLine: true,
        });
        this.modifiedLineDecorationType = vscode.window.createTextEditorDecorationType({
            backgroundColor: 'var(--vscode-merge-incomingContentBackground)',
            isWholeLine: true,
        });
        this.removedLineDecorationType = vscode.window.createTextEditorDecorationType({
            backgroundColor: 'var(--vscode-diffEditor-removedLineBackground)',
            isWholeLine: true,
        });

        this.disposables.push(
            vscode.languages.registerCodeLensProvider({scheme: 'file'}, this)
        );

        this.disposables.push(
            vscode.commands.registerCommand(COMATE_DIFF_ALL_ACCEPT, this.acceptAllChanges, this),
            vscode.commands.registerCommand(COMATE_DIFF_ALL_REJECT, this.rejectAllChanges, this),
            vscode.commands.registerCommand(RegisteredCommand.cancelInlineChat, this.clearLoadingRange, this),
            vscode.workspace.onDidChangeTextDocument(
                event => {
                    const filepath = event.document.uri.fsPath;
                    const loadingRange = this.visualStateManagerProvider.getLoadingRange(filepath);
                    const diffRange = this.diffRanges.get(filepath);
                    if (!loadingRange && diffRange) {
                        event.contentChanges.forEach(change => {
                            const linesAdded = change.text.split('\n').length - 1;
                            const linesDeleted = change.range.end.line - change.range.start.line;
                            const lineDelta = linesAdded - linesDeleted;

                            if (diffRange?.range.start.line >= change.range.start.line) {
                                diffRange.range = new vscode.Range(
                                    diffRange.range.start.line + lineDelta,
                                    diffRange.range.start.character,
                                    diffRange.range.end.line + lineDelta,
                                    diffRange.range.end.character
                                );
                            }
                        });
                    }
                }
            )
        );
    }

    async applyBlockDiffHighlight(filePath: string, range: vscode.Range, modifiedContent: string, uuid: string) {
        try {
            const uri = vscode.Uri.file(filePath);
            const document = await vscode.workspace.openTextDocument(uri);
            const editor = await vscode.window.showTextDocument(document);
            const originalContent = document.getText(range).split(/\r?\n/).join('\n');
            const modifiedLines = modifiedContent.split('\n');
            const isInsert = range.start.isEqual(range.end);
            const insertedRanges: vscode.DecorationOptions[] = [];
            const removedRanges: vscode.DecorationOptions[] = [];

            if (!isInsert) {
                removedRanges.push({range});
            }

            const insertPosition = new vscode.Position(isInsert ? range.end.line : range.end.line + 1, 0);
            await editor.edit(
                editBuilder => {
                    editBuilder.insert(insertPosition, modifiedContent);
                },
                {
                    undoStopAfter: false,
                    undoStopBefore: false,
                }
            );

            const lineRange = new vscode.Range(
                new vscode.Position(insertPosition.line, 0),
                new vscode.Position(insertPosition.line + modifiedLines.length - 2, 0)
            );
            insertedRanges.push({range: lineRange});
            editor.setDecorations(this.removedLineDecorationType, removedRanges);
            editor.setDecorations(this.insertedLineDecorationType, insertedRanges);
            this.addDiffRange(filePath, {range, originalContent, modifiedContent, type: 'block', diffs: [], uuid});
        }
        catch (ex) {
            console.error(ex);
        }
    }

    // eslint-disable-next-line max-statements, complexity
    async applyLineDiffHighlight(
        filePath: string,
        range: vscode.Range,
        modifiedContent: string,
        uuid: string,
        isFullLineReplace?: boolean
    ) {
        try {
            const uri = vscode.Uri.file(filePath);
            const document = await vscode.workspace.openTextDocument(uri);
            const editor = await vscode.window.showTextDocument(document);
            const originalContent = splitEol(document.getText(range)).join('\n');
            const originalNewlines = ((/\r?\n*$/.exec(originalContent)) || [''])[0].length;
            const modifiedNewlines = ((/\n*$/.exec(modifiedContent)) || [''])[0].length;
            const originalContentWithoutNewlines = originalNewlines === 0
                ? originalContent
                : originalContent.slice(0, -originalNewlines);
            const modifiedContentWithoutNewlines = modifiedNewlines === 0
                ? modifiedContent
                : modifiedContent.slice(0, -modifiedNewlines);
            const dmp = new DiffMatchPatch();
            const {chars1, chars2, lineArray} = dmp.diff_linesToChars_(
                originalContentWithoutNewlines,
                modifiedContentWithoutNewlines
            );
            const diffs = dmp.diff_main(chars1, chars2, true);
            dmp.diff_charsToLines_(diffs, lineArray);
            if (modifiedNewlines !== 0 || originalNewlines !== 0) {
                diffs.push([DIFF_EQUAL, '\n']);
            }
            if (modifiedNewlines !== 0 && originalNewlines !== modifiedNewlines) {
                diffs.push([DIFF_INSERT, '\n'.repeat(modifiedNewlines)]);
            }
            if (originalNewlines !== 0 && originalNewlines !== modifiedNewlines) {
                diffs.push([DIFF_DELETE, '\n'.repeat(originalNewlines)]);
            }

            const insertDecorations: vscode.DecorationOptions[] = [];
            const removedDecorations: vscode.DecorationOptions[] = [];
            await editor.edit(
                editBuilder => {
                    const mappedDiffs = diffs.map((diff, index, array) => {
                        let lineToAdd = diff[1];
                        const nextDiff = array[index + 1];
                        // 检查当前行是否不以\n结尾，且下一行存在且不是以\n开头时，在当前行末尾添加一个换行符
                        if (!lineToAdd.endsWith('\n') && nextDiff && !nextDiff[1].startsWith('\n')) {
                            lineToAdd += '\n';
                        }
                        return lineToAdd;
                    });
                    editBuilder.replace(range, mappedDiffs.join(''));
                },
                {
                    undoStopAfter: false,
                    undoStopBefore: false,
                }
            );

            let pos = range.start.line; // 起始偏移量
            diffs.forEach(diff => {
                const [type, text] = diff;
                switch (type) {
                    case DIFF_EQUAL:
                        pos += splitEol(text).length - 1;
                        break;
                    case DIFF_DELETE:
                        text.split('\n').forEach((splitText, idx) => {
                            const isLastLineEmpty = idx === splitEol(text).length - 1 && splitText === '';
                            if (!isLastLineEmpty) {
                                const start = {
                                    line: editor.document.lineAt(pos).range.start.line,
                                    character: 0,
                                } as vscode.Position;
                                const end = {
                                    line: editor.document.lineAt(pos).range.end.line,
                                    character: 0,
                                } as vscode.Position;
                                removedDecorations.push({range: new vscode.Range(start, end)});
                                pos++;
                            }
                        });
                        break;
                    case DIFF_INSERT:
                        text.split('\n').forEach((splitText, idx) => {
                            const isLastLineEmpty = idx === splitEol(text).length - 1 && splitText === '';
                            if (!isLastLineEmpty) {
                                if (pos < editor.document.lineCount) {
                                    const start = {
                                        line: editor.document.lineAt(pos).range.start.line,
                                        character: 0,
                                    } as vscode.Position;
                                    const end = {
                                        line: editor.document.lineAt(pos).range.end.line,
                                        character: 0,
                                    } as vscode.Position;
                                    insertDecorations.push({range: new vscode.Range(start, end)});
                                    pos++;
                                }
                            }
                        });
                        break;
                }
            });

            editor.setDecorations(this.insertedLineDecorationType, insertDecorations);
            editor.setDecorations(this.removedLineDecorationType, removedDecorations);
            this.addDiffRange(filePath, {
                range,
                originalContent,
                modifiedContent,
                type: 'line',
                diffs,
                uuid,
                isFullLineReplace,
            });
            await editor.edit(editBuilder => {
                editBuilder.insert(new vscode.Position(editor.document.lineCount, 0), '');
            });
        }
        catch (ex) {
            console.error(ex);
        }
    }

    addLoadingRange(filePath: string, range: vscode.Range) {
        this.visualStateManagerProvider.addLoadingRange(filePath, range);
    }

    addOnCancelCallback(filePath: string, callback: (source?: any) => void) {
        this.visualStateManagerProvider.addOnCancelCallback(filePath, callback);
    }

    getLoadingRange(path: string) {
        return this.visualStateManagerProvider.getLoadingRange(path);
    }

    updateIndexLineDecorations(filePath: string, range: vscode.Range) {
        this.visualStateManagerProvider.updateIndexLineDecorations(filePath, range);
    }

    async clearLoadingRange(path?: string, source?: any) {
        const filePath = path || vscode.window.activeTextEditor?.document.uri.fsPath || '';
        const isLoading = Boolean(this.visualStateManagerProvider.getLoadingRange(filePath));
        if (!isLoading) {
            return;
        }
        this.visualStateManagerProvider.clearLoadingRange(filePath, source);
        await this.clearDiffRange(filePath);
    }

    addDiffRange(filePath: string, data: DiffRange) {
        this.diffRanges.set(filePath, data);
    }

    getDiffRange(path: string) {
        return this.diffRanges.get(path);
    }

    async clearDiffRange(path: string) {
        this.diffRanges.delete(path);
        const uri = vscode.Uri.file(path);
        const document = await vscode.workspace.openTextDocument(uri);
        const editor = await vscode.window.showTextDocument(document);
        editor.setDecorations(this.removedLineDecorationType, []);
        editor.setDecorations(this.insertedLineDecorationType, []);
        this._onDidChangeCodeLenses.fire();
    }

    isInDiff(path: string): boolean {
        return this.diffRanges.has(path);
    }

    setDualScreenDiff(diff: (text: string) => Promise<void>) {
        this.currentDualScreenDiff = diff;
    }

    getCurrentDualScreenDiff() {
        return this.currentDualScreenDiff;
    }

    clearCurrentDualScreenDiff() {
        this.currentDualScreenDiff = undefined;
    }

    setCurrentDiffInfoForDualScreen(diffInfo: DiffRange) {
        this.currentDiffInfoForDualScreen = diffInfo;
    }

    getCurrentDiffInfoForDualScreen() {
        return this.currentDiffInfoForDualScreen;
    }

    clearCurrentDiffInfoForDualScreen() {
        this.currentDiffInfoForDualScreen = undefined;
    }

    provideCodeLenses(document: vscode.TextDocument): vscode.ProviderResult<vscode.CodeLens[]> {
        const codeLenses: vscode.CodeLens[] = [];
        const diffInfo = this.getDiffRange(document.uri.fsPath);
        const loadingRange = this.visualStateManagerProvider.getLoadingRange(document.uri.fsPath);
        const dualScreenDiff = this.getCurrentDualScreenDiff();
        vscode.commands.executeCommand('setContext', 'baidu.comate.inlinechat.codelensVisible', false);

        if (loadingRange) {
            return [];
        }
        else if (diffInfo) {
            const {range} = diffInfo;
            vscode.commands.executeCommand('setContext', 'baidu.comate.inlinechat.codelensVisible', true);
            codeLenses.push(
                new vscode.CodeLens(range, {
                    title: '$(comate-logo-mini)' + L10n.t(DiffProviderText.ACCEPT, (isWin ? '(Alt+' : '(⌥') + 'A)'),
                    command: COMATE_DIFF_ALL_ACCEPT,
                    arguments: [document.uri, range, diffInfo.modifiedContent, diffInfo.uuid],
                })
            );
            codeLenses.push(
                new vscode.CodeLens(range, {
                    title: L10n.t(DiffProviderText.REJECT, (isWin ? '(Alt+' : '(⌥') + 'X)'),
                    command: COMATE_DIFF_ALL_REJECT,
                    arguments: [document.uri, range, diffInfo.modifiedContent],
                })
            );
            codeLenses.push(
                new vscode.CodeLens(diffInfo.range, {
                    title: L10n.t(DiffProviderText.OPEN_DUAL_SCREEN_DIFF, (isWin ? '(Alt+' : '(⌥') + 'D)'),
                    command: COMATE_DIFF_DUAL_SCREEN_OPEN,
                    arguments: [document, diffInfo],
                })
            );
        }
        else if (dualScreenDiff) {
            const currentDiffInfoForDualScreen = this.getCurrentDiffInfoForDualScreen();
            currentDiffInfoForDualScreen && dualScreenDiff(currentDiffInfoForDualScreen.modifiedContent);
            this.clearCurrentDualScreenDiff();
            this.clearCurrentDiffInfoForDualScreen();
        }
        return codeLenses;
    }

    async acceptBlockChanges(uri: vscode.Uri, range: vscode.Range, uuid: string) {
        const document = await vscode.workspace.openTextDocument(uri);
        const editor = await vscode.window.showTextDocument(document);

        acceptCode({uuid, accepted: true, content: ''});

        // 删除原来range内的内容
        await editor.edit(
            editBuilder => {
                editBuilder.delete(range);
            },
            {
                undoStopAfter: false,
                undoStopBefore: false,
            }
        );
        this.clearDiffRange(uri.fsPath);
    }

    async acceptLinesChanges(uri: vscode.Uri, range: vscode.Range, uuid: string) {
        const document = await vscode.workspace.openTextDocument(uri);
        const editor = await vscode.window.showTextDocument(document);

        acceptCode({uuid, accepted: true, content: ''});
        const diffMap = this.getDiffRange(uri.fsPath);

        const modifiedLines = diffMap
            ?.diffs
            .map((diff, index) => {
                let diffContent = diff[1];
                if (!diffContent.endsWith('\n') && index !== diffMap.diffs.length - 1) {
                    diffContent += '\n';
                }

                return diffContent;
            })
            .join('')
            .split('\n') || [];
        const modifiedContent = this.getDiffRange(uri.fsPath)?.modifiedContent || '';
        const isFullLineReplace = this.getDiffRange(uri.fsPath)?.isFullLineReplace ?? true;
        const startLine = range.start.line;
        const endLine = startLine + modifiedLines.length - 1;
        const deleteRange = new vscode.Range(
            new vscode.Position(startLine, isFullLineReplace ? 0 : range.start.character),
            new vscode.Position(endLine, modifiedLines[modifiedLines.length - 1].length)
        );

        await editor.edit(
            editBuilder => {
                editBuilder.replace(deleteRange, modifiedContent);
            },
            {
                undoStopAfter: false,
                undoStopBefore: false,
            }
        );
        this.clearDiffRange(uri.fsPath);
    }

    async rejectBlockChanges(uri: vscode.Uri, range: vscode.Range, modifiedContent: string) {
        const document = await vscode.workspace.openTextDocument(uri);
        const editor = await vscode.window.showTextDocument(document);
        const modifiedLines = modifiedContent.split('\n');
        const isInsert = range.start.isEqual(range.end);
        const startLine = isInsert ? range.end.line : (range.end.line + 1);
        const endLine = isInsert ? startLine + (modifiedLines.length - 1) : (startLine + modifiedLines.length);
        const deleteRange = new vscode.Range(
            new vscode.Position(startLine, 0),
            new vscode.Position(endLine, 0)
        );

        await editor.edit(
            editBuilder => {
                editBuilder.delete(deleteRange);
            },
            {
                undoStopAfter: false,
                undoStopBefore: false,
            }
        );
        this.clearDiffRange(uri.fsPath);
    }

    async rejectLinesChanges(uri: vscode.Uri, range: vscode.Range) {
        const document = await vscode.workspace.openTextDocument(uri);
        const editor = await vscode.window.showTextDocument(document);
        const diffMap = this.getDiffRange(uri.fsPath);

        const modifiedLines = diffMap
            ?.diffs
            .map((diff, index) => {
                let diffContent = diff[1];
                if (!diffContent.endsWith('\n') && index !== diffMap.diffs.length - 1) {
                    diffContent += '\n';
                }

                return diffContent;
            })
            .join('')
            .split('\n') || [];
        const originalContent = this.getDiffRange(uri.fsPath)?.originalContent || '';
        const startLine = range.start.line;
        const endLine = startLine + modifiedLines.length - 1;
        const isFullLineReplace = this.getDiffRange(uri.fsPath)?.isFullLineReplace ?? true;
        const deleteRange = new vscode.Range(
            new vscode.Position(startLine, isFullLineReplace ? 0 : range.start.character),
            new vscode.Position(endLine, modifiedLines[modifiedLines.length - 1].length)
        );
        await editor.edit(
            editBuilder => {
                editBuilder.replace(deleteRange, originalContent);
            },
            {
                undoStopAfter: false,
                undoStopBefore: false,
            }
        );
        await this.clearDiffRange(uri.fsPath);
    }

    async applyCustomDiff(
        filePath: string,
        range: vscode.Range,
        modifiedContent: string,
        type: DiffType,
        {accept, reject}: {accept: (...args: any[]) => void, reject: (...args: any[]) => void}
    ) {
        const wrappedAccept = async (...args: any[]) => {
            // acceptCode({uuid, accepted: true, content: ''});
            this.clearDiffRange(filePath);
            this._onDidDiffAcceptedChanged.fire({accepted: true});
            this.customHandlers.delete(filePath);
            return accept(...args);
        };
        const wrappedReject = async (...args: any[]) => {
            this.clearDiffRange(filePath);
            this._onDidDiffAcceptedChanged.fire({accepted: false});
            this.customHandlers.delete(filePath);
            return reject(...args);
        };
        this.addDiffRange(filePath, {
            range,
            originalContent: '',
            modifiedContent,
            type,
            diffs: [],
            uuid: '',
        });
        this.customHandlers.set(filePath, {accept: wrappedAccept, reject: wrappedReject});
    }

    async acceptAllChanges() {
        const activeEditor = vscode.window.activeTextEditor;
        if (activeEditor) {
            const document = activeEditor.document;
            const uri = document.uri;
            if (this.customHandlers.has(uri.fsPath)) {
                const handler = this.customHandlers.get(uri.fsPath);
                return handler?.accept();
            }
            const diffInfo = this.getDiffRange(uri.fsPath);
            if (diffInfo) {
                const range = diffInfo.range;
                const uuid = diffInfo.uuid;
                if (diffInfo.type === 'block') {
                    await this.acceptBlockChanges(uri, range, uuid);
                }
                else {
                    await this.acceptLinesChanges(uri, range, uuid);
                }
            }
            this._onDidDiffAcceptedChanged.fire({accepted: true});
        }
    }

    async rejectAllChanges() {
        const activeEditor = vscode.window.activeTextEditor;
        if (activeEditor) {
            const document = activeEditor.document;
            const uri = document.uri;
            if (this.customHandlers.has(uri.fsPath)) {
                const handler = this.customHandlers.get(uri.fsPath);
                return handler?.reject();
            }
            const diffInfo = this.getDiffRange(uri.fsPath);
            if (diffInfo) {
                const range = diffInfo.range;
                if (diffInfo.type === 'block') {
                    const modifiedContent = diffInfo.modifiedContent;
                    await this.rejectBlockChanges(uri, range, modifiedContent);
                }
                else {
                    await this.rejectLinesChanges(uri, range);
                }
            }
            this._onDidDiffAcceptedChanged.fire({accepted: false});
        }
    }

    dispose() {
        this.insertedLineDecorationType.dispose();
        this.modifiedLineDecorationType.dispose();
        this.removedLineDecorationType.dispose();
        this.disposables.forEach(d => d.dispose());
    }
}
