import * as vscode from 'vscode';
import {injectable, inject} from 'inversify';
import {RegisteredCommand} from '@/constants';
import {ChatViewProvider} from '@/services/ChatViewProvider';
import {QuickFixProvider} from '@/services/QuickFixProvider';
import {EventMessage, WebviewAgentConversationType} from '@shared/protocols';
import {promptForFixAll} from './prompts';
import {getFileName, getDiagnostics, getDocument} from './utils';
import type {IDiagnostic} from './types';

@injectable()
export class ComateFixProvider implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];

    constructor(
        @inject(ChatViewProvider) private readonly chatViewProvider: ChatViewProvider,
        @inject(QuickFixProvider) private readonly quickFixProvider: QuickFixProvider,
    ) {
        this.disposables.push(
            vscode.commands.registerCommand(RegisteredCommand.ideRepairAllFile, async (reasoures: string[], markers: (IDiagnostic[])[]) => {
                this.fixFile(reasoures, markers, '修复全部问题');
            }),
            vscode.commands.registerCommand(RegisteredCommand.ideRepairFile, async (reasoures: string[], markers: (IDiagnostic[])[]) => {
                this.fixFile(reasoures, markers, `修复${getFileName(reasoures?.[0])}`);
            }),
            vscode.commands.registerCommand(RegisteredCommand.ideRepairMarker, async (reasoures: string[], markers: (IDiagnostic[])[]) => {
                this.fixSingeMark(reasoures?.[0], markers?.[0]);
            }),
        );
    }

    async fixFile(reasoures: string[], markers: (IDiagnostic[])[], defaultContent: string) {
        this.showChatPanel();
        this.chatViewProvider.sendDataToWebview(EventMessage.AgentConversationAddFromIdeEvent, {
            conversationId: '',
            conversationType: WebviewAgentConversationType.E2EBotConversation,
            messageType: 'add-conversation',
            payload: {
                defaultContent,
                knowledgeList: [],
                query: promptForFixAll(reasoures, markers),
            },
        });
    }

    async fixSingeMark(uri: string, markers: IDiagnostic[]) {
        this.showChatPanel();
        const diagnostics = getDiagnostics(markers);
        const document = await getDocument(uri);

        if (diagnostics.length) {
            this.quickFixProvider.fixAll(document, diagnostics);
        }
    }

    showChatPanel() {
        vscode.commands.executeCommand(RegisteredCommand.showChatPanel);
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
