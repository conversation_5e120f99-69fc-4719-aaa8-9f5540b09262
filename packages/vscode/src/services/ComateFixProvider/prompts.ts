import * as vscode from 'vscode';
import {buildDisplayMessage} from '@/services/QuickFixProvider/prompts';
import {getDiagnostics} from './utils';
import type {IDiagnostic} from './types';

export function promptMessages(diagnostics: readonly vscode.Diagnostic[], fileIndex: number) {
    const messages = diagnostics.map((v, index) => {
        return `${fileIndex + 1}-${index + 1}. ${buildDisplayMessage(v)}`
            + `所在文件中的开始位置：(Line: ${v.range.start.line + 1} Character: ${v.range.start.character + 1})`;
    });
    return `对应错误列表：\n${messages.join('\n')}`;
}

export function promptForFixAll(resource: string[], markers: (IDiagnostic[])[]) {
    const messages = resource.map((uri, index) => {
        return `${index + 1}. 有错误文件路径：${vscode.Uri.parse(uri)} ${promptMessages(getDiagnostics(markers[index]), index)}`
    });

    return `请修复文件中的错误：${messages.join('\n')} \n 注意：一条一条处理，处理过程是先查看对应错误位置，然后修复错误并且确保修改是正确的，其他内容不需要改动`;
}
