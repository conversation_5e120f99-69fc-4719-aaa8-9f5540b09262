import * as vscode from 'vscode';
import type {IDiagnostic} from './types';

export async function getDocument(uri: string) {
    return vscode.workspace.openTextDocument(vscode.Uri.parse(uri));
}

export function getDiagnostics(markers: IDiagnostic[]) {
    return markers.map(m => getDiagnostic(m));
}

export function getDiagnostic(marker: IDiagnostic) {
    return new vscode.Diagnostic(
        new vscode.Range(
            marker.range.startLine,
            marker.range.startColumn,
            marker.range.endLine,
            marker.range.endColumn,
        ),
        marker.message || '',
        marker.severity
    )
}

export function getFileName(uri: string) {
    return uri.split('/').pop();
}
