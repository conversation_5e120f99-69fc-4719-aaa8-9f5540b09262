import * as vscode from 'vscode';
import {injectable, inject} from 'inversify';
import {RegisteredCommand} from '@/constants';
import {isPoc} from '@/utils/features';
import {DiffProvider} from '../DiffProvider';
import {ChatBaseProvider} from '../ChatBaseProvider';

/**
 * CodeLens里增加开始对话的引导，提示对话的打开频次
 */
@injectable()
export class ChatTrialProvider extends ChatBaseProvider implements vscode.Disposable {
    static readonly supportedLanguages = [
        'python',
        'java',
        'go',
        'javascript',
        'typescript',
        'javascriptreact',
        'jsx',
        'typescriptreact',
        'cpp',
        'c',
    ];
    private disposables: vscode.Disposable[] = [];
    onDidChangeCodeLenses?: vscode.Event<void> | undefined;
    _onDidChange = new vscode.EventEmitter<vscode.Uri>();
    onDidChange: vscode.Event<vscode.Uri> = this._onDidChange.event;

    constructor(@inject(DiffProvider) readonly diffProvider: DiffProvider) {
        super(diffProvider);

        if (!isPoc) {
            this.disposables.push(
                vscode.commands.registerCommand(
                    RegisteredCommand.showInlineChatAndSelectCode,
                    (document: vscode.TextDocument, range: vscode.Range, ...args) => {
                        const editor = vscode.window.activeTextEditor;
                        if (editor?.document === document) {
                            editor.selection = new vscode.Selection(range.start, range.end);
                            vscode.commands.executeCommand(RegisteredCommand.showInlineChat, ...args);
                        }
                    }
                )
            );
        }
    }

    dispose() {
        // 清理所有事件监听器
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];

        // 清理EventEmitter
        this._onDidChange.dispose();
    }
}
