const {existsSync} = require('node:fs');
const {symlink, unlink} = require('node:fs/promises');
const path = require('node:path');
const {preparePackageJson} = require('./common.cjs');

async function prepareStaticFiles(platform, enterpriseVersion) {
    const filesToSync = [
        {
            source: path.resolve(
                __dirname, '..', `CHANGELOG.${platform}${enterpriseVersion ? '-' + enterpriseVersion : ''}.md`
            ),
            target: path.resolve(__dirname, '..', 'CHANGELOG.md'),
        },
        {
            source: path.resolve(
                __dirname, '..', `README.${platform}${enterpriseVersion ? '-' + enterpriseVersion : ''}.md`
            ),
            target: path.resolve(__dirname, '..', 'README.md'),
        },
    ];
    for (const {source, target} of filesToSync) {
        const sourceRelativeToTarget = path.relative(path.dirname(target), source);
        if (existsSync(target)) {
            await unlink(target);
        }
        await symlink(sourceRelativeToTarget, target);
        console.info(`Symlinked ${path.basename(source)} to ${path.basename(target)}`);
    }
}

async function main() {
    const platform = process.env.PLATFORM;
    const enterpriseVersion = process.env.ENTERPRISE_VERSION;
    const ide = process.env.IDE;
    if (!platform) {
        console.error('Error: please set PLATFORM env');
        process.exit(1);
    }
    await preparePackageJson(platform, enterpriseVersion, ide);
    await prepareStaticFiles(platform, enterpriseVersion);
    console.info('Done syncing!');
}

main();
