
const {execSync} = require('child_process');
const {readFile, writeFile} = require('fs/promises');
const path = require('path');
const fs = require('fs');
const {platformIndependentFields} = require('./constants.cjs');

async function stripPlatformSpecificFields() {
    console.info('Stripping platform specific fields from packages/vscode/package.json...');
    const packageJsonPath = path.resolve(__dirname, '..', 'package.json');
    const gitRootPath = path.resolve(__dirname, '..', '..', '..');
    const raw = await readFile(packageJsonPath, 'utf8');
    const jsonData = JSON.parse(raw);
    const entries = Object.fromEntries(
        platformIndependentFields
            .filter(field => field in jsonData)
            .map(field => [field, jsonData[field]])
    );
    const strippedJson = JSON.stringify(entries, null, 4);
    await writeFile(packageJsonPath, strippedJson);
    execSync(`git add ${packageJsonPath}`, {cwd: gitRootPath});
}

function weirwoodVersionChange() {
    const targetFiles = ['packages/weirwood.json'];
    const gitRootPath = path.resolve(__dirname, '..', '..', '..');
    const internalPkgPath = path.join(__dirname, '..', 'package.internal.json');
    console.info('internalPkgPath', internalPkgPath);

    if (!fs.existsSync(internalPkgPath)) {
        console.info('package.internal.json not found, skipping version update');
        return;
    }

    try {
        const pkg = JSON.parse(fs.readFileSync(internalPkgPath, 'utf8'));
        const version = pkg.version;

        targetFiles.forEach(filePath => {
            const fullPath = path.join(gitRootPath, filePath);
            if (!fs.existsSync(fullPath)) {
                console.info(`${filePath} not found, skipping`);
                return;
            }

            try {
                const data = JSON.parse(fs.readFileSync(fullPath, 'utf8'));
                if (data.buildid !== version) {
                    data.buildid = version;
                    fs.writeFileSync(fullPath, JSON.stringify(data, null, 2));
                    execSync(`git add ${fullPath}`, {cwd: gitRootPath});
                    console.info(`Updated ${filePath} with version ${version}`);
                }
            } catch (error) {
                console.error(`Error updating ${filePath}:`, error.message);
            }
        });
    } catch (error) {
        console.error('Error reading package.internal.json:', error.message);
    }
}

async function main() {
    await stripPlatformSpecificFields();
    weirwoodVersionChange();
}

main();
