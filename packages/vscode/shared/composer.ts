export type ComposerFileAction =
    | 'create'
    | 'rewrite'
    | 'edit'
    | 'rename'
    | 'delete'
    | 'explain'
    | 'preview'
    | 'replaceFrom';

export enum WorkflowStatus {
    UNREADY = 'unready',
    READY = 'ready',
    RUNNING = 'running',
    SUCCESS = 'success',
    FAILED = 'failed',
    CANCELLED = 'cancelled',
}

export enum AcceptState {
    UNTOUCHED = 0,
    ACCEPT = 1,
    REJECT = 2,
}

export type IllegalFileType = 'image' | 'video' | 'audio';
export interface IllegalTextExtDefinition {
    type: IllegalFileType;
    extensions: string[];
}

export interface ComposerAcceptFilePayload {
    action: 'file-view' | 'file-accept' | 'file-reject';
    filePath: string;
    id: number | string;
}
