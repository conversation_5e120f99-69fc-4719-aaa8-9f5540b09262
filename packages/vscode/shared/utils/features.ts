/**
 * 获取某个功能的开关，后续可以统一调整
 */

/** 功能名称 */
export enum FeatureName {
    /** #菜单-知识集 */
    HASH_MENU_KNOWLEDGE_SET,
    /** #菜单-API */
    HASH_MENU_API,
    /** 开放平台 */
    EXTENSION_PLATFORM,
    /** 侧边栏顶部的反馈入口 */
    CHAT_PANEL_FEEDBACK,
    /** 智能体 */
    AGENT_BOT,
}

export const isFeatureVisible = (featureName: FeatureName) => {
    switch (featureName) {
        case FeatureName.HASH_MENU_API:
        case FeatureName.EXTENSION_PLATFORM:
        case FeatureName.CHAT_PANEL_FEEDBACK:
        case FeatureName.HASH_MENU_KNOWLEDGE_SET: {
            return $features.ENTERPRISE_VERSION !== 'gitee';
        }
        case FeatureName.AGENT_BOT: {
            return true;
        }
        default: {
            const exhaustiveCheck: never = featureName;
            throw new Error(`${exhaustiveCheck} is not a valid feature name`);
        }
    }
};

/**
 * 登录接口的接口前缀，gitee 会增加 gitee- 前缀
 */
export const getLoginUrlPrefix = () => {
    switch ($features.ENTERPRISE_VERSION) {
        case 'gitee': {
            return 'gitee-';
        }
        default: {
            return '';
        }
    }
};

/**
 * 首页标题后缀 gitee 会增加 Gitee版 后缀
 */
export const getHomeTitleSuffix = () => {
    switch ($features.ENTERPRISE_VERSION) {
        case 'gitee': {
            return ' (Gitee版)';
        }
        default: {
            return '';
        }
    }
};

/**
 * 智能云用户快速登录功能显隐 flag, gitee 版会隐藏
 */
export const showLicenseLoginVisible = () => {
    switch ($features.ENTERPRISE_VERSION) {
        case 'gitee': {
            return false;
        }
        default: {
            return true;
        }
    }
};

/**
 * AutoWork 功能显隐 flag, gitee 版会隐藏
 */
export const showRelatedToAtVisible = () => {
    switch ($features.ENTERPRISE_VERSION) {
        case 'gitee': {
            return false;
        }
        default: {
            return true;
        }
    }
};

/**
 * 第三方插件功能显隐 flag, gitee 版会隐藏
 */
export const showThirdPartyAgentsVisible = () => {
    switch ($features.ENTERPRISE_VERSION) {
        case 'gitee': {
            return false;
        }
        default: {
            return true;
        }
    }
};

/**
 * 第三方指令功能显隐 flag, gitee 版会隐藏
 */
export const showThirdPartyCommandsVisible = () => {
    switch ($features.ENTERPRISE_VERSION) {
        case 'gitee': {
            return false;
        }
        default: {
            return true;
        }
    }
};

/**
 * 剩余vip天数显隐 flag, gitee 版会隐藏
 */
export const showVipDaysVisible = () => {
    switch ($features.ENTERPRISE_VERSION) {
        case 'gitee': {
            return false;
        }
        default: {
            return true;
        }
    }
};
