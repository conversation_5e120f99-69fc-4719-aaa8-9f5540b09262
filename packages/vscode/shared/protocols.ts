/* eslint-disable max-lines */
/**
 * 这个文件定义了插件和 webview 之间通信的协议，一旦约定就避免修改现有的事件名以及字段含义，因为不同的 webview 使用方依赖了这些约定。
 * 如有新增和 breaking 的变更，请在这个文档同步 https://ku.baidu-int.com/d/s5x3LDDJBw-o8W 并通知给 webview 使用方及时更新。
 */
import type {
    AgentConversationInfo as AgentConversationInfoProps,
    AgentMessage,
    VirtualEditorMethodCall,
    LicenseFullDetail,
    AgentPayload,
    ActionConfig,
    RulePanelAction,
} from '@comate/plugin-shared-internals';
import * as vscode from 'vscode';
import type {
    PassthroughMessageParams,
    PassthroughMessageResponse,
} from '@comate/kernel-shared';
import {AcceptState, WorkflowStatus} from './composer';

export interface CodeSelectionEventData {
    selection: string;
    language: string;
}
export type ColorTheme = 'dark' | 'light';

export enum EventMessage {
    /**
     * * webview -> ide
     */
    HelpSendEvent = 'helpSendEvent',
    SubmitCommandEvent = 'submitCommandEvent',
    UsernameFetchEvent = 'usernameFetchEvent',
    MessagesFetchEvent = 'messagesFetchEvent',
    ColorThemeFetchEvent = 'colorThemeFetchEvent',
    UpdateThemeEvent = 'updateThemeEvent',
    QuerySendEvent = 'querySendEvent',
    MessageActionTriggerEvent = 'messageActionTriggerEvent',
    ResponseCancelEvent = 'responseCancelEvent',
    /** 通知打开某个channle */
    ShowOutputEvent = 'showOutputEvent',
    // 非VSCODE端使用
    ClearMessagesEvent = 'clearMessagesEvent',
    LinkClickEvent = 'linkClickEvent',
    ShowCodeBlockEvent = 'showCodeBlockEvent',
    FeatureFlagsFetchEvent = 'featureFlagsFetchEvent',
    LicenseValidityFetchEvent = 'licenseValidityFetchEvent',
    LicenseValidityFetchEvent2 = 'licenseValidityFetchEvent2',
    FilesFetchEvent = 'filesFetchEvent',
    SelectedFilesRecordEvent = 'selectedFilesRecordEvent',
    WebViewInitEvent = 'webViewInitEvent',
    // 获取登录状态事件
    LoginStatusFetchEvent = 'loginStatusFetchEvent',
    // 更新登录状态事件
    LoginStatusChangeEvent = 'loginStatusChangeEvent',
    // 退出登录
    LoginOutEvent = 'loginOutEvent',
    // webview触发登录
    LoginTriggerEvent = 'loginTriggerEvent',
    /** 查询当前工作区的状态，包括是否构建完成索引、存在激活的文件、是否是合法的git仓库等 */
    WorkspaceStatusRequest = 'WorkspaceStatusRequest',
    // 配置license
    OpenLicenseConfigEvent = 'openLicenseConfigEvent',
    // 视图初始化时获取头像事件
    AvatarFetchEvent = 'avatarFetchEvent',
    // 视图获取当前语言
    LanguageFetchEvent = 'languageFetchEvent',
    InputHistoryRefreshEvent = 'inputHistoryRefreshEvent',
    /** 通用的消息提示事件 */
    IdeToastMessageEvent = 'IdeToastMessageEvent',
    /** license 详细信息，主要和企业有关 */
    LicenseFullDetailFetchEvent = 'licenseFullDetailFetchEvent',
    /** [NotUsed] 更换用户引导事件 */
    UserGuideChangeEvent = 'userGuideChangeEvent',
    /** 获取一些基础信息 */
    InitFetchEvent = 'initFetchEvent',
    SwitchChatSessionEvent = 'switchChatSessionEvent',
    ChatSessionListFetchEvent = 'chatSessionListFetchEvent',
    ChatSessionDeleteEvent = 'chatSessionDeleteEvent',
    /** 获取全部智能体任务 */
    AgentConversationFetchEvent = 'agentConversationFetchEvent',
    /** 新增智能体任务 */
    AgentConversationAddEvent = 'agentConversationAddEvent',
    /** 设置某个智能体任务到前台并开始获取消息 */
    AgentConversationSetForegroundEvent = 'agentConversationSetForegroundEvent',
    AgentConversationNewMessageEvent = 'agentConversationNewMessageEvent',
    /** 发送panelData */
    PanelDataSendEvent = 'panelDataSendEvent',
    /** 通知panel ready */
    PanelReadyEvent = 'panelReadyEvent',
    /** 修改智能体开关状态 */
    AgentSwitchChangeEvent = 'agentSwitchChangeEvent',
    /** 增加缓存 */
    CacheAddEvent = 'CacheAddEvent',
    /** 获取缓存 */
    CacheGetEvent = 'CacheGetEvent',
    /** 旧逻辑在插件侧管理模型信息 */
    /** 新逻辑在webview侧管理模型信息 */
    /** （旧）获取默认模型 */
    DefaultModelSelectEvent = 'DefaultModelSelectEvent',
    /** （旧）更新选择的模型 */
    UpdateDefaultModelSelectEvent = 'UpdateDefaultModelSelectEvent',
    /** （新）获取选择的模型 */
    GetLastUsedModelEvent = 'getLastUsedModelEvent',
    /** （新）更新选择的模型的模型 */
    UpdateLastUsedModelEvent = 'updateLastUsedModelEvent',
    /** 创建自定义Prompt */
    PromptTemplateCreateEvent = 'promptTemplateCreateEvent',
    /** 编辑自定义Prompt */
    PromptTemplateEditEvent = 'PromptTemplateEditEvent',
    /** 删除自定义Prompt */
    PromptTemplateDeleteEvent = 'promptTemplateDeleteEvent',
    /** 私有化功能，接入SQL获取表结构 */
    GetSqlConnectionEvent = 'getSqlConnectionEvent',
    SubmitSqlConnectionEvent = 'submitSqlConnectionEvent',
    /** webview 发送通过ide透传的kernel消息 */
    PassthroughMessageToIDEFromWebviewEvent = 'webview_passthroughMessage_toIde',
    /** webview 里的服务初始化完成 */
    WebViewServiceInitEvent = 'webviewServiceInit',
    WebViewCloseEvent = 'webViewCloseEvent',
    /** 删除已选代码块 */
    CodeSelectionDeleteEvent = 'codeSelectionDeleteEvent',
    /** 规则管理页的操作事件 */
    RulePanelActionEvent = 'rulePanelActionEvent',
    /**
     * * ide -> webview
     */
    ColorThemeChangeEvent = 'colorThemeChangeEvent',
    MessagesRefreshEvent = 'messagesRefreshEvent',
    SuggestionsRefreshEvent = 'suggestionsRefreshEvent',
    ClearSessionEvent = 'clearSessionEvent',
    UsernameChangeEvent = 'usernameChangeEvent',
    // 外侧服务控制 input 执行 focus
    InputFocusEvent = 'inputFocusEvent',
    FeedbackTriggerEvent = 'feedbackTriggerEvent',
    ShowWebviewPanelEvent = 'showWebviewPanelEvent',
    /** @deprecated jetbrains 上没实现，使用 utils/clipboard */
    CopyToClipboardEvent = 'copyToClipboardEvent',
    IntegratedCommandFetchEvent = 'integratedCommandFetchEvent',
    UpdateChatCompletionEvent = 'updateChatCompletionEvent',
    EngineInitEvent = 'engineInitEvent',
    ClearPermissionEvent = 'clearPermissionEvent',
    FeatureFlagsChangeEvent = 'featureFlagsChangeEvent',
    // 获取当前的 query
    CurrentQueryEvent = 'currentQueryEvent',
    // 输入框 focus 触发的事件
    HandleInputFocusEvent = 'handleInputFocusEvent',
    // TODO handler 那里是个map，不是个广义的eventemitter，所以不能重复监听或者注册
    LicenseValidityChangeEvent = 'licenseValidityChangeEvent',
    LicenseValidityChangeEvent2 = 'licenseValidityChangeEvent2',
    /** comate开放平台里助理模式，扫描文件后推送消息给WebView */
    ComatePairScanResultMessageStream = 'ComatePairScanResultMessageStream',
    /** comate开放平台里助理模式，停止生成 */
    ComatePairScanProgressCancel = 'ComatePairScanProgressCancel',
    /** comate开放平台里助理模式，没切换到助理面板时，同步一次所有消息 */
    ComatePairScanResultMessageSyncRequest = 'ComatePairScanResultMessageSyncRequest',
    /** comate开放平台里助理模式，点击生成的文件块上的采纳按钮，发消息给IDE执行替换 */
    ComatePairFileContentReplace = 'ComatePairFileContentReplace',
    ComatePairFileContentCheckExist = 'ComatePairCheckFileContentExist',
    ComatePairFileContentCopy = 'ComatePairCheckFileContentCopy',
    /** comate开放平台里助理模式，清除所有消息 */
    ComatePairClearHistory = 'ComatePairClearHistory',
    /** comate开放平台里助理模式，监听文件的变更，如果其他场景需要复用，可以调整 */
    ComatePairActiveDocumentChange = 'ComatePairActiveDocumentChange',
    /** todo comate助理模式 tab变更后同步到IDE这侧，后续 @chenze03 改成更通用的形式 */
    SurveyVersionRefreshEvent = 'surveyVersionRefreshEvent',
    SurveyVersionFetchEvent = 'surveyVersionFetchEvent',
    SurveySendEvent = 'surveySendEvent',
    OpenPluginConfigPanelEvent = 'openPluginConfigPanelEvent',
    SuggestionClickEvent = 'suggestionClickEvent',
    // 头像更新事件
    AvatarChangeEvent = 'avatarChangeEvent',
    ModalOpenEvent = 'modalOpenEvent',
    ModalSubmitEvent = 'modalSubmitEvent',
    /** @deprecated 不要了，用clicklink代替 */
    PluginConfigurationButtonClickEvent = 'pluginConfigurationButtonClickEvent',
    SettingButtonClickEvent = 'settingButtonClickEvent',
    ToggleChatPanelEvent = 'toggleChatPanelEvent',
    PluginConfigurationChangeEvent = 'pluginConfigurationChangeEvent',
    WorkspaceStatusEvent = 'workspaceStatusEvent',
    QueryVisibilitySelector = 'QueryVisibilitySelector',
    QueryVisibilitySelectorLogEvent = 'QueryVisibilitySelectorLogEvent',
    // 切换语言时通知视图
    LanguageChangeEvent = 'languageChangeEvent',
    // 切换语言
    UpdateLanguageEvent = 'updateLanguageEvent',
    // 用户使用日志打点
    UserActionStartLogEvent = 'userActionStartLogEvent',
    UserActionStopLogEvent = 'userActionStopLogEvent',
    CopyLogHistoryEvent = 'copyLogHistoryEvent',
    /** Autowork Client端实现发API的能力 */
    AutoWorkAPIProxy = 'AutoWorkAPIProxy',
    // webView 内，通过 postmessage上传用户行为日志
    UploadUserActionLog = 'UploadUserActionLog',
    /** 智能体创建一个完整示例项目 */
    AgentCreateDemo = 'AgentCreateDemo',
    // 侧边栏输入框历史记录，用户可以通过上下键快速取用
    InputHistoryFetchEvent = 'inputHistoryFetchEvent',
    /** [NotUsed] 获取用户引导列表 */
    UserGuideFetchEvent = 'userGuideFetchEvent',
    // 获取知识相关引导列表
    RepoGuideFetchEvent = 'repoGuideFetchEvent',
    SearchAPIByKeywordEvent = 'searchAPIByKeywordEvent',
    TrackClickSearchedAPIEvent = 'trackClickSearchedAPIEvent',
    // 更新知识集列表
    UpdateKnowledgeListEvent = 'updateKnowledgeListEvent',
    // 获取saas用户是否是混合云账号
    CustomizeUserFetchEvent = 'customizeUserFetchEvent',
    // 更新saas用户是否是混合云账号
    CustomizeUserChangeEvent = 'customizeUserChangeEvent',
    IndexRecreateEvent = 'indexRecreateEvent',
    // 获取提示信息
    ToastMessageChangeEvent = 'toastMessageChangeEvent',
    LicenseFullDetailChangeEvent = 'licenseFullDetailChangeEvent',
    WebviewConsoleLogEvent = 'webviewConsoleLogEvent',
    CodeIndexEvent = 'codeIndexEvent',
    // 通用请求代理事件
    PassthroughRequest = 'PassthroughRequest',
    SmartApplyUpdate = 'SmartApplyUpdate',
    // 更多操作事件
    MoreOperationsEvent = 'moreOperationsEvent',
    // 打开帮助文档
    VisitHelpDocsEvent = 'visitHelpDocsEvent',
    FeedbackEvent = 'feedbackEvent',
    // 新建对话事件
    CreateNewChatEvent = 'createNewChatEvent',
    // 历史会话事件
    HistoryChatEvent = 'historyChatEvent',
    /** 更新前台智能体消息 */
    AgentConversationMessageUpdateEvent = 'agentConversationMessageUpdateEvent',
    VirtualEditorEvent = 'VirtualEditorEvent',
    MockVirtualEditorEventFromEngine = 'MockVirtualEditorEventFromEngine',
    /** 其他入口创建智能体任务 */
    AgentConversationAddFromIdeEvent = 'agentConversationAddFromIdeEvent',
    /** 控制智能体command是否开启 */
    AgentCommandVisibleEvent = 'agentCommandVisibleEvent',
    /** 发送展示数据给panel */
    PanelDataListenEvent = 'panelDataListenEvent',
    /** 获取智能体开关状态 */
    AgentSwitchStatusEvent = 'agentSwitchStatusEvent',
    /** ide更新智能体开关状态 */
    AgentSwitchChangeFromIdeEvent = 'agentSwitchChangeFromIdeEvent',
    /** 触发下载运行日志 */
    DownloadLogEvent = 'downloadLogEvent',
    /** 获取自定义Prompt列表 */
    PromptTemplateListUpdateEvent = 'promptTemplateListUpdateEvent',
    /** 自定义Prompt新建成功 */
    PromptTemplateSaveEvent = 'promptTemplateSaveEvent',
    ChatCodeGraphEvent = 'chatCodeGraphEvent',
    EditorChangedEvent = 'editorChangedEvent',
    PrivateServiceUpdateEvent = 'privateServiceUpdateEvent',
    /** webview 接受来通过ide透传的kernel消息 */
    PassthroughMessageToWebviewFromIDEEvent = 'ide_passthroughMessage_toWebview',
    /** 设置或者获取本地的持久化状态 */
    PersistentStorageEvent = 'persistentStorageEvent',
    OpenNewFolderEvent = 'openNewFolderEvent',
    InputBoxUpdateEvent = 'inputBoxUpdateEvent',
    ScreenReaderStateChangeEvent = 'screenReaderStateChangeEvent',
    ScreenReaderStateFetchEvent = 'screenReaderStateFetchEvent',
    // 导入配置
    ImportIdeSetEvent = 'ImportIdeSetEvent',
    // 打开ide的设置页
    OpenIdeSettingEvent = 'OpenIdeSettingEvent',
    // 获取ide的配置信息同步到webview（补全相关的）
    UpdateSettingItemEvent = 'UpdateSettingItemEvent',
    // 获取ide的配置信息同步到webview（zuluChat相关的）
    zuluChatConfigEvent = 'zuluChatConfigEvent',
    initSettingEvent = 'initSettingEvent',
    // 采纳快捷键发生变化
    InlineSuggestCommitKeyChangeEvent = 'InlineSuggestCommitKeyChangeEvent',
    // 触发快捷键发生变化
    InlineSuggestTriggerKeyChangeEvent = 'InlineSuggestTriggerKeyChangeEvent',
    /** IDE 控制规则管理页的事件 */
    UpdateRulePanelEvent = 'updateRulePanelEvent',
}

export type ComposerMessages = Array<DehydratedUserMessage | DehydratedComposerMessage>;

export interface DehydratedComposerMessage extends DehydratedAssistantMessage {
    tasks: Array<{status: WorkflowStatus, filePath: string, accepted: AcceptState}>;
}

export type ComposerAction =
    | {id: number, action: 'accept' | 'abandon' | 'diff', filePath?: string}
    | {id: number, action: 'cancel'}
    | {action: 'clear'};

export interface ActiveEditor {
    name: string;
    relativePath: string;
    selection: [number, number] | null;
    id: string;
    content?: string;
}

export interface EventMessageCallbackTypes {
    [EventMessage.UsernameFetchEvent]: () => {
        /** 用户名 */
        username: string;
        chineseName?: string;
    };
    [EventMessage.QuerySendEvent]: (params: UserQueryPromptType) => void;
    [EventMessage.UpdateKnowledgeListEvent]: (knowledgeList: KnowledgeList[]) => void;
    [EventMessage.CustomizeUserFetchEvent]: () => {customized: boolean};
    [EventMessage.CustomizeUserChangeEvent]: (data: {customized: boolean}) => void;
    [EventMessage.UserGuideFetchEvent]: () => ConfigItem[];
    [EventMessage.SearchAPIByKeywordEvent]: (params: SearchAPIParamType) => APIItem[];
    [EventMessage.TrackClickSearchedAPIEvent]: (params: TrackAPIParamType) => void;
    [EventMessage.LoginOutEvent]: () => void;
    /** @returns key:string, typeId: number, typeCode: string, type:string 返回当前账号类型，比如个人免费，企业付费 */
    [EventMessage.LicenseFullDetailFetchEvent]: () => LicenseFullDetail & {endpoint?: string};
    [EventMessage.LicenseFullDetailChangeEvent]: (params: LicenseFullDetail & {endpoint?: string}) => void;
    [EventMessage.AutoWorkAPIProxy]: <K extends keyof AutoWorkAPIProxyCollection>(
        params: Omit<AutoWorkAPIProxyCollection[K]['params'], 'userName'>
    ) => AutoWorkAPIProxyCollection[K]['response'];
    [EventMessage.UserGuideChangeEvent]: () => ConfigItem[];
    [EventMessage.ToastMessageChangeEvent]: (data: ToastMessage) => void;
    [EventMessage.CodeIndexEvent]: (params: {command: 'recreate' | 'inquire'}) => {progress: number, total?: number};
    [EventMessage.AgentConversationFetchEvent]: () => {allConversations: AgentConversationInfo[]};
    [EventMessage.AgentConversationAddEvent]: (
        webviewAgentPayload: {agentPayload: WebviewAgentPayload}
    ) => AgentConversationInfo;
    [EventMessage.AgentConversationSetForegroundEvent]: (payload: {agentPayload: {conversationId: string}}) => void;
    [EventMessage.AgentConversationMessageUpdateEvent]: (data: {
        type: 'conversation-messages' | 'conversation-status' | 'partial-message-data' | 'partial-message-elements';
        conversationInfo: AgentConversationInfo;
        messages?: AgentMessage[];
        messageData?: Partial<AgentMessage> & {id: string};
    }) => void;
    // 通用请求代理事件
    [EventMessage.PassthroughRequest]: <T>(params: UnityRequestType) => HttpUnityResponseType<T>;
    [EventMessage.VirtualEditorEvent]: <K extends VirtualEditorMethodCall>(
        params: Omit<K, 'response'>
    ) => K['response'];
    [EventMessage.AgentConversationAddFromIdeEvent]: (data: WebviewAgentPayload) => void;
    [EventMessage.AgentCommandVisibleEvent]: (data: {type: WebviewAgentConversationType, visible: boolean}) => void;
    [EventMessage.CacheAddEvent]: (data: {key: string, value: any}) => void;
    [EventMessage.CacheGetEvent]: (key: string) => any;
    [EventMessage.DefaultModelSelectEvent]: (list: any[]) => {chat: string | undefined, completion: string | undefined};
    [EventMessage.UpdateDefaultModelSelectEvent]: (data: {id: string, type: 'CHAT' | 'COMPLETION'}) => void;
    [EventMessage.GetLastUsedModelEvent]: () => {chat: string | undefined, completion: string | undefined};
    [EventMessage.UpdateLastUsedModelEvent]: (data: {id: string, type: 'CHAT' | 'COMPLETION'}) => void;
    [EventMessage.ChatCodeGraphEvent]: (
        payload: {action: 'view' | 'collapse', collapsed?: boolean, messageId: number | string}
    ) => void;
    [EventMessage.EditorChangedEvent]: (
        editor: {activeEditor: ActiveEditor | null, fromCommand?: boolean, fromTerminal?: boolean}
    ) => {
        activeEditor: ActiveEditor | null;
    };
    [EventMessage.PrivateServiceUpdateEvent]: () => void;
    [EventMessage.PassthroughMessageToIDEFromWebviewEvent]: <T, K>(
        params: PassthroughMessageParams<T> | PassthroughMessageResponse<T>
    ) => PassthroughMessageResponse<K>;
    [EventMessage.PassthroughMessageToWebviewFromIDEEvent]: <T>(params: PassthroughMessageResponse<T>) => void;
    [EventMessage.PersistentStorageEvent]: (
        data:
            | {action: 'read', scope: 'global', key: string}
            | {action: 'read', scope: 'workspace', key: string}
            | {action: 'write', scope: 'global', key: string, value: any}
            | {action: 'write', scope: 'workspace', key: string, value: any}
    ) => any;
    [EventMessage.OpenNewFolderEvent]: () => any;
    [EventMessage.InputBoxUpdateEvent]: (data: {
        agent?: string;
        command?: string;
        query?: string;
    }) => any;
    [EventMessage.UpdateRulePanelEvent]: (params: {action: string}) => void;
    [EventMessage.RulePanelActionEvent]: <T extends RulePanelAction>(params: {
        action: T;
        payload: any;
    }) => Promise<{status: string, payload: any}>;
    [key: string]: (params: any) => any; // TODO 类型补全后删除掉
}

export enum ChatLabFunction {
    STORY_TO_CODE,
}

export type Role =
    | 'system' // 系统级消息（暂未用到）
    | 'user' // 用户消息
    | 'assistant'; // 助手消息

export type MessageStatus =
    | 'inProgress' // 正在生成中
    | 'success' // 生成完成
    | 'canceled' // 已取消
    | 'failed'; // 生成失败

export type MessageType =
    | 'apiDoc' // 接口文档
    | 'unitTest' // 单测
    | 'nl2Code' // 自然语言生成代码
    | 'help'
    | 'session'
    | 'docstring' // 函数注释
    | 'explain' // 代码解释
    | 'splitFunction' // 长函数拆分
    | 'optimizeFunction' // 调优建议
    | 'inlineComment' // 行间注释
    | 'thirdParty'
    | 'comatePlus'
    | 'addInlineLog'; // 添加日志

// TODO 精确支持replaceToFile: (filePath, from, to) => Promise<void>，当前难度太大，先简单实现
export type Actions = Record<
    string,
    (content?: any, language?: string, extra?: any, markdown?: Message) => void
>;

// AutoComate 思考过程中的任务的状态
export enum TaskStatus {
    INIT = 'INIT',
    PROCESSING = 'PROCESSING',
    SUCCEED = 'SUCCEED',
    FAIL = 'FAIL',
}

interface SubTask {
    /** 任务描述 */
    desc: string;
    status: TaskStatus;
    /** 任务详情 */
    detail?: string[];
    /** type 决定当前思考过程的 tag 的显示 */
    taskType: 'SEARCH' | 'PLAN' | 'REASONING' | 'THOUGHT' | 'CODE_GENERATE' | 'ANSWER' | 'ANALYZE';
}
// AutoComate 思考过程中的任务
export interface Task extends SubTask {
    /** 二级子任务 */
    subTasks?: SubTask[];
}

interface BaseDynamicSection {
    label: string;
    done: boolean;
    collapsible: boolean;
}

// 对应多项任务进度展示区域的数据
export interface DynamicActionItems extends BaseDynamicSection {
    type: 'actionItems';
    tasks: Task[];
    relevantFiles?: string[];
}

// 代码块
interface CodeChunk {
    filePath: string;
    code: string;
    language: string;
    startLine: number;
    endLine: number;
}

interface KnowledgeChunk {
    id: string;
    content: string; // 文档内容
    url: string; // 原文地址
    title: string; // 原文标题
}

export interface WebChunk {
    id: number; // 网页id，顺序id用于展示角标
    title: string; // 网页标题
    url: string;
}

// 对应代码块展示区域的数据
export interface DynamicCodeChunks extends BaseDynamicSection {
    type: 'codeChunks';
    showGraph?: boolean;
    codeChunks?: CodeChunk[];
    knowledgeChunks?: KnowledgeChunk[];
    webChunks?: WebChunk[];
}

// 对应关联文件/目录展示区域
export interface DynamicRelativeFiles extends BaseDynamicSection {
    type: 'relativeFiles';
    files: string[];
}

export type OperateType = 'ADD' | 'CHANGE' | 'DELETE';

export interface Snippet {
    original: string;
    updated: string;
}

export interface File {
    filepath: string;
    operateType: OperateType;
    steps: string[]; // 文件对应的执行步骤，不需要展示
    snippetsDetail?: Snippet[];
}

export interface Step {
    id: number;
    desc: string;
    status: TaskStatus;
    tasks: Task[];
}

export interface DynamicCodeGenSteps extends BaseDynamicSection {
    type: 'codeGenSteps';
    steps: Step[];
}

export interface DynamicReminder extends BaseDynamicSection {
    type: 'reminder';
    reminder: string;
    setting?: string;
}

export interface Notification {
    notificationType: 'info' | 'fail' | 'success';
    title: string;
    path?: string;
}

export interface DynamicNotification extends BaseDynamicSection {
    type: 'notification';
    notifications: Notification[];
}

export interface DynamicReasoning extends BaseDynamicSection {
    type: 'reasoning';
    content?: string;
    status: MessageStatus;
}

// DynamicSection 是 Message 中会动态更新的一个区域
export type DynamicSection =
    | DynamicActionItems
    | DynamicCodeChunks
    | DynamicCodeGenSteps
    | DynamicRelativeFiles
    | DynamicReminder
    | DynamicNotification
    | DynamicReasoning;

export interface MessageExtraReplyMessage {
    /** 消息id */
    id: number;
    /** 消息内容 */
    content: string;
    agent?: string;
    slash?: string;
}
export interface MessageExtra {
    capability?: string;
    /** 自定义指令名称 */
    capabilityDisplayName?: string;
    /** 是否以新版本的JSX组件渲染，主要是为了代码块的采纳功能 */
    renderJSX?: boolean;
    /** 代码图谱的数据 */
    codeGraphThumbnail?: {
        collapsed: boolean;
        data: any;
    };
    knowledgeList?: KnowledgeList[];
    uuid?: string; // track 用的 uuid
    replyMessage?: MessageExtraReplyMessage; // 用于在回复中展示保存自定义Prompt的按钮
}

export interface Metadata {
    // 区分用户是否开启了安全增强模式
    secure?: boolean;
    /** (SAAS & VPC)自定义 prompt 参数说明 */
    /* (SAAS & VPC)自定义 prompt 内容 */
    prompt?: string;
    /* (SAAS & VPC)自定义 prompt 的指令名称 */
    capabilityDisplayName?: string;
}

/** action的其他信息配置集合 */
export type ActionConfigs = Record<string, ActionConfig<string>>;

interface BaseMessage {
    id: number;
    /** 消息状态 */
    status: MessageStatus;
    /** 消息发送者角色 */
    role: Role;
    /** 消息内容（markdown文本） */
    content: string;
    /** 是否流式输出 */
    stream?: boolean;
    /** 选中的代码 */
    code?: string;
    /** 消息类型 */
    type: MessageType;
    /** 创建时间戳 */
    timestamp: number;
    /** 回复给哪个Message */
    replyTo?: number;
    /** 展示哪些操作按钮 */
    actions?: Actions;
    /** action的其他信息配置集合 */
    actionConfigs?: ActionConfigs;
    cancelTokenSource?: vscode.CancellationTokenSource;
    uuid?: string;
    /** iapi uuid */
    iapiUuid?: string;
    /** 额外属性 */
    metadata?: Metadata;

    // 以下字段是开放平台插件所拥有的
    /** 该消息的来源，默认值根据 role 区分是用户名还是 comate，可以用作插件名 */
    name?: string;
    /** 该消息的来源的头像，默认值根据 role 区分是用户头像还是 comate logo */
    icon?: string;
    /** 该消息的额外信息，用于传递一些特殊的信息，比如插件的能力，引用信息等 */
    extra?: MessageExtra;
    /** 消息取消时的回调函数 */
    onCancel?: () => void;
    sessionUuid?: string;

    conversationId?: number;
}
export interface SystemMessage extends BaseMessage {
    role: 'system';
}

export interface UserMessage extends BaseMessage {
    role: 'user';
    knowledgeList?: KnowledgeList[];
    agent?: string; // 表示@调用了哪个插件
    slash?: string; // 表示/功能，当前仅有 search（搜索）和 generate（生成），另外只有 role 为 user 时有此字段
}

export interface AssistantMessage extends BaseMessage {
    role: 'assistant';
    agent?: string;
    slash?: string;
    language?: string;
    dynamicSections?: DynamicSection[];
    /** 给 UT agent 的数据，现在应该不再使用了 */
    dynamicFooterSections?: DynamicSection[];
}

interface DehydratedSystemMessage extends Omit<BaseMessage, 'cancelTokenSource' | 'onCancel' | 'actions'> {
    role: 'system';
    actions: string[];
}

export interface DehydratedUserMessage extends Omit<BaseMessage, 'cancelTokenSource' | 'onCancel' | 'actions'> {
    role: 'user';
    knowledgeList?: KnowledgeList[];
    agent?: string;
    slash?: string;
    actions: string[];
}

// eslint-disable-next-line max-len
export interface DehydratedAssistantMessage
    extends Omit<AssistantMessage, 'cancelTokenSource' | 'onCancel' | 'actions'> {
    role: 'assistant';
    actions: string[];
}

export type Message = SystemMessage | UserMessage | AssistantMessage;

export type DehydratedMessage = DehydratedSystemMessage | DehydratedUserMessage | DehydratedAssistantMessage;

export interface Prompt {
    prompt: string;
    needContext?: boolean;
    type: 'ut' | 'doc' | 'default' | 'utSetting' | 'help';
    agent?: string;
    slash?: string;
    knowledgeList?: KnowledgeList[];
}

export interface UserFeedback {
    otherFeedback: string;
    incompleteContent: boolean;
    incorrectFormat: boolean;
    irrelevantAnswer: boolean;
    notHelpful: boolean;
}

export interface FeedbackOptions {
    // 取消、赞、踩
    isLike?: '0' | '1' | '2';
    userFeedback?: UserFeedback;
}

export enum Feature {
    SmartApply = 'smartApply',
    ComatePlus = 'comatePlus',
    ComatePair = 'comatePair',
    SaaSV0301 = 'SaaSV31',
    Jetbrains = 'jetbrains',
    IssueGenerate = 'issueGenerate',
    /** 是否可以使用iapi */
    EnableIAPI = 'enableIAPI',
    /** 对话显示错误信息 */
    OnlyErrorResponseInChat = 'onlyErrorResponseInChat',
    /** 是否允许用户指引 */
    EnableUserGuide = 'enableUserGuide',
    /** 是否开启智能采纳 */
    EnableSmartApply = 'enableSmartApply',
    /** 是否展示漏洞扫描提醒 */
    SecGit = 'comateSecGit',
    /** 是否进行代码扫描 */
    IScan = 'iScan',
    /** 是否进行SA扫描 */
    SA = 'SA',
}

export type FeatureFlags = Record<Feature, boolean>;

export enum BuiltinAgent {
    Comate = 'Comate',
    AutoWork = 'AutoWork',
    PromptTemplate = 'PromptTemplate',
    ComateConsole = 'ComateConsole',
}

export interface CommandList {
    displayName: string; // 中文名称
    displayNameKey?: string; // 名称需要翻译时对应的key
    name: string; // 英文名称
    owner: AgentList;
    originOwner?: AgentList;
    type?: string; // 'Skill' | 'Prompt' | 'Fallback'
    tipConfig?: {
        tip?: string;
        tipCodeBlock?: {
            type: string;
            showTipWhileEmpty: boolean;
        };
    };
    /* (SAAS & VPC)自定义 prompt 内容 */
    prompt?: string;
}

export interface CommandListWithId extends CommandList {
    id: string;
}

export interface AgentList {
    name: string;
    displayName?: string;
    description?: string;
    descriptionKey?: string;
    icon?: string;
    capabilities?: CommandListWithId[];
}

export interface AgentListWithId extends AgentList {
    id: string;
}

export interface CommandList {
    displayName: string; // 中文名称
    displayNameKey?: string; // 名称需要翻译时对应的key
    name: string; // 英文名称
    owner: AgentList;
    originOwner?: AgentList;
    type?: string; // 'Skill' | 'Prompt' | 'Fallback'
    placeholder?: string;
    defaultUserMessage?: string;
    displayTag?: string;
    enableSmartApply?: boolean;
}

export interface CommandListWithId extends CommandList {
    id: string;
}

export enum ContextType {
    /** 当前文件 */
    CURRENT_FILE = 'CURRENT_FILE',
    /** 文件 */
    FILE = 'FILE',
    /** 目录 */
    FOLDER = 'FOLDER',
    /** 代码库 */
    REPO = 'REPO',
    /** 系统知识集 */
    SYSTEM = 'SYSTEM',
    /** 普通知识集 */
    NORMAL = 'NORMAL',
    /** 网页 */
    URL = 'URL',
    /** Web，网络搜索 */
    WEB = 'WEB',
    /** 单个 API 链接 */
    API = 'API',
    /** API 的项目 */
    API_PROJECT = 'API_PROJECT',
    /** 用户划选的代码，理论上不会放到 context 里 */
    CODE = 'CODE',
    /** 历史存在，先保留该类型 */
    TEMP = 'TEMP',
    /** 终端，暂时只有autoDebug在用 */
    TERMINAL = 'TERMINAL',
    IMAGE = 'IMAGE',
    /** 规则 */
    RULE = 'RULE',
    /** 编码区右键添加的代码块 */
    CODE_SELECTION = 'CODE_SELECTION',
    /** 终端右键添加的信息 */
    TERMINAL_SELECTION = 'TERMINAL_SELECTION',
}

export interface KnowledgeList {
    id: string;
    name: string;
    content?: string;
    /** IMAGE 图片类型的在用 */
    contents?: string[];
    url?: string;
    retrievalType?: 'LOCAL_CODE' | 'TEXT';
    /** 当type为TERMINAL时，path代表终端路径 */
    path?: string;
    /** 当type为CODE_SELECTION时，selection代表选中代码的行号范围 */
    selection?: [number, number];
    type: ContextType;
}

export interface CodeSelection {
    id: string;
    name: string;
    filePath: string;
    selection: [number, number];
    content: string;
    type: ContextType.CODE_SELECTION | ContextType.TERMINAL_SELECTION;
}

export interface LicenseValidity {
    validDays?: number;
    renewUrl?: string;
    isActive?: boolean;
    keyType?: 'NONE' | 'IAM' | 'PASSORT' | 'GITEE' | 'GITHUB';
}

export interface Suggestion extends Prompt {
    id: number;
    displayTextKey: string;
    autoFocus?: boolean;
    onClick?: () => any;
}

export type UserQueryPromptType = Partial<Prompt> & {
    messageOrder: Record<number, number>;
    supportAt: boolean;
    /** 是否开启对话意图识别 */
    chatIntentRecognition?: boolean;
    /** 对话回复消息的提醒类信息 */
    responseReminder?: string;
    /** 是否禁用自动携带划选代码作为提问上下文 */
    disableCode?: boolean;
    /** 额外数据，这个字段目前是给自定义指令使用的，所以目前是一个{uuid: string;// command}的类型 */
    extraData?: any;
};

export const AutoWorkKnowledgeType = {
    EMPTY: 'empty',
    CURRENT_FILE: 'currentFile',
    REPO: 'repo',
    FOLDER: 'folder',
    FILE: 'file',
    IMAGE: 'image',
    KNOWLEDGE: 'knowledge',
    WEB: 'web',
    IAPI: 'iapi',
    ADD_KNOWLEDGE: 'navigateToKnowledge',
    WEB_SEARCH: 'webSearch',
    TERMINAL: 'terminal',
    RULE: 'rule',
};

type KnowledgeStatus = 'INACTIVATED' | 'ACTIVATED';
export const knowledgeScopes = ['API-', 'API_PROJECT-'];

export const placeholderChar = '\u00a0';

export interface ExtensionConfig {
    /** 是否默认开启对话的的基于代码库的上下文增强开关 */
    enableCodebaseEnhancedContext: boolean;
}
export interface AutoWorkAPIProxyCollection {
    generateLinkToUuid: {
        params: {link: string, userName: string};
        response: {uuid: string, title: string};
    };
    getAccessTo: {
        params: {userName: string, featureName: Feature};
        response: {status: boolean};
    };
    getKnowledgeStatus: {
        params: {uuid: string, userName: string};
        response: {status: KnowledgeStatus};
    };
    waitKnowledgeActivedInSecond: {
        params: {uuids: string[], second: number, userName: string};
        response: void;
    };
    getExtensionConfig: {
        params: undefined;
        response: Partial<ExtensionConfig>;
    };
    reportTerminalLogLines: {
        params: {platform: 'VSCODE', errorLog: string, repo: string, userName: string};
        response: void;
    };
    fetchKnowledgeList: {
        params: {query: string};
        response: KnowledgeList[];
    };
    setChatIntentRecognition: {
        params: {status: boolean};
        response: void;
    };
    getChatIntentRecognition: {
        params: void;
        response: {status: boolean};
    };
}

export type ExtensionDisplayLanguages = 'en' | 'zh';

export interface InputBoxMessageHistory {
    value: string;
    agent: string | undefined;
    slash: string | undefined;
    /** 原始的带mention的文本，优先使用这个 */
    rawMessage?: string;
}

export interface Choice {
    promptQuery: string;
    agent: string;
    slash?: string;
    knowledgeList: KnowledgeList[];
}

export interface RepoContext {
    description: string;
    type: string;
    suggestedQuery: KnowledgeTypeExampleRefer;
}

export interface ConfigItem {
    uuid: number;
    title: string;
    displayQuery: string;
    rule?: 'REPO'; // TODO 待补充其他规则类型
    reason?: string;
    type: string;
    primaryChoice?: Choice;
    alternativeChoice?: Choice;
    repoContext?: RepoContext;
}

export interface KnowledgeTypeExampleRefer {
    displayQuery: string;
    references: KnowledgeList[];
}

export interface KnowledgeTypeExampleItem {
    description: string;
    type: string;
    suggested_quires: KnowledgeTypeExampleRefer[];
}

// 智能API相关
export interface SearchAPIParamType {
    apiKeyword: string;
    num: number;
    byVector: boolean;
    username?: string;
}

export interface APIItem {
    id: string;
    title: string;
    projectName: string;
    url: string;
}

export interface TrackAPIParamType {
    type: string;
    content: string;
    username?: string;
}

export interface ActivityInfo {
    activityId: string;
    activityName: string;
    msgId: string;
    content: string;
    type: string;
    picUrl: string;
    position: string;
    responseType: string | null;
    isShow: boolean;
    isCheckIn: boolean;
    activityUrl: string;
}

// /**
//  * BannerVersionFetchReturn
//  * @param bannerShow 活动是否开启
//  * @param bannerVersion 上次活动的版本，存在本地，默认0，用户点击之后保存到本地，不在下次展示
//  * @param activityVersion 当前活动的版本
//  * @param bannerUrl 广告图片的url
//  * @param actionUri 点击跳转的url
//  */
// export interface BannerVersionFetchReturn {
//     bannerShow: boolean;
//     bannerVersion: number;
//     activityVersion: number;
//     bannerUrl: string;
//     actionUri: string;
// }

/**
 * 用户点击了banner
 * @param actionUri 点击跳转的url，如果开头是 //chat: 需要将内容塞到对话里。比如 //chat:xxxxx，则将xxxxx塞到对话里
 */
export interface BannerVersionClickParams {
    actionUri: string;
}

export interface UnityRequestType {
    method: 'GET' | 'POST' | 'PUT' | 'DELETE';
    url: string;
    useBaseUrl: boolean; // 是否需要baseUrl（拼接后端服务地址），目前vscode都传true即可，主要是提供给其他端
    body?: any;
    headers?: Record<string, string>;
    authorization?: boolean;
    options?: any;
    rawData?: boolean;
}

export interface UnityResponseDataType<T> {
    data: T;
    code: number;
    message: string;
}

export interface HttpUnityResponseSuccessType<T> {
    status: number;
    headers: Record<string, string>;
    data: UnityResponseDataType<T>;
}

export interface HttpUnityResponseRawSuccessType<T> {
    status: number;
    headers: Record<string, string>;
    data: T;
}

export type ResponseSuccessType<T> = HttpUnityResponseSuccessType<T> | HttpUnityResponseRawSuccessType<T>;

export type HttpUnityResponseType<T> =
    | HttpUnityResponseSuccessType<T>
    | {error: Error | string};
export const VPC_PLUGIN_CONFIG_LINK = '/zh/company/knowledge';

interface AcceptByReplace {
    type: 'replace';
    path: string;
    range: vscode.Range;
    newText: string;
    metadata?: vscode.WorkspaceEditEntryMetadata;
}

interface AcceptByInsert {
    type: 'insert';
    path: string;
    position: vscode.Position;
    newText: string;
    metadata?: vscode.WorkspaceEditEntryMetadata;
}

type AcceptByType = AcceptByReplace | AcceptByInsert;
export type Accept = AcceptByType[];

export interface ToastMessage {
    type: 'info' | 'success' | 'fail';
    message: string;
}

export enum WebviewAgentConversationType {
    /** 安全智能体 */
    SecuBotConversation = 'SecuBotConversation',
    /** 调试智能体 */
    DebugBotConversation = 'DebugBotConversation',
    /** 单测智能体 */
    TestBotConversation = 'TestBotConversation',
    /** 端到端测试智能体 */
    E2EBotConversation = 'E2EBotConversation',
    /** 续写智能体 */
    CompletionBotConversation = 'CompletionBotConversation',
    /** F2C智能体 */
    F2cBotConversation = 'F2cBotConversation',
}

export type WebviewAgentPayload<T extends object = any> = Omit<AgentPayload, 'conversationType' | 'payload'> & {
    conversationType: WebviewAgentConversationType;
    payload?: T;
};

export enum WebviewAgentConversationStatus {
    /** 创建但未发生内容变化 */
    Ready = 'Ready',
    /** 进行中 */
    Running = 'Running',
    /** 失败 */
    Failed = 'Failed',
    /** 完成 */
    Completed = 'Completed',
    /** 已取消 */
    Cancelled = 'Cancelled',
}

export interface AgentConversationInfo extends Omit<AgentConversationInfoProps, 'status' | 'type'> {
    status: WebviewAgentConversationStatus;
    type: WebviewAgentConversationType;
}

interface TestBotButton {
    style: 'error' | 'warning' | 'default';
    buttonText: string;
    clickMethodMessages: Array<{
        title: string;
        content: string;
    }>;
}
export interface TestBotBlockSection {
    absolutePath: string;
    status: WorkflowStatus;
    content: string | string[];
    extraBadge?: {
        text: string;
        style: 'warning' | 'error' | 'default';
    };
    buttons?: TestBotButton[];
}

export interface AgentConfig {
    /** 续写智能体 */
    enableCompletionIntelligence: boolean;
    /** 单测智能体（对话） */
    enableUTChatIntelligence: boolean;
    /** 单测智能体（编辑区） */
    enableUTEditorIntelligence: boolean;
    /** 修复智能体 */
    enableDebugIntelligence: boolean;
    /** 全栈智能体 */
    enableFullStackIntelligence: boolean;
    /** 安全智能体 */
    enableSecurityIntelligence: boolean;
}

export enum ApplyStatus {
    Applying = 'applying',
    Approved = 'approved',
    Rejected = 'rejected',
}

export interface AgentApplyStatus {
    completionIntelligenceApplyStatus: ApplyStatus | null;
    unitTestIntelligenceApplyStatus: ApplyStatus | null;
    debugIntelligenceApplyStatus: ApplyStatus | null;
    fullStackIntelligenceApplyStatus: ApplyStatus | null;
    securityIntelligenceApplyStatus: ApplyStatus | null;
}

export enum SmartApplyStatus {
    APPLING = 'APPLING',
    APPLIED = 'APPLIED',
    INQUIRE = 'INQUIRE',
    UNTOUCHED = 'UNTOUCHED',
}
export interface PromptTemplateList {
    uuid: string;
    name: string;
    description?: string;
    ts?: number;
    prompt?: string;
}

export interface CreatePromptTemplatePayload {
    query?: string;
}

export interface AgentConfigItem {
    id: number;
    scene: 'demo' | 'query';
    content: string;
    prompt: string;
    schema: string;
    displayNew: boolean;
    knowledgeList: any[];
    type?: string;
    figmaInfo?: any;
}

export interface InlineSuggestionModeItem {
    displayName: string;
    requestDelayMs: number;
}

export type InlineSuggestionModeType = 'fast' | 'balance' | 'accurate' | 'extremeAccurate' | 'extremeFast';

export type InlineSuggestionModeMap = Record<InlineSuggestionModeType, InlineSuggestionModeItem>;

export interface UnitTestConfig {
    java: {
        mockFrame: string;
        unitTestFrame: string;
    };
    go: {
        mockFrame: string;
        unitTestFrame: string;
    };
    javascript: {
        mockFrame: string;
        unitTestFrame: string;
    };
    cpp: {
        mockFrame: string;
        unitTestFrame: string;
    };
    python: {
        mockFrame: string;
        unitTestFrame: string;
    };
}

export type LinePreferMode = 'auto' | 'singleLine' | 'multiLine';

export interface UserConfig {
    langSuggestion: Record<string, boolean>;
    delayMilliSecondsMode: string;
    delayMilliSecondsModeMap: InlineSuggestionModeMap;
    enableInlineUnitTest: boolean;
    enableInlineCodeExplain: boolean;
    enableInlineCodeToComment: boolean;
    /** 是否允许 API 续写 */
    enableGenAPIInvokeCompletion: boolean;
    /** 是否展示函数的 添加日志 功能 CodeLense */
    enableInlineLog: boolean;
    enableSecurityEnhance: boolean;
    linePreferMode: LinePreferMode;
    enableCommentEnhancement: boolean;
    unitTestConfig?: UnitTestConfig;
    isInsider?: boolean;
    isPreferStream?: boolean;
    /** 开放平台和autowork的开关 */
    isEnableAutoworkAndPlus?: boolean;
    /** 是否开启基于代码库的对话增强 */
    enableCodebaseEnhancedContext?: boolean;
    // NOTE: 虽然叫 ChatPanel，但实际上作用是触发 InlineChat，考虑到和服务端的接口保持一致，暂时不做修改
    /** 是否开启 codelens 里开始对话的快捷按钮 */
    enableChatPanelShortcut?: boolean;
    displayLanguage?: string;
    /** 混合云用户服务地址 */
    customizedUrl?: string;
    /** 私有域服务地址 */
    privateService?: string;
    /** 智能粘贴功能开关 */
    enableAutoPaste?: boolean;
    /** 智能体功能开关 */
    enableIntelligenceAgent?: AgentConfig;
    /** 是否开启安全智能体 */
    enableSecurityIntelligence: boolean;
    /** 智能体申请状态 */
    applyStatus: AgentApplyStatus;
    /** 是否在编辑区生成函数注释 */
    functionDocstringOutputInEditor: boolean;
    /** 是否在编辑区生成行间注释 */
    inlineCommentOutputInEditor: boolean;
    /** 用户选择的模型版本 */
    modelId?: string;
    /** 是否开启续写智能体的诊断信息感知修复 */
    enableDiagnosticInfo: boolean;
}
