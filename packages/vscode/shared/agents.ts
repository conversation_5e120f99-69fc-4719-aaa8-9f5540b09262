import {
    FUNCTION_COMMENT,
    INLINE_COMMENT,
    CODE_EXPLAIN,
    OPTIMIZE,
    FUNCTION_SPLIT,
    UT,
    API_DOC,
} from '@shared/constants';
import {AgentListWithId, BuiltinAgent, CommandListWithId} from './protocols';

export type AgentInfo = AgentListWithId & {displayName: string} & {capabilities?: CommandListWithId[]};

// agent comate
export const AGENT_COMATE_DESCRIPTION = 'agent.comate.description';
export const AGENT_COMATE_COMMAND_DOCSTRING = 'agent.comate.command.docstring';
const AGENT_COMATE_COMMAND_API_DOC = 'agent.comate.command.apiDoc';
export const AGENT_COMATE_COMMAND_COMMENT = 'agent.comate.command.comment';
export const AGENT_COMATE_COMMAND_EXPLAIN = 'agent.comate.command.explain';
export const AGENT_COMATE_COMMAND_OPTIMIZE = 'agent.comate.command.optimize';
export const AGENT_COMATE_COMMAND_SPLIT = 'agent.comate.command.split';
export const AGENT_COMATE_COMMAND_CLEAR = 'agent.comate.command.clear';
export const AGENT_COMATE_COMMAND_UT = 'agent.comate.command.ut';

// agent autowork
export const AGENT_AUTOWORK_DESCRIPTION = 'agent.autowork.description';
export const AGENT_AUTOWORK_COMMAND_ASK = 'agent.autowork.command.ask';

// agent promptTemplate
export const AGENT_PROMPTTEMPLATE_DESCRIPTION = 'agent.promptTemplate.description';

// 指令
const FUNCTION_COMMENT_COMMAND = {
    id: FUNCTION_COMMENT + '@Comate', // 没法和代码内其他声明保持一致了，因为要暴露给用户，更明确一些好
    displayName: '函数注释',
    displayNameKey: AGENT_COMATE_COMMAND_DOCSTRING,
    name: FUNCTION_COMMENT,
    owner: {
        name: BuiltinAgent.Comate,
        displayName: 'Comate',
    },
    type: 'Skill',
};

export const API_DOC_COMMAND = {
    id: API_DOC + '@Comate',
    displayName: '接口文档',
    displayNameKey: AGENT_COMATE_COMMAND_API_DOC,
    name: API_DOC,
    owner: {
        name: BuiltinAgent.Comate,
        displayName: 'Comate',
    },
    type: 'Skill',
};

const INLINE_COMMENT_COMMAND = {
    id: INLINE_COMMENT + '@Comate', // 没法和代码内其他声明保持一致了，因为要暴露给用户，更明确一些好
    displayName: '行间注释',
    displayNameKey: AGENT_COMATE_COMMAND_COMMENT,
    name: INLINE_COMMENT,
    owner: {
        name: BuiltinAgent.Comate,
        displayName: 'Comate',
    },
    type: 'Skill',
};

const CODE_EXPLAIN_COMMAND = {
    id: CODE_EXPLAIN + '@Comate',
    displayName: '代码解释',
    displayNameKey: AGENT_COMATE_COMMAND_EXPLAIN,
    name: CODE_EXPLAIN,
    owner: {
        name: BuiltinAgent.Comate,
        displayName: 'Comate',
    },
    type: 'Skill',
};

const FUNCTION_SPLIT_COMMAND = {
    id: FUNCTION_SPLIT + '@Comate',
    displayName: '函数拆分',
    displayNameKey: AGENT_COMATE_COMMAND_SPLIT,
    name: FUNCTION_SPLIT,
    owner: {
        name: BuiltinAgent.Comate,
        displayName: 'Comate',
    },
    type: 'Skill',
};

const OPTIMIZE_COMMAND = {
    id: OPTIMIZE + '@Comate',
    displayName: '调优建议',
    displayNameKey: AGENT_COMATE_COMMAND_OPTIMIZE,
    name: OPTIMIZE,
    owner: {
        name: BuiltinAgent.Comate,
        displayName: 'Comate',
    },
    type: 'Skill',
};

export const UT_COMMAND = {
    id: UT + '@Comate',
    displayName: '生成单测',
    displayNameKey: AGENT_COMATE_COMMAND_UT,
    name: UT,
    owner: {
        name: BuiltinAgent.Comate,
        displayName: 'Comate',
    },
    type: 'Skill',
};

const CLEAR_COMMAND = {
    id: 'clear@Comate',
    displayName: '清空对话框',
    displayNameKey: AGENT_COMATE_COMMAND_CLEAR,
    name: 'clear',
    owner: {
        name: BuiltinAgent.Comate,
        displayName: 'Comate',
    },
    type: 'Skill',
};

const HELP_COMMAND = {
    id: 'help@Comate',
    displayName: 'help',
    name: 'help',
    owner: {
        name: BuiltinAgent.Comate,
        displayName: 'Comate',
    },
    type: 'Skill',
};

export const BUILTIN_COMMANDS: CommandListWithId[] = [
    FUNCTION_COMMENT_COMMAND,
    // API_DOC_COMMAND,
    INLINE_COMMENT_COMMAND,
    CODE_EXPLAIN_COMMAND,
    FUNCTION_SPLIT_COMMAND,
    OPTIMIZE_COMMAND,
    // 单测指令初始化不展示，等待engine启动后通知webview再展示
    // UT_COMMAND,
    CLEAR_COMMAND,
    HELP_COMMAND,
];

export const BUILTIN_AGENT: AgentInfo = {
    id: BuiltinAgent.Comate,
    displayName: 'Comate',
    name: 'Comate',
    description: '官方能力插件，支持函数注释、行间注释、代码解释',
    descriptionKey: AGENT_COMATE_DESCRIPTION,
    icon: '',
    capabilities: BUILTIN_COMMANDS,
};

export const E2EBOT_AGENT: AgentInfo = {
    id: BuiltinAgent.AutoWork,
    displayName: 'Zulu',
    name: 'Zulu',
    description: '实现完整编程任务',
    descriptionKey: AGENT_AUTOWORK_DESCRIPTION,
    icon: '',
    capabilities: [],
};

export const PROMPT_TEMPLATE_AGENT: AgentInfo = {
    id: BuiltinAgent.PromptTemplate,
    displayName: '自定义指令',
    name: 'PromptTemplate',
    description: '自定义Prompt',
    descriptionKey: AGENT_PROMPTTEMPLATE_DESCRIPTION,
    icon: '',
    capabilities: [],
};

export const COMATE_CONSOLE_AGENT: AgentInfo = {
    ...BUILTIN_AGENT,
    id: BuiltinAgent.ComateConsole,
    name: 'ComateConsole',
    capabilities: [],
};

export const QUICK_COMMANDS: CommandListWithId[] = [
    FUNCTION_COMMENT_COMMAND,
    INLINE_COMMENT_COMMAND,
    CODE_EXPLAIN_COMMAND,
    FUNCTION_SPLIT_COMMAND,
];
