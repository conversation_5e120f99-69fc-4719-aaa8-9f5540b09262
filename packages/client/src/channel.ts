import {
    ACTION_CHAT_QUERY,
    ACTION_SECUBOT,
    ACTION_SECUBOT_TASK_PROGRESS,
    SecubotQueryPayload,
    ChatQueryPayload,
    ACTION_CHAT_TASK_PROGRESS,
    TaskProgressChunk,
    Session,
    Channel,
    ChannelImplement,
    Channel<PERSON><PERSON>mentMaybe,
    ACTION_REQUEST_PERMISSION,
    LlmType,
    LlmResponseTypes,
    ACTION_ASK_LLM,
    LlmPayloadTypes,
    ACTION_GET_PLUGIN_CONFIG,
    ACTION_INFORMATION_QUERY,
    GetPluginConfigPayload,
    LlmPayload,
    PermissionType,
    RequestPermissionPayload,
    TaskProgressPayload,
    SessionInit,
    InformationPayload,
    ACTION_START_BACKGROUND_SERVICE,
    StartBackgroundServicePayload,
    ACTION_ASK_LLM_STREAMING,
    IdeSideLlmPayload,
    ACTION_CUSTOM_COMMAND,
    CustomCommandInvokePayload,
    ACTION_DIAGNOSTIC_SCAN_TASK_PROGRESS,
    ACTION_DIAGNOSTIC_SCAN,
    ACTION_DIAGNOSTIC_SCAN_TASK_COUNT,
    DiagnosticScanInvokePayload,
    DiagnosticScanTaskProgressChunk,
    DiagnosticScanTaskProgressPayload,
    DiagnosticScanCountPayload,
    ACTION_DEBUG_TASK_PROCESS,
    DebugAgentPayload,
    DebugAgentResponse,
    ACTION_ASK_RAG,
    ACTION_CODE_SEARCH,
    CodeSearchPayload,
    CodeChunk,
    ACTION_BATCH_ACCEPT,
    BatchAcceptPayload,
    ACTION_START_ISCAN,
    IScanInvokePayload,
    IScanResult,
    IScanResultPayload,
    ACTION_ISCAN_RESULT,
    ACTION_ISCAN_JOB_BUILD_ID,
    IScanJobBuildPayload,
    IScanBuildResult,
    ACTION_SA_SCAN_DIAGNOSTIC,
    SADiagnosticScanInvokePayload,
    SAScanDiagnosticResult,
    ACTION_SA_SCAN_DIAGNOSTIC_RESULT,
    SADiagnosticScanResultPayload,
} from '@comate/plugin-shared-internals';
import {CapabilityInvoker} from './invoker/index.js';

interface PayloadMap {
    [ACTION_CHAT_QUERY]: ChatQueryPayload;
    [ACTION_START_BACKGROUND_SERVICE]: StartBackgroundServicePayload;
    [ACTION_CUSTOM_COMMAND]: CustomCommandInvokePayload;
    [ACTION_DIAGNOSTIC_SCAN]: DiagnosticScanInvokePayload;
    [ACTION_SECUBOT]: SecubotQueryPayload;
    [ACTION_START_ISCAN]: IScanInvokePayload;
    [ACTION_SA_SCAN_DIAGNOSTIC]: SADiagnosticScanInvokePayload;
}

/**
 * 处理IDE与Engine之间的会话
 */
export class EngineSession extends Session<PayloadMap> {
    private readonly invoker: CapabilityInvoker;

    constructor(init: SessionInit, implement: ChannelImplement, invoker: CapabilityInvoker) {
        super(init, implement);
        this.invoker = invoker;
    }

    /**
     * 根据指定的片段，更新绘制用户界面
     *
     * @param messageId 对应的消息ID
     * @param chunk 绘制界面的片段信息
     */
    reportChatProgress(
        taskId: string,
        chunk: TaskProgressChunk,
        capabilityName?: string,
        messageId?: number,
        enableSmartApply?: boolean
    ) {
        const payload: TaskProgressPayload = {
            taskId,
            chunk,
            capabilityName,
            messageId,
            enableSmartApply,
        };

        void this.send({action: ACTION_CHAT_TASK_PROGRESS, payload});
    }

    /**
     * 报告诊断扫描结果
     * @param chunk
     * @param capabilityName
     * @returns
     */
    reportDiagnosticScanResult(chunk: DiagnosticScanTaskProgressChunk, capabilityName?: string) {
        const payload: DiagnosticScanTaskProgressPayload = {
            chunk,
            capabilityName,
        };

        void this.send({action: ACTION_DIAGNOSTIC_SCAN_TASK_PROGRESS, payload});
    }

    /**
     * 报告诊断扫描结果数量
     * @param chunk
     * @param capabilityName
     * @returns
     */
    reportDiagnosticScanCount(chunk: number, capabilityName?: string) {
        const payload: DiagnosticScanCountPayload = {
            chunk,
            capabilityName,
        };

        void this.send({action: ACTION_DIAGNOSTIC_SCAN_TASK_COUNT, payload});
    }

    /**
     * 报告安全智能体生成的内容
     * @param chunk
     * @param capabilityName
     * @returns
     */
    reportSecubot(chunk: string, capabilityName?: string) {
        const payload: any = {
            chunk,
            capabilityName,
        };
        void this.send({action: ACTION_SECUBOT_TASK_PROGRESS, payload});
    }

    /**
     * 报告代码扫描build结果
     * @param chunk
     * @param capabilityName
     * @returns
     */
    reportIScanJobBuildId(jobBuildResult: IScanBuildResult, capabilityName?: string) {
        const payload: IScanJobBuildPayload = {
            chunk: jobBuildResult,
            capabilityName,
        };

        void this.send({action: ACTION_ISCAN_JOB_BUILD_ID, payload});
    }

    /**
     * 报告代码扫描结果
     * @param chunk
     * @param capabilityName
     * @returns
     */
    reportIScanResult(result: IScanResult, capabilityName?: string) {
        const payload: IScanResultPayload = {
            chunk: result,
            capabilityName,
        };

        void this.send({action: ACTION_ISCAN_RESULT, payload});
    }

    /**
     * 报告SA波浪线扫描结果
     * @param chunk
     * @param capabilityName
     * @returns
     */
    reportSADiagnosticScanResult(result: SAScanDiagnosticResult | boolean, capabilityName?: string) {
        const payload: SADiagnosticScanResultPayload = {
            chunk: result,
            capabilityName,
        };

        void this.send({action: ACTION_SA_SCAN_DIAGNOSTIC_RESULT, payload});
    }

    async requestPermission(pluginName: string, type: PermissionType) {
        const payload: RequestPermissionPayload = {pluginName, type};
        const {granted} = await this.send({action: ACTION_REQUEST_PERMISSION, payload});
        return granted;
    }

    async askLlm<T extends LlmType>(type: T, input: LlmPayloadTypes[T]): Promise<LlmResponseTypes[T]> {
        // 类型是对的，但只有写`any`才能过
        const payload = {type, ...input} as LlmPayload;
        const {result} = await this.send({action: ACTION_ASK_LLM, payload});
        return result;
    }

    async *askLlmStreaming(input: LlmPayloadTypes[LlmType.Text]): AsyncIterable<string> {
        // 类型是对的，但只有写`any`才能过
        const payload = {type: LlmType.Text, ...input} as LlmPayload;
        const result = this.sendStreaming<IdeSideLlmPayload>({action: ACTION_ASK_LLM_STREAMING, payload});
        for await (const chunk of result) {
            // TODO: 类型还是不够健壮啊
            yield chunk.result as string;
        }
    }

    async *askDebugAgentProcess(
        payload: {pluginName: string, capabilityName?: string, debugAgentPayload: DebugAgentPayload}
    ): AsyncIterable<DebugAgentResponse> {
        // TODO: 类型还是不够健壮啊
        const result = this.sendStreaming<IdeSideLlmPayload>({action: ACTION_DEBUG_TASK_PROCESS, payload});
        for await (const chunk of result) {
            yield chunk.result as DebugAgentResponse;
        }
    }

    async askRAG(payload: {
        pluginName: string;
        capabilityName?: string;
        payload: {prompt: string, retrieveFrom?: {workspace?: boolean}};
    }) {
        const {result} = await this.send({action: ACTION_ASK_RAG, payload});
        return result.output as string;
    }

    async getCodeSearchResult(
        payload: {pluginName: string, capabilityName?: string, codeSearchPayload: CodeSearchPayload}
    ): Promise<CodeChunk[]> {
        const {result} = await this.send({action: ACTION_CODE_SEARCH, payload});
        return result;
    }

    batchAcceptRecord(
        payload: {pluginName: string, capabilityName?: string, batchAcceptPayload: BatchAcceptPayload}
    ) {
        void this.send({action: ACTION_BATCH_ACCEPT, payload});
    }

    async getPluginConfig(pluginName: string, key: string) {
        const payload: GetPluginConfigPayload = {pluginName, key};
        const {value} = await this.send({action: ACTION_GET_PLUGIN_CONFIG, payload});
        return value;
    }

    async informationQuery(payload: InformationPayload) {
        const {result} = await this.send({
            action: ACTION_INFORMATION_QUERY,
            payload,
        });
        return result;
    }

    protected initializeListeners() {
        super.initializeListeners();

        this.setListener(ACTION_CHAT_QUERY, payload => this.invoker.invokeChat(payload, this));
        this.setListener(ACTION_SECUBOT, payload => this.invoker.invokeSecubot(payload, this));
        this.setListener(ACTION_DIAGNOSTIC_SCAN, payload => this.invoker.invokeDiagnosticScan(payload, this));
        this.setListener(ACTION_CUSTOM_COMMAND, payload => this.invoker.invokeCommand(payload, this));
        this.setListener(ACTION_START_ISCAN, payload => this.invoker.invokeIScan(payload, this));
        this.setListener(ACTION_SA_SCAN_DIAGNOSTIC, payload => this.invoker.invokeSADiagnosticScan(payload, this));
        this.setListener(
            ACTION_START_BACKGROUND_SERVICE,
            payload => this.invoker.invokeBackgroundService(payload, this)
        );

    }
}

export class EngineChannel extends Channel<EngineSession> {
    private readonly invoker: CapabilityInvoker;

    constructor(implement: ChannelImplementMaybe, invoker: CapabilityInvoker) {
        super(implement);
        this.invoker = invoker;
    }

    protected createSession(init: SessionInit) {
        return new EngineSession(init, this.implement, this.invoker);
    }
}
