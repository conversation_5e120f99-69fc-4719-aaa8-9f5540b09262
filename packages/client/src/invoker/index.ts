import {
    ChatQueryPayload,
    StartBackgroundServicePayload,
    CustomCommandInvokePayload,
    DiagnosticScanInvokePayload,
    SecubotQueryPayload,
    IScanInvokePayload,
    SADiagnosticScanInvokePayload,
} from '@comate/plugin-shared-internals';
import {ClientRegistry} from '../registry.js';
import {EngineSession} from '../channel.js';
import {ChatInvoker} from './chat.js';
import {BackgroundServiceInvoker} from './background.js';
import {CommandInvoker} from './command.js';
import {DiagnosticScanInvoker} from './diagnosticScan.js';
import {SecubotInvokers} from './secubot.js';
import {IScanInvoker} from './iscan.js';
import {SADiagnosticScanInvoker} from './saDiagnosticScan.js';

export class CapabilityInvoker {
    private readonly chatInvokers = new Map<string, ChatInvoker>();
    private readonly backgroundServiceInvokers = new Map<string, BackgroundServiceInvoker>();
    private readonly diagnosticScanInvokers = new Map<string, DiagnosticScanInvoker>();
    private readonly commandInvokers = new Map<string, CommandInvoker>();
    private readonly secubotInvokers = new Map<string, SecubotInvokers>();
    private readonly iScanInvokers = new Map<string, IScanInvoker>();
    private readonly saDiagnosticScanInvokers = new Map<string, SADiagnosticScanInvoker>();

    attach(pluginName: string, registry: ClientRegistry) {
        this.chatInvokers.set(pluginName, new ChatInvoker(pluginName, registry));
        this.diagnosticScanInvokers.set(pluginName, new DiagnosticScanInvoker(pluginName, registry));
        this.backgroundServiceInvokers.set(pluginName, new BackgroundServiceInvoker(pluginName, registry));
        this.commandInvokers.set(pluginName, new CommandInvoker(pluginName, registry));
        this.secubotInvokers.set(pluginName, new SecubotInvokers(pluginName, registry));
        this.iScanInvokers.set(pluginName, new IScanInvoker(pluginName, registry));
        this.saDiagnosticScanInvokers.set(pluginName, new SADiagnosticScanInvoker(pluginName, registry));
    }

    detach(pluginName: string) {
        this.chatInvokers.delete(pluginName);
        this.diagnosticScanInvokers.delete(pluginName);
        this.backgroundServiceInvokers.delete(pluginName);
        this.commandInvokers.delete(pluginName);
        this.secubotInvokers.delete(pluginName);
        this.iScanInvokers.delete(pluginName);
        this.saDiagnosticScanInvokers.delete(pluginName);
    }

    async invokeChat(payload: ChatQueryPayload, session: EngineSession) {
        const invoker = this.chatInvokers.get(payload.pluginName);

        if (!invoker) {
            throw new Error(`No invoker for plugin ${payload.pluginName} found`);
        }

        await invoker.invoke(payload, session);
    }

    async invokeSecubot(payload: SecubotQueryPayload, session: EngineSession) {
        const invoker = this.secubotInvokers.get(payload.pluginName);
        if (!invoker) {
            throw new Error(`No invoker for plugin ${payload.pluginName} found`);
        }

        await invoker.invoke(payload, session);
    }

    async invokeDiagnosticScan(payload: DiagnosticScanInvokePayload, session: EngineSession) {
        const invoker = this.diagnosticScanInvokers.get(payload.pluginName);

        if (!invoker) {
            throw new Error(`No invoker for plugin ${payload.pluginName} found`);
        }

        await invoker.invoke(payload, session);
    }

    async invokeBackgroundService(payload: StartBackgroundServicePayload, session: EngineSession) {
        const invoker = this.backgroundServiceInvokers.get(payload.pluginName);

        if (!invoker) {
            throw new Error(`No invoker for plugin ${payload.pluginName} found`);
        }

        await invoker.invoke(payload, session);
    }

    async invokeCommand(payload: CustomCommandInvokePayload, session: EngineSession) {
        const invoker = this.commandInvokers.get(payload.pluginName);

        if (!invoker) {
            throw new Error(`No invoker for plugin ${payload.pluginName} found`);
        }

        await invoker.invoke(payload, session);
    }

    async invokeIScan(payload: IScanInvokePayload, session: EngineSession) {
        const invoker = this.iScanInvokers.get(payload.pluginName);

        if (!invoker) {
            throw new Error(`No invoker for plugin ${payload.pluginName} found`);
        }

        await invoker.invoke(payload, session);
    }

    async invokeSADiagnosticScan(payload: SADiagnosticScanInvokePayload, session: EngineSession) {
        const invoker = this.saDiagnosticScanInvokers.get(payload.pluginName);

        if (!invoker) {
            throw new Error(`No invoker for plugin ${payload.pluginName} found`);
        }

        await invoker.invoke(payload, session);
    }
}
