import {DiagnosticScanInvokePayload} from '@comate/plugin-shared-internals';
import {EngineSession} from '../channel.js';
import {isDiagnosticScanHandler} from '../handlers/diagnosticScan.js';
import {InvokeContext, InvokerBase} from './base.js';
import {ProviderCapability} from '../registry.js';

interface InvokeTarget {
    capability: ProviderCapability;
    payload: DiagnosticScanInvokePayload;
    session: EngineSession;
}

export class DiagnosticScanInvoker extends InvokerBase {
    async invoke(payload: DiagnosticScanInvokePayload, session: EngineSession): Promise<void> {
        const capability = this.registry.findCapabilityByName(payload.capabilityName);

        if (!capability) {
            return;
        }

        try {
            await this.invokeService({payload, session, capability});
        }
        catch (ex) {
            session.log(
                'error',
                this.constructor.name,
                'InvokeDiagnosticScanCapabilityError',
                {
                    capability: capability.name,
                    reason: ex instanceof Error ? ex.message : `${ex}`,
                }
            );
        }
    }

    private async invokeService({capability, session, payload}: InvokeTarget): Promise<void> {
        const context: InvokeContext = {
            session,
            capability,
            pluginName: payload.pluginName,
            systemInfo: payload.systemInfo,
            activationContext: payload.context!,
            informationList: [],
        };
        const provider = this.createProviderInstance(context);

        if (!isDiagnosticScanHandler(provider)) {
            session.log(
                'error',
                this.constructor.name,
                'InvokeDiagnosticScanCapabilityError',
                {
                    capability: capability.name,
                    reason: `Provider ${provider.constructor.name} must implement DiagnosticScan`,
                }
            );
            return;
        }

        try {
            if (payload.scanType === 'count') {
                const count = provider.getScanCount();
                session.reportDiagnosticScanCount(count, capability.name);
            }
            else {
                for await (const chunk of provider.scanDiagnostics(payload.scanType, payload.changedFiles)) {
                    session.reportDiagnosticScanResult(chunk, capability.name);
                }
            }
        }
        catch (ex) {
            session.log(
                'error',
                this.constructor.name,
                'InvokeDiagnosticScanCapabilityError',
                {
                    capability: capability.name,
                    reason: `Provider ${provider.constructor.name} must implement DiagnosticScan`,
                }
            );
        }
    }
}
