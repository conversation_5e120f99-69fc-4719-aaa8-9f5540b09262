import {EngineSession} from '../channel.js';
import {InvokeContext, InvokerBase} from './base.js';
import {ProviderCapability} from '../registry.js';
import {SecubotQueryPayload} from '@comate/plugin-shared-internals';

interface InvokeTarget {
    capability: ProviderCapability;
    payload: any;
    session: EngineSession;
}

export class SecubotInvokers extends InvokerBase {
    async invoke(payload: SecubotQueryPayload, session: EngineSession): Promise<void> {
        const capability = this.registry.findCapabilityByName(payload.capabilityName);

        if (!capability) {
            return;
        }

        try {
            await this.invokeService({payload, session, capability});
        }
        catch (ex) {
            session.log(
                'error',
                this.constructor.name,
                'InvokSecubotCapabilityError',
                {
                    capability: capability.name,
                    reason: ex instanceof Error ? ex.message : `${ex}`,
                }
            );
        }
        finally {
            session.finish();
        }
    }

    private async invokeService({capability, session, payload}: InvokeTarget): Promise<void> {
        const context: InvokeContext = {
            session,
            capability,
            pluginName: payload.pluginName,
            systemInfo: payload.systemInfo,
            activationContext: {
                query: payload.input.query,
                ...payload.context,
            },
            informationList: payload.input.informationList,
        };
        const provider = this.createProviderInstance(context) as any;

        try {
            for await (const chunk of provider.execute(payload.input.data)) {
                session.reportSecubot(chunk, capability.name);
            }
        }
        catch (ex) {
            session.log(
                'error',
                this.constructor.name,
                'InvokSecubotCapabilityError',
                {
                    capability: capability.name,
                    reason: `Provider ${provider.constructor.name} must implement execute method`,
                }
            );
        }
        finally {
            session.finish();
        }
    }
}
