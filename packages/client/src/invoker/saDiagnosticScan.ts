import {SADiagnosticScanInvokePayload} from '@comate/plugin-shared-internals';
import {EngineSession} from '../channel.js';
import {isSADiagnosticScanHandler} from '../handlers/saDiagnosticScan.js';
import {InvokeContext, InvokerBase} from './base.js';
import {ProviderCapability} from '../registry.js';

interface InvokeTarget {
    capability: ProviderCapability;
    payload: SADiagnosticScanInvokePayload;
    session: EngineSession;
}

export class SADiagnosticScanInvoker extends InvokerBase {
    async invoke(payload: SADiagnosticScanInvokePayload, session: EngineSession): Promise<void> {
        // 从注册表中查找指定名称的能力
        const capability = this.registry.findCapabilityByName(payload.capabilityName);

        if (!capability) {
            return;
        }

        try {
            await this.invokeService({payload, session, capability});
        }
        catch (ex) {
            session.log(
                'error',
                this.constructor.name,
                'InvokeSADiagnosticScanCapabilityError',
                {
                    capability: capability.name,
                    reason: ex instanceof Error ? ex.message : `${ex}`,
                }
            );
        }
    }

    private async invokeService({capability, session, payload}: InvokeTarget): Promise<void> {
        const context: InvokeContext = {
            session,
            capability,
            pluginName: payload.pluginName,
            systemInfo: payload.systemInfo,
            activationContext: payload.context!,
            informationList: [],
        };
        const provider = this.createProviderInstance(context);
        if (!isSADiagnosticScanHandler(provider)) {
            session.log(
                'error',
                this.constructor.name,
                'InvokeSADiagnosticScanCapabilityError',
                {
                    capability: capability.name,
                    reason: `Provider ${provider.constructor.name} must implement SADiagnosticScan`,
                }
            );
            return;
        }

        try {
            if (payload.scanType === 'init') {
                const result = await provider.init();
                session.reportSADiagnosticScanResult(result, capability.name);
            }
            else if (payload.scanType === 'getResult') {
                const result = await provider.getScanResult(payload.absolutePath);
                session.reportSADiagnosticScanResult(result, capability.name);
            }
            else if (payload.scanType === 'destroy') {
                provider.destroy();
            }

        }
        catch (ex) {
            session.log(
                'error',
                this.constructor.name,
                'InvokeSADiagnosticScanCapabilityError',
                {
                    capability: capability.name,
                    reason: (ex as Error).message,
                }
            );
        }
    }
}
