import {IScanInvokePayload} from '@comate/plugin-shared-internals';
import {EngineSession} from '../channel.js';
import {isIScanHandler} from '../handlers/iscan.js';
import {InvokeContext, InvokerBase} from './base.js';
import {ProviderCapability} from '../registry.js';

interface InvokeTarget {
    capability: ProviderCapability;
    payload: IScanInvokePayload;
    session: EngineSession;
}

export class IScanInvoker extends InvokerBase {
    async invoke(payload: IScanInvokePayload, session: EngineSession): Promise<void> {
        const capability = this.registry.findCapabilityByName(payload.capabilityName);

        if (!capability) {
            return;
        }

        try {
            await this.invokeService({payload, session, capability});
        }
        catch (ex) {
            session.log(
                'error',
                this.constructor.name,
                'InvokeIScanCapabilityError',
                {
                    capability: capability.name,
                    reason: ex instanceof Error ? ex.message : `${ex}`,
                }
            );
        }
    }

    private async invokeService({capability, session, payload}: InvokeTarget): Promise<void> {
        const context: InvokeContext = {
            session,
            capability,
            pluginName: payload.pluginName,
            systemInfo: payload.systemInfo,
            activationContext: payload.context!,
            informationList: [],
        };
        const provider = this.createProviderInstance(context);
        if (!isIScanHandler(provider)) {
            session.log(
                'error',
                this.constructor.name,
                'InvokeIScanCapabilityError',
                {
                    capability: capability.name,
                    reason: `Provider ${provider.constructor.name} must implement IScan`,
                }
            );
            return;
        }

        try {
            if (payload.scanType === 'init') {
                const scanResult = await provider.startScan();
                session.reportIScanJobBuildId(scanResult, capability.name);
            }
            else {
                const result = await provider.getJobBuildResult(payload.jobBuildId);
                session.reportIScanResult(result, capability.name);
            }
        }
        catch (ex) {
            session.log(
                'error',
                this.constructor.name,
                'InvokeIScanCapabilityError',
                {
                    capability: capability.name,
                    reason: (ex as Error).message,
                }
            );
        }
    }
}
