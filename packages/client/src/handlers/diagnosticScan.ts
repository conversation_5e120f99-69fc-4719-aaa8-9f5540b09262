import {
    DiagnosticScanChangedFiles,
    DiagnosticScanTaskProgressChunk,
    DiagnosticScanTypes,
} from '@comate/plugin-shared-internals';

export interface DiagnosticScanHandler {
    /**
     * 处理扫描任务并返回对代码的更新补丁
     *
     * @returns 返回的补丁将会被选择性应用到代码中
     */
    scanDiagnostics(
        type: DiagnosticScanTypes,
        changedFiles?: DiagnosticScanChangedFiles
    ): AsyncIterableIterator<DiagnosticScanTaskProgressChunk>;
    getScanCount(): number;
}

export function isDiagnosticScanHandler(value: any): value is DiagnosticScanHandler {
    return typeof value.scanDiagnostics === 'function' && typeof value.getScanCount === 'function';
}
