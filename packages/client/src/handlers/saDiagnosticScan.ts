import {SAScanDiagnosticResult} from '@comate/plugin-shared-internals';

export interface SADiagnosticScanHandler {
    /**
     * 获取文件对应的扫描结果
     */
    getScanResult(absolutePath?: string): Promise<SAScanDiagnosticResult>;
    /**
     * 初始化拉起扫描server，返回是否成功
     */
    init(): Promise<boolean>;
    /**
     * 销毁扫描进程
     */
    destroy(): void;
}

export function isSADiagnosticScanHandler(value: any): value is SADiagnosticScanHandler {
    return typeof value.getScanResult === 'function';
}
