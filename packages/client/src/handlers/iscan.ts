import {IScanResult, IScanBuildResult} from '@comate/plugin-shared-internals';

export interface IScanHandler {
    /**
     * 开启代码扫描
     */
    startScan(): Promise<IScanBuildResult>;
    /**
     * 获取当前正在运行的扫描结果
     */
    getJobBuildResult(jobBuildId?: string): Promise<IScanResult>;
    getJobBuildId(): string;
}

export function isIScanHandler(value: any): value is IScanHandler {
    return typeof value.startScan === 'function' && typeof value.getJobBuildResult === 'function';
}
