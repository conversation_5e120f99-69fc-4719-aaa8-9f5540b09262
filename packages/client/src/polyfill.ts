// @ts-ignore ESM ignore
import nodeFetch from 'node-fetch';
// @ts-ignore ESM ignore
import {Blob} from 'fetch-blob';

// 适配node16
if (!AbortSignal.timeout) {
    AbortSignal.timeout = function timeout(ms: number) {
        const controller = new AbortController();
        const timer = setTimeout(() => controller.abort(), ms);
        controller.signal.addEventListener('abort', () => clearTimeout(timer));
        return controller.signal;
    };
}

if (!globalThis.fetch) {
    globalThis.fetch = nodeFetch as any;
}

if (!globalThis.Blob) {
    globalThis.Blob = Blob;
}

