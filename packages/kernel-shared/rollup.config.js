import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import terser from '@rollup/plugin-terser';
import json from '@rollup/plugin-json';
import alias from '@rollup/plugin-alias';
import path from 'path';
import {fileURLToPath} from 'url';
import {dts} from 'rollup-plugin-dts';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const config = [{
    context: 'this',
    input: 'dist/index.js',
    output: {
        file: 'dist/bundle/index.js',
        format: 'es',
        sourcemap: process.env.PLATFORM === 'internal',
    },
    plugins: [
        alias({
            entries: [
                { find: '@kernel-shared', replacement: path.resolve(__dirname, 'dist') }
            ]
        }),
        commonjs({
            dynamicRequireTargets: [
                'node_modules/inversify/es/syntax/binding_{on,when}_syntax.js',
            ],
        }),
        json(),
        resolve({
            preferBuiltins: true,
        }),
        // terser(),
    ],
    external: [
        '@comate/kernel',
        '@comate/plugin-shared-internals',
        'axios',
        'dayjs',
        'fs-extra',
        'inversify',
        'isomorphic-git',
        'lowdb',
        'reflect-metadata',
        'safe-stable-stringify',
        'vscode-languageserver',
        'vscode-languageserver-textdocument',
        'vscode-uri'
    ]
}, {
    input: 'dist/index.d.ts',
    output: {
        file: 'dist/bundle/index.d.ts',
        format: 'es'
    },
    plugins: [dts()]
}, {
    input: 'dist/types/index.js',
    output: {
        file: 'dist/types/bundle/index.js',
        format: 'es',
        sourcemap: process.env.PLATFORM === 'internal',
    },
    plugins: [
        alias({
            entries: [
                { find: '@kernel-shared', replacement: path.resolve(__dirname, 'dist') }
            ]
        }),
        commonjs(),
        json(),
        resolve({
            preferBuiltins: true,
        }),
    ],
    external: [
        '@comate/kernel',
        '@comate/plugin-shared-internals'
    ]
}, {
    input: 'dist/types/index.d.ts',
    output: {
        file: 'dist/types/bundle/index.d.ts',
        format: 'es'
    },
    plugins: [dts()]
}, {
    input: 'dist/browser/index.js',
    output: {
        file: 'dist/browser/bundle/index.js',
        format: 'es',
        sourcemap: process.env.PLATFORM === 'internal',
    },
    plugins: [
        alias({
            entries: [
                { find: '@kernel-shared', replacement: path.resolve(__dirname, 'dist') }
            ]
        }),
        commonjs(),
        json(),
        resolve({
            preferBuiltins: true,
        }),
    ],
    external: [
        '@comate/kernel',
        '@comate/plugin-shared-internals'
    ]
}, {
    input: 'dist/browser/index.d.ts',
    output: {
        file: 'dist/browser/bundle/index.d.ts',
        format: 'es'
    },
    plugins: [dts()]
}];

export default config;