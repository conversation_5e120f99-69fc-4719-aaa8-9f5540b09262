import 'reflect-metadata';
import fs from 'node:fs/promises';
import * as vscodeUri from 'vscode-uri';
import * as stacktraceParser from 'stacktrace-parser';
import {initConnection} from './createConnection.js';
import {KernelService} from './service/KernelService.js';
import {IKernel} from './types/config.js';
import {iocContainer} from './context.js';
import {ConfigService} from './service/ConfigService.js';
import {HttpService} from './service/HttpService.js';
import {ConnectService} from './service/ConnectService.js';
import {StorageService} from './service/StorageService/index.js';
import {GitService} from './service/GitService.js';
import {LoggerService} from './service/LoggerService.js';
import {activate} from './activate.js';
import {
    ConfigServiceName,
    HttpServiceName,
    ConnectServiceName,
    StorageServiceName,
    GitServiceName,
    LoggerServiceName,
    KernelServiceName,
} from './service/serviceName.js';
import {init as weirwoodInit} from '@baidu/weirwood-comate-sdk/lib/bundle.node.esm.js';
import weirwoodJson from '../../weirwood.json' with {type: 'json'};
const {reportException} = weirwoodInit({
    common: {
        buildid: weirwoodJson.buildid,
        token: weirwoodJson.token,
        sampleRate: 0.5,
    },
});
interface ErrorParams {
    page?: string;
    error: Error;
}

const uploadError = (params: ErrorParams) => {
    const errorStack = stacktraceParser.parse(params?.error?.stack || '');
    for (const frame of errorStack) {
        if (frame?.file?.includes('comate')) {
            try {
                reportException({
                    page: params.page || '',
                    env: {},
                    exception: {
                        type: params.error.name,
                        value: params.error.message,
                    },
                    errorInstance: params.error,
                });
                return;
            } catch (err) {}
        }
    }
    return;
};
export {uploadError};

export type {ConfigService} from './service/ConfigService.js';
export type {ConnectService} from './service/ConnectService.js';
export type {GitService} from './service/GitService.js';
export type {HttpService} from './service/HttpService.js';
export type {KernelService} from './service/KernelService.js';
export type {LoggerService} from './service/LoggerService.js';
export type {StorageService} from './service/StorageService/index.js';
export type {UserService} from './service/UserService.js';

export * from './events.js';
export * from './types/index.js';
export * from './types/public.js';
export * from './lib/index.js';
export {AxiosStatic} from 'axios';

activate();
const configService = iocContainer.get<ConfigService>(ConfigServiceName);
const httpService = iocContainer.get<HttpService>(HttpServiceName);
const connectService = iocContainer.get<ConnectService>(ConnectServiceName);
const storageService = iocContainer.get<StorageService>(StorageServiceName);
const gitService = iocContainer.get<GitService>(GitServiceName);
const loggerService = iocContainer.get<LoggerService>(LoggerServiceName);
export const kernelService = iocContainer.get<KernelService>(KernelServiceName);

export let kernel = {
    connect: {
        initConnection: () => initConnection(kernelService),
    },
    logger: loggerService.logger,
    uri: vscodeUri.URI,
    fs: fs,
} as IKernel;

export let env = {} as IKernel['env'];
export let config = {} as IKernel['config'];
export let git = {} as IKernel['git'];
export let uri = vscodeUri.URI;
export let connect = {} as IKernel['connect'];
export let http = {} as IKernel['http'];
export let logger = {} as IKernel['logger'];
export let storage = {} as IKernel['storage'];
kernelService.onInitialized(() => {
    env = {
        get appRoot() {
            return configService.appRoot;
        },
        get appHost() {
            return configService.appHost;
        },
        get language() {
            return configService.language;
        },
        get deviceId() {
            return configService.deviceId;
        },
        get ideName() {
            return configService.ideName;
        },
        get ideSeries() {
            return configService.ideSeries;
        },
        get ideVersion() {
            return configService.ideVersion;
        },
        get extensionVersion() {
            return configService.extensionVersion;
        },
        get environment() {
            return configService.environment;
        },
        get platform() {
            return configService.platform;
        },
        get themeColor() {
            return configService.themeColor;
        },
        get httpHost() {
            return configService.httpHost;
        },
        get serverEndpointPrefix() {
            return configService.serverEndpointPrefix;
        },
        get customized() {
            return configService.customized;
        },
        get customizedUrl() {
            return configService.customizedUrl;
        },
        get isSaaS() {
            return configService.isSaaS;
        },
        get isInternal() {
            return configService.isInternal;
        },
        get isPoc() {
            return configService.isPoc;
        },
        get isVSCode() {
            return configService.isVSCode;
        },
        get ideTerminalInfo() {
            return configService.ideTerminalInfo;
        },
        /** @deprecated 不该从这个namespace读取*/
        get workspaceInfo() {
            return configService.workspaceInfo;
        },
    };
    config = {
        get key() {
            return configService.key;
        },
        get license() {
            return configService.license;
        },
        get username() {
            return configService.username;
        },
        get zuluConfig() {
            return configService.zuluConfig;
        },
    };
    git = gitService;
    connect = {
        initConnection: () => initConnection(kernelService),
        onRequest: connectService.onRequest as any,
        sendRequest: connectService.sendRequest as any,
        sendNotification: connectService.sendNotification as any,
        onWebviewMessage: connectService.onWebviewMessage as any,
        onWebviewStreamMessage: connectService.onWebviewStreamMessage as any,
        sendWebviewMessage: connectService.sendWebviewMessage as any,
        onNotification: connectService.onNotification as any,
        // workspace: connectService.workspace,
        webviewInitialized: connectService.webviewInitialized,
    };
    http = httpService;
    logger = loggerService.logger;
    storage = storageService;
    kernel = {
        env,
        config,
        git,
        uri,
        connect,
        http,
        logger,
        storage,
        fs: fs,
    };
});
