// 初始化相关事件
export const KERNEL_CONFIG_FETCH_REQUEST = 'kernel/kernelConfig/fetch';
export const KERNEL_EXTENSION_CONFIG_FETCH_REQUEST = 'kernel/extensionConfig/fetch';
export const KERNEL_PASSTHROUGH_MESSAGE_TO_IDE_REQUEST = 'kernel/passthroughMessage/toIDE';
export const IDE_PASSTHROUGH_MESSAGE_TO_KERNEL_REQUEST = 'ide/passthroughMessage/toKernel';
export const IDE_CONFIG_DID_CHANGE_REQUEST = 'ide/kernelConfig/didChange';
export const IDE_EXTENSION_CONFIG_DID_CHANGE_REQUEST = 'ide/extensionConfig/didChange';
export const KERNEL_SYSTEM_INITIALIZED_NOTIFICATION = 'kernel/system/initialized';

// 文件上传，创建向量
export const KERNEL_GRAPH_CODEINDEXREADY_EVENT = 'kernel/graph/codeIndexReady';
export const KERNEL_GRAPH_CODEINDEXCANCEL_EVENT = 'kernel/graph/codeIndexCancel';
export const IDE_GRAPH_CODEINDEXCOMPLETED_EVENT = 'ide/graph/codeIndexCompleted';
export const IDE_GRAPH_INDEXREADY_EVENT = 'ide/graph/indexReady';

// Webview与ide 通信，Jetbrains有些限制，无法用 / 分割，用下划线替换
export const WEBVIEW_PASSTHROUGH_MESSAGE_TO_IDE_REQUEST = 'webview_passthroughMessage_toIDE';
export const IDE_PASSTHROUGH_MESSAGE_TO_WEBVIEW_REQUEST = 'ide_passthroughMessage_toWebview';

// passthrough 内部事件
export const PT_KERNEL_SYSTEM_INITIALIZED_REQUEST = 'pt/kernel/system/initialized';
export const PT_WEBVIEW_SYSTEM_INITIALIZED_REQUEST = 'pt/webview/system/initialized';
export const PT_WEBVIEW_DEMO_REQUEST = 'pt/webview/passthroughDemo/send';
export const PT_WEBVIEW_STREAM_DEMO_REQUEST = 'pt/webview/passthroughDemo/streamSend';
export const PT_KERNEL_DEMO_REQUEST = 'pt/kernel/passthroughDemo/ping';

// mcp 相关事件
export const PT_KERNEL_MCP_CONFIG_UPDATED = 'pt/kernel/mcp/configUpdated'; // 配置通知webview
export const PT_KERNEL_MCP_CONNECTION_LIST_DID_CHANGE = 'pt/kernel/mcp/connectListDidChange'; // 连接列表信息更新通知webview
export const PT_WEBVIEW_MCP_CONNECTION_LIST_RELOAD = 'pt/webview/mcp/connectListReload'; // webview reload MCP Servers
export const PT_WEBVIEW_MCP_CONNECTION_LIST_FETCH = 'pt/webview/mcp/connectListFecth'; // webview fetch MCP Servers
export const PT_WEBVIEW_MCP_CONNECTION_CREATE_CONFIG = 'pt/webview/mcp/createConfig'; // webview 创建连接列表
export const KERNEL_MCP_OUTPUT_CHANNEL_CREATE = 'kernel/mcp/outputChannelCreate'; // vscode 创建新的 outputChannel 展示 MCP 的错误信息
export const KERNEL_MCP_OUTPUT_CHANNEL_MESSAGE = 'kernel/mcp/outputChannelMessage'; // MCP 对应的 outputChannel 接收到信息

export const PT_WEBVIEW_AGENT_CREATE_FIGMA_RULES = 'pt/webview/agent/createFigmaRules'; // F2C 创建rules
export const PT_WEBVIEW_MCP_CONFIG_FETCH = 'pt/webview/mcp/configFetch'; // webview 获取 MCP 配置
export const PT_WEBVIEW_MCP_CONFIG_UPDATE = 'pt/webview/mcp/configUpdate'; // webview 删除 MCP 配置
