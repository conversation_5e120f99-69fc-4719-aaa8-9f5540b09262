import {WorkspaceFolder} from 'vscode-languageserver';
import fs from 'node:fs/promises';
import * as vscodeUri from 'vscode-uri';
import git from 'isomorphic-git';
import type {HttpService} from '../service/HttpService.js';
import type {StorageService} from '../service/StorageService/index.js';
import type {KernelService} from '../service/KernelService.js';
import type {ConnectService} from '../service/ConnectService.js';
import type {GitService} from '../service/GitService.js';
import {LoggerService} from '../service/LoggerService.js';
export interface ZuluConfig {
    terminalAutoExecution: string;
    terminalAutoExecutionDenyList: string[];
    terminalAutoExecutionAllowList: string[];
}
export type Env =
    & KernelConfig
    & EnvCondition
    & {workspaceInfo: WorkspaceInfo, ideTerminalInfo?: IdeTerminalInfo};
export type Config =
    & Pick<ExtensionConfig, 'license' | 'username'>
    & {key?: string}
    & {zuluConfig?: ZuluConfig};
export type Git = GitService;
export type Uri = typeof vscodeUri.URI;
export interface Connect {
    initConnection: () => KernelService;
    onRequest: typeof ConnectService.prototype.onRequest;
    onNotification: typeof ConnectService.prototype.onNotification;
    sendRequest: typeof ConnectService.prototype.sendRequest;
    sendNotification: typeof ConnectService.prototype.sendNotification;
    sendWebviewMessage: typeof ConnectService.prototype.sendWebviewMessage;
    onWebviewMessage: typeof ConnectService.prototype.onWebviewMessage;
    onWebviewStreamMessage: typeof ConnectService.prototype.onWebviewStreamMessage;
    webviewInitialized: typeof ConnectService.prototype.webviewInitialized;
    // workspace: RemoteWorkspace;
}

/**
 * 为了兼容vs，只支持一个工作区，不提供workspaceFolders参数
 */
export interface WorkspaceInfo {
    workspaceFolders: WorkspaceFolder[] | null | undefined;
    rootUri: string | undefined;
    rootPath: string;
}

export interface IdeTerminalInfo {
    defaultShell: string;
}

export interface IKernel {
    fs: typeof fs;
    env: Env;
    git: Git;
    config: Config;
    uri: Uri;
    connect: Connect;
    http: HttpService;
    logger: typeof LoggerService.prototype.logger;
    storage: StorageService;
}

export interface EnvCondition {
    isSaaS: boolean;
    isInternal: boolean;
    isPoc: boolean;
    isVSCode: boolean;
}

export enum ENVIRONMENT {
    DEVELOPMENT = 'development',
    PRODUCTION = 'production',
    TEST = 'test',
}
export enum PLATFORM {
    SAAS = 'saas',
    INTERNAL = 'internal',
    POC = 'poc',
}
export type DeviceId = string;
export enum IDE_NAME {
    VSCode = 'vscode',
    JetBrains = 'jetbrains',
    VS = 'vs',
    Xcode = 'xcode',
    Eclipse = 'eclipse',
    /** 官网对话用的 */
    Web = 'web',
}
export type IdeVersion = string;

// TODO 要给企业单独一个字段，在poc下的一级，参考gitee
export interface KernelConfig {
    /** IDE 应用本身所在的目录 */
    appRoot: string;
    /** IDE 模式 */
    appHost: 'desktop' | 'web';
    /** IDE 使用的语言 IETF 标准 */
    language: string;
    /** 设备 ID */
    deviceId: DeviceId;
    /** IDE 名称 */
    ideName: IDE_NAME; // jetbrains vscode
    /** IDE 系列 */
    ideSeries: string; // jetbrains-android 之类的
    /** IDE 版本 */
    ideVersion: IdeVersion;
    /** 当前comate版本 */
    extensionVersion: string;
    /** 执行环境 */
    environment: ENVIRONMENT;
    /** 厂内外等版本标识，同 platform */
    platform: PLATFORM;
    /** 主题色 */
    themeColor: 'light' | 'dark';
    /** 服务接口地址 */
    httpHost?: string;
    /** 代理服务 */
    proxyUrl?: string;
    /** IDE 的 Terminal 信息 */
    ideTerminalInfo?: IdeTerminalInfo;
    /** @deprecated */
    serverEndpointPrefix?: string;
    /** @deprecated */
    customized?: string;
    /** @deprecated */
    customizedUrl?: string;
}

// TODO 需要区分不同的ide
export interface ExtensionConfig {
    /** 用户名称 */
    username?: string;
    /** 证书 */
    license?: string;
    /** 代理服务器地址 */
    privateService?: string;
    inlineSuggestionMode: 'extremeFast' | 'fast' | 'balance' | 'accurate' | 'extremeAccurate';
    linePreferMode: 'auto' | 'singleLine' | 'multiLine';
    codelensDisplayMode: 'expand' | 'collapse';
    enableCodelens: {
        enableInlineUnitTest: boolean;
        enableInlineExplain: boolean;
        enableInlineDocstring: boolean;
        enableInlineSplit: boolean;
        enableInlineComment: boolean;
        enableInlineOptimize: boolean;
    };
    enableSecurityEnhancement: boolean;
    langSuggestion: {
        [key: string]: boolean;
    };
    zuluConfig?: ZuluConfig;
}

export const KERNEL_KEYS = {
    APP_ROOT: 'appRoot',
    APP_HOST: 'appHost',
    LANGUAGE: 'language',
    DEVICE_ID: 'deviceId',
    IDE_NAME: 'ideName',
    IDE_VERSION: 'ideVersion',
    ENVIRONMENT: 'environment',
    PLATFORM: 'platform',
    THEME_COLOR: 'themeColor',
    HTTP_HOST: 'httpHost',
    HTTP_PROXY: 'httpProxy',
};

export const EXTENSION_KEYS = {
    LICENSE: 'license',
    USER_NAME: 'userName',
    ENABLE_SECURITY_ENHANCEMENT: 'enableSecurityEnhancement',
    LANG_SUGGESTION: 'langSuggestion',
    PRIVATE_SERVICE: 'privateService',
};
