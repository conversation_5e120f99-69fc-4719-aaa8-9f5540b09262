import {iocContainer} from '../context.js';
import {ConfigService} from './ConfigService.js';
import {ConnectService} from './ConnectService.js';
import {Connection, InitializeParams} from 'vscode-languageserver';
import {HttpService} from './HttpService.js';
import {UserService} from './UserService.js';
import {inject, injectable} from 'inversify';
import {LoggerService} from './LoggerService.js';
import {KERNEL_SYSTEM_INITIALIZED_NOTIFICATION, PT_KERNEL_SYSTEM_INITIALIZED_REQUEST} from '../events.js';
import {
    ConfigServiceName,
    ConnectServiceName,
    HttpServiceName,
    LoggerServiceName,
    UserServiceName,
} from './serviceName.js';
import {LoggerUploaderCategoryType, uploadError, PTMessageClient, PTMessageServer} from '../index.js';
import EventEmitter from 'node:events';

// process.stderr.on('data', data => {
//     logStream.write(data);
// });

// process.on('exit', code => {
//     logStream.write(`ENGINE IDE CHANNEL exit ${code}\n`);
// });

// process.on('beforeExit', code => {
//     logStream.write(`ENGINE IDE CHANNEL  beforeExit ${code}\n`);
// });

process.on('uncaughtException', error => {
    // logStream.write('ENGINE IDE CHANNEL UnCaught exception: ' + err + '\n');
    process.env.NODE_ENV !== 'development' && process.env.PLATFORM === 'internal' && uploadError({
        page: 'kernelService',
        error: error,
    });
    process.exit(0);
});

process.on('unhandledRejection', (reason, promise) => {
    // nothing to do
    // logStream.write('ENGINE IDE CHANNEL 未处理的Promise拒绝:\n');
    const newError = reason instanceof Error ? reason : new Error(String(reason));
    process.env.NODE_ENV !== 'development' && process.env.PLATFORM === 'internal' && uploadError({
        page: 'kernelService',
        error: newError,
    });
});

function isOrphanProcess() {
    const ppid = process.ppid;
    return ppid === 1 || ppid === 0;
}

function handleOrphanProcess() {
    if (isOrphanProcess()) {
        process.exit(0);
    }
}

// 定期检查是否变成孤儿进程
setInterval(handleOrphanProcess, 5000);

@injectable()
export class KernelService {
    private connectService!: ConnectService;
    private lspEmitter: EventEmitter = new EventEmitter();
    private kernelEmitter: EventEmitter = new EventEmitter();
    private isInitialized: boolean = false;

    private static readonly INIT_EVENT = 'initialized';
    private static readonly LSP_INIT_EVENT = 'lspInitialized';
    constructor(@inject(LoggerServiceName) private readonly loggerService: LoggerService) {}

    private async serviceInitialized() {
        this.loggerService.logger.info('Kernel all server initialized 🚀');
        this.connectService.sendNotification(KERNEL_SYSTEM_INITIALIZED_NOTIFICATION);
        this.isInitialized = true;
        this.kernelEmitter.emit(KernelService.INIT_EVENT);
        try {
            this.connectService.sendWebviewMessage(PT_KERNEL_SYSTEM_INITIALIZED_REQUEST, {
                time: new Date(),
            });
        }
        catch {
            // 可能webview没有初始化，发送消息失败
        }
    }

    setInitializeParams(params: InitializeParams) {
        const configService = iocContainer.get<ConfigService>(ConfigServiceName);
        configService.setInitializeParams(params);
    }
    async LSPInitialized() {
        this.loggerService.logger.info('Kernel LSP initialized 🤝');
        this.lspEmitter.emit(KernelService.LSP_INIT_EVENT);
        await iocContainer.get<ConfigService>(ConfigServiceName).init();
        this.loggerService.uploader({
            category: LoggerUploaderCategoryType.kernelInnerInit,
            content: 'IDE channel initialized',
        });
        // TODO 这里有个问题，user可能还没有呢
        await iocContainer.get<HttpService>(HttpServiceName).init();
        // 这里没有await，业务第一个请求运气好可能有结果，没有结果当场拉结果
        iocContainer.get<UserService>(UserServiceName).init();

        this.serviceInitialized();
    }
    // lifecycle 所有的 lifecycle 都返回 this，方便链式调用
    init(connection: Connection) {
        this.connectService = iocContainer.get<ConnectService>(ConnectServiceName).init(connection);
        return this;
    }

    /**
     * @param callback 回调函数
     * @returns void
     * @description LSP 初始化完成后的回调，注意，此时只有LSP初始化完成，其他所有服务都为完成初始化，能是有的API有限制
     * @description 生命周期的顺序：onLSPInitialized -> onInitialized
     */
    onLSPInitialized(callback: (data?: any) => void) {
        this.lspEmitter.on(KernelService.LSP_INIT_EVENT, callback);
        return {
            dispose: () => {
                this.lspEmitter.removeListener(KernelService.INIT_EVENT, callback);
            },
        };
    }

    /**
     * @param callback 回调函数
     * @description Kernel初始化完成后的回调
     * @description 生命周期的顺序：onLSPInitialized -> onInitialized
     */
    onInitialized(callback: (data?: any) => void) {
        if (this.isInitialized) {
            callback();
            return {
                dispose: () => {},
            };
        }

        this.kernelEmitter.on(KernelService.INIT_EVENT, callback);
        return {
            dispose: () => {
                this.kernelEmitter.removeListener(KernelService.INIT_EVENT, callback);
            },
        };
    }

    dispose() {
        this.lspEmitter.removeAllListeners();
        this.kernelEmitter.removeAllListeners();
    }
}
