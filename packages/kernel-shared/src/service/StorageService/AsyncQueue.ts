export class AsyncQueue {
    private queue: (() => Promise<any>)[] = [];
    private isProcessing = false;

    public enqueue(task: () => Promise<any>): Promise<any> {
        return new Promise((resolve, reject) => {
            const wrappedTask = async () => {
                try {
                    const res = await task();
                    resolve(res);
                }
                catch (error) {
                    reject(error);
                }
                finally {
                    this.dequeue();
                }
            };
            this.queue.push(wrappedTask);
            if (!this.isProcessing) {
                this.dequeue();
            }
        });
    }

    private dequeue(): void {
        if (this.queue.length === 0) {
            this.isProcessing = false;
            return;
        }

        this.isProcessing = true;
        const task = this.queue.shift();
        if (task) {
            task().catch(error => {
                // console.error('An error occurred while processing the queue:', error);
            });
        }
    }
}
