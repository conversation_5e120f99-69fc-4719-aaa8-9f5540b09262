import fs from 'node:fs';
import path from 'node:path';
import os from 'node:os';
import {Connection} from 'vscode-languageserver';
import {inject, injectable} from 'inversify';
import type {
    OnRequestTypes,
    PassthroughMessageParams,
    PassthroughMessageResponse,
    PTEventTypes,
    SendRequestTypes,
} from '../types/index.js';
import {LoggerUploaderCategoryType} from '../types/index.js';
import {LoggerService} from './LoggerService.js';
import {LoggerServiceName} from './serviceName.js';
import {
    IDE_PASSTHROUGH_MESSAGE_TO_KERNEL_REQUEST,
    KERNEL_PASSTHROUGH_MESSAGE_TO_IDE_REQUEST,
    PT_WEBVIEW_SYSTEM_INITIALIZED_REQUEST,
} from '../events.js';
import {PTMessageClient, PTMessageServer} from '../lib/index.js';

const timer = {
    setTimeout: global.setTimeout.bind(global),
    clearTimeout: global.clearTimeout.bind(global),
};

@injectable()
export class ConnectService {
    private connect!: Connection;
    private ptServer!: PTMessageServer;
    private ptClient!: PTMessageClient;
    private isWebviewInitialized: boolean = false;
    private webviewInitPromise: Promise<void>;
    private webviewInitResolve!: () => void;

    constructor(@inject(LoggerServiceName) private readonly loggerService: LoggerService) {
        this.webviewInitPromise = new Promise(resolve => {
            this.webviewInitResolve = resolve;
        });
    }

    init(connect: Connection) {
        this.connect = connect;
        this.connect.onExit((...args) => {
            this.loggerService.error(LoggerUploaderCategoryType.KernelExitError, 'kernel connect onExit', ...args);
            // todo
        });
        this.connect.onShutdown(() => {
            this.loggerService.error(LoggerUploaderCategoryType.KernelShutdownError, 'kernel connect onShutdown');
            // todo
        });
        this.ptInit();
        return this;
    }

    /**
     * 返回一个 Promise 用于等待 webview 初始化完成
     * 如果 webview 已经初始化完成，则立即返回一个已解决的 Promise
     * 如果还未初始化，则返回一个处于等待状态的 Promise
     */
    get webviewInitialized(): Promise<void> {
        if (this.isWebviewInitialized) {
            return Promise.resolve();
        }
        return this.webviewInitPromise;
    }

    /**
     * 为指定事件注册请求处理程序
     * @param eventName - 要处理的事件名称
     * @param callback - 处理请求的回调函数
     * @returns 用于注销处理程序的可释放对象
     */
    onRequest = <K extends keyof OnRequestTypes>(eventName: K, callback: OnRequestTypes[K]) => {
        return this.connect.onRequest(eventName, callback);
    };

    /**
     * 发送具有指定事件名称和可选数据的请求
     * @param eventName - 要发送的事件名称
     * @param data - 要随请求发送的可选数据
     * @returns 解析为响应的 Promise
     */
    sendRequest = <K extends keyof SendRequestTypes>(
        eventName: K,
        data?: Parameters<SendRequestTypes[K]>[0]
    ): Promise<ReturnType<SendRequestTypes[K]>> => {
        return this.connect.sendRequest(eventName, data);
    };

    /**
     * 发送具有指定事件名称和可选参数的通知
     * @param eventName - 要发送的事件名称
     * @param params - 要随通知发送的可选参数
     * @returns void
     */
    sendNotification = (eventName: string, params?: any) => {
        return this.connect.sendNotification(eventName, params);
    };

    /**
     * 为指定事件注册通知处理程序
     * @param eventName - 要处理的通知事件名称
     * @param callback - 处理通知的回调函数
     * @returns 用于注销处理程序的可释放对象
     */
    onNotification = <K extends keyof OnRequestTypes>(eventName: K, callback: OnRequestTypes[K]) => {
        return this.connect.onNotification(eventName, callback);
    };

    /**
     * 向连接控制台记录消息
     * @param message - 要记录的消息
     * @returns void
     */
    logMessage = (message: string) => {
        return this.connect.console.info(message);
    };

    /**
     * 向 webview 发送具有指定事件名称和参数的消息
     * @param eventName - 要发送的事件名称
     * @param params - 要随消息发送的参数
     */
    sendWebviewMessage = <K extends keyof PTEventTypes>(
        eventName: K,
        params: Parameters<PTEventTypes[K]>[0]
    ): Promise<ReturnType<PTEventTypes[K]>> => {
        return this.ptClient.send(eventName, params);
    };

    /**
     * 为具有指定事件名称的 webview 消息注册处理程序
     * @param eventName - 要处理的事件名称
     * @param callback - 处理消息的回调函数
     */
    onWebviewMessage = <K extends keyof PTEventTypes>(eventName: K, callback: PTEventTypes[K]) => {
        return this.ptServer.register(eventName, callback);
    };

    /**
     * @deprecated This feature is not yet implemented
     */
    // @ts-ignore
    sendWebviewStreamMessage = <T>(eventName: string, params?: any): AsyncIterableIterator<T> => {
        // TODO
    };

    /**
     * 为具有指定事件名称的 webview 流消息注册处理程序
     * @param eventName - 要处理的事件名称
     * @param callback - 处理流消息的回调函数
     * @returns 用于注销处理程序的可释放对象
     */
    onWebviewStreamMessage = <T>(eventName: string, callback: (params?: any) => AsyncIterableIterator<T>) => {
        return this.ptServer.registerStream(eventName, callback);
    };

    private async ptInit() {
        const transport = {
            send: (params: unknown) => {
                this.connect.sendRequest(KERNEL_PASSTHROUGH_MESSAGE_TO_IDE_REQUEST, params);
            },
        };
        this.ptServer = new PTMessageServer(transport);

        this.ptClient = new PTMessageClient(transport, timer);

        this.connect.onRequest(
            IDE_PASSTHROUGH_MESSAGE_TO_KERNEL_REQUEST,
            (message: PassthroughMessageParams<unknown> | PassthroughMessageResponse<unknown>) => {
                if (message.type === 'request') {
                    this.ptServer.handleRequest(message);
                }
                else {
                    this.ptClient.handleResponse(message);
                }
            }
        );

        this.onWebviewMessage(PT_WEBVIEW_SYSTEM_INITIALIZED_REQUEST, message => {
            this.isWebviewInitialized = true;
            this.webviewInitResolve();
            this.loggerService.info('Receive a message from webview 🌐');
            return Promise.resolve({receive: true});
        });
    }
}
