import {inject, injectable} from 'inversify';
import {ConnectService} from './ConnectService.js';
import {
    IDE_CONFIG_DID_CHANGE_REQUEST,
    IDE_EXTENSION_CONFIG_DID_CHANGE_REQUEST,
    KERNEL_EXTENSION_CONFIG_FETCH_REQUEST,
    KERNEL_CONFIG_FETCH_REQUEST,
} from '../events.js';
import type {ExtensionConfig, KernelConfig, WorkspaceInfo} from '../types/index.js';
import {PLATFORM} from '../types/index.js';
import {InitializeParams} from 'vscode-languageserver';
import {URI} from 'vscode-uri';
import {EventEmitter} from 'node:events';
import {ConnectServiceName} from './serviceName.js';

@injectable()
export class ConfigService {
    private isInited: boolean = false;
    private params!: InitializeParams;
    // TODO 这个好奇怪
    public workspaceInfo!: WorkspaceInfo;
    private kernelConfig!: KernelConfig;
    private extensionCnfig!: ExtensionConfig;
    private configEmitter: EventEmitter = new EventEmitter();

    constructor(@inject(ConnectServiceName) private readonly connectService: ConnectService) {
    }
    // kernel config
    get appRoot() {
        return this.kernelConfig.appRoot;
    }

    get appHost() {
        return this.kernelConfig.appHost;
    }

    get language() {
        return this.kernelConfig.language;
    }

    get deviceId() {
        return this.kernelConfig.deviceId;
    }

    get ideName() {
        return this.kernelConfig.ideName ?? process.env.IDE_NAME;
    }

    get ideSeries() {
        return this.kernelConfig.ideSeries;
    }

    get ideVersion() {
        return this.kernelConfig.ideVersion ?? process.env.IDE_VERSION;
    }

    get extensionVersion() {
        return this.kernelConfig.extensionVersion;
    }

    get environment() {
        return this.kernelConfig.environment || process.env.ENVIRONMENT;
    }

    // TODO ide config>环境变量>编译期
    get platform() {
        return this.kernelConfig.platform || process.env.PLATFORM;
    }

    get themeColor() {
        return this.kernelConfig.themeColor;
    }

    get httpHost() {
        return this.kernelConfig.httpHost;
    }

    get proxyUrl() {
        return this.kernelConfig.proxyUrl;
    }

    get serverEndpointPrefix() {
        return this.kernelConfig.serverEndpointPrefix;
    }

    get customized() {
        return this.kernelConfig.customized;
    }

    get customizedUrl() {
        return this.kernelConfig.customizedUrl;
    }

    // extension config
    // 厂内username，厂外license
    get key() {
        return this.isInternal ? this.extensionCnfig.username : this.extensionCnfig.license;
    }

    get license() {
        return this.extensionCnfig.license;
    }

    get username() {
        return this.extensionCnfig.username;
    }

    get zuluConfig() {
        return this.extensionCnfig.zuluConfig;
    }

    // env config
    get isSaaS() {
        return this.kernelConfig.platform === PLATFORM.SAAS;
    }

    get isInternal() {
        return this.kernelConfig.platform === PLATFORM.INTERNAL;
    }

    get isPoc() {
        return this.kernelConfig.platform === PLATFORM.POC;
    }

    get isVSCode() {
        return this.kernelConfig.ideName.toLowerCase() === 'vscode';
    }
    // 虽然可以使用 lsp.workspace，为了简单
    // workspace config
    get workspacePath() {
        return this.workspaceInfo.rootPath;
    }

    get ideTerminalInfo() {
        return this.kernelConfig.ideTerminalInfo;
    }

    // 判断厂内username，厂外license 是否存在且合法，不会发送请求
    get isLogin() {
        return this.isInternal ? !!this.username : !!this.license;
    }

    async init() {
        this.connectService.onRequest(IDE_CONFIG_DID_CHANGE_REQUEST, this.configDidChange);
        this.connectService.onRequest(IDE_EXTENSION_CONFIG_DID_CHANGE_REQUEST, this.extensionDidChange);
        await this.getConfig();
        this.isInited = true;
    }

    async getConfig() {
        // TODO 错误处理，ajv一下，不合法直接报错
        this.kernelConfig = (await this.connectService.sendRequest(KERNEL_CONFIG_FETCH_REQUEST)).data;
        this.extensionCnfig = (await this.connectService.sendRequest(KERNEL_EXTENSION_CONFIG_FETCH_REQUEST)).data;
    }

    configDidChange = (data: KernelConfig) => {
        this.kernelConfig = data;
        for (const [key, value] of Object.entries(data)) {
            if (value !== (this.kernelConfig as any)[key]) {
                this.configEmitter.emit(key, value);
            }
        }
    };
    // TODO 这可能有坑，object是嵌套的
    extensionDidChange = (data: ExtensionConfig) => {
        this.extensionCnfig = data;
        for (const [key, value] of Object.entries(data)) {
            if (value !== (this.extensionCnfig as any)[key]) {
                this.configEmitter.emit(key, value);
            }
        }
    };

    setInitializeParams(params: InitializeParams) {
        this.params = params;
        if (params.workspaceFolders) {
            const {workspaceFolders} = params;
            this.workspaceInfo = {
                workspaceFolders,
                rootUri: workspaceFolders?.at(0)?.uri,
                rootPath: URI.parse(workspaceFolders?.at(0)?.uri ?? '').fsPath,
            };
        }
        else {
            // 这里为了兼容vs 2022
            const {rootUri} = params;
            this.workspaceInfo = {
                workspaceFolders: [{
                    uri: rootUri ?? '',
                    name: URI.parse(rootUri ?? '').fsPath,
                }],
                rootUri: rootUri ?? '',
                rootPath: URI.parse(rootUri ?? '').fsPath,
            };
        }
    }

    // vscode polyfill
    onDidChangeConfig(
        key: keyof (KernelConfig & ExtensionConfig),
        callback: (key: keyof (KernelConfig & ExtensionConfig)) => void
    ): {dispose: () => void} {
        this.configEmitter.on(key, callback);

        return {
            dispose: () => {
                this.configEmitter.removeListener(key, callback);
            },
        };
    }
}
