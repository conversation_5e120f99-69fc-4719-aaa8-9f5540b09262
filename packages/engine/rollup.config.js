import resolve from '@rollup/plugin-node-resolve';
import dts from 'rollup-plugin-dts';
import commonjs from '@rollup/plugin-commonjs';
import json from '@rollup/plugin-json';
import terser from '@rollup/plugin-terser';

const config = [{
    input: 'dist/index.js',
    output: {
        file: 'bundle/index.js',
        format: 'es',
        sourcemap: process.env.PLATFORM === 'internal',
    },
    plugins: [
        commonjs(),
        resolve({
            preferBuiltins: true,
        }),
        json(),
        terser(),
    ]
}, {
    input: 'dist/index.d.ts',
    output: [{ file: 'bundle/index.d.ts', format: 'es' }],
    plugins: [dts()],
}];

export default config;
