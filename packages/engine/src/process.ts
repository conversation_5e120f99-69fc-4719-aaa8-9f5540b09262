import url from 'node:url';
import {ChildProcess} from 'node:child_process';
import {pEvent} from 'p-event';
import {execaNode} from 'execa';
import {resolve} from 'import-meta-resolve';

const binary = url.fileURLToPath(resolve('@comate/plugin-host/bin', import.meta.url));

interface PluginProcessForkOptions {
    nodeOptions?: string[];
    onUnexpectedExit?: (code: number) => void;
    onUnexpectedError?: (error: Error) => void;
    onPluginSetup?: (pluginName: string) => void;
}

export function forkPluginProcess(groupKey: string, options: PluginProcessForkOptions) {
    const nodeOptions = ['--no-warnings', ...options.nodeOptions ?? []];
    const child = execaNode(
        binary,
        [],
        {
            nodeOptions,
            env: {
                ...process.env,
                COMATE_PLUGIN_GROUP_KEY: groupKey,
            },
        }
    );

    void child.on(
        'exit',
        code => {
            if (code) {
                options.onUnexpectedExit?.(code);
            }
        }
    );

    void child.catch(
        (error: Error) => {
            error && options.onUnexpectedError?.(error);
        }
    );

    void child.on(
        'message',
        (message: any) => {
            if (!message.comateInternalEvent) {
                return;
            }

            const event = message.comateInternalEvent;
            if (event.type === 'PLUGIN_SETUP') {
                options.onPluginSetup?.(event.payload.pluginName);
            }
        }
    );

    return child;
}

async function waitProcessInitialize(process: ChildProcess) {
    return new Promise(resolve => {
        const message = {
            comateInternalEvent: {
                type: 'PING',
            },
        };

        const pingInterval = setInterval(
            () => process.send(message),
            100
        );

        const timeout = setTimeout(
            () => {
                clearInterval(pingInterval);
                resolve(false);
            },
            1000
        );

        process.once('message', () => {
            clearTimeout(timeout);
            clearInterval(pingInterval);
            resolve(true);
        });
    });
}

interface PluginSetupInput {
    name: string;
    directory: string;
}

export async function invokePluginSetupToProcess(process: ChildProcess, input: PluginSetupInput): Promise<void> {
    await waitProcessInitialize(process);
    const message = {
        comateInternalEvent: {
            type: 'PLUGIN_ATTACH',
            payload: {
                directory: input.directory,
            },
        },
    };

    process?.send(message);

    const isPluginSetup = (message: any) => {
        const event = message.comateInternalEvent;
        return event?.type === 'PLUGIN_SETUP' && event.payload.pluginName === input.name;
    };
    return pEvent(process, 'message', isPluginSetup);
}
