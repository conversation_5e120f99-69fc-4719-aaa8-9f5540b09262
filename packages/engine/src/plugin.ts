import {
    ACTION_CHAT_TASK_PROGRESS,
    ACTION_REQUEST_PERMISSION,
    Channel,
    TaskProgressPayload,
    Session,
    RequestPermissionPayload,
    ACTION_ASK_LLM,
    LlmPayload,
    ACTION_GET_PLUGIN_CONFIG,
    GetPluginConfigPayload,
    ACTION_INFORMATION_QUERY,
    InformationPayload,
    SessionInit,
    ACTION_ASK_LLM_STREAMING,
    ACTION_DIAGNOSTIC_SCAN_TASK_PROGRESS,
    DiagnosticScanTaskProgressPayload,
    ACTION_SECUBOT_TASK_PROGRESS,
    SecubotQueryPayload,
    ACTION_DEBUG_TASK_PROCESS,
    DebugAgentPluginPayload,
    RAGPayload,
    ACTION_ASK_RAG,
    ACTION_CODE_SEARCH,
    CodeSearchPayload,
    ACTION_BATCH_ACCEPT,
    BatchAcceptPayload,
    ACTION_DIAGNOSTIC_SCAN_TASK_COUNT,
    DiagnosticScanCountPayload,
    ACTION_ISCAN_RESULT,
    IScanResultPayload,
    ACTION_ISCAN_JOB_BUILD_ID,
    IScanJobBuildPayload,
    ACTION_SA_SCAN_DIAGNOSTIC_RESULT,
    SAScanDiagnosticResult,
} from '@comate/plugin-shared-internals';

interface PayloadMap {
    [ACTION_CHAT_TASK_PROGRESS]: TaskProgressPayload;
    [ACTION_REQUEST_PERMISSION]: RequestPermissionPayload;
    [ACTION_ASK_LLM]: LlmPayload;
    [ACTION_ASK_RAG]: RAGPayload;
    [ACTION_ASK_LLM_STREAMING]: LlmPayload;
    [ACTION_GET_PLUGIN_CONFIG]: GetPluginConfigPayload;
    [ACTION_INFORMATION_QUERY]: InformationPayload;
    [ACTION_DIAGNOSTIC_SCAN_TASK_PROGRESS]: DiagnosticScanTaskProgressPayload;
    [ACTION_DIAGNOSTIC_SCAN_TASK_COUNT]: DiagnosticScanCountPayload;
    [ACTION_SECUBOT_TASK_PROGRESS]: SecubotQueryPayload;
    [ACTION_DEBUG_TASK_PROCESS]: DebugAgentPluginPayload;
    [ACTION_CODE_SEARCH]: CodeSearchPayload;
    [ACTION_BATCH_ACCEPT]: BatchAcceptPayload;
    [ACTION_ISCAN_RESULT]: IScanResultPayload;
    [ACTION_ISCAN_JOB_BUILD_ID]: IScanJobBuildPayload;
    [ACTION_SA_SCAN_DIAGNOSTIC_RESULT]: SAScanDiagnosticResult;
}

/**
 * 处理Engine与插件之间的会话
 */
export class PluginSession extends Session<PayloadMap> {
    protected initializeListeners() {
        super.initializeListeners();

        this.forwardMessageToParent(ACTION_REQUEST_PERMISSION);
        this.forwardMessageToParent(ACTION_ASK_LLM);
        this.forwardMessageToParent(ACTION_ASK_RAG);
        this.forwardMessageToParent(ACTION_ASK_LLM_STREAMING);
        this.forwardMessageToParent(ACTION_DEBUG_TASK_PROCESS);
        this.forwardMessageToParent(ACTION_GET_PLUGIN_CONFIG);
        this.forwardMessageToParent(ACTION_INFORMATION_QUERY);
        this.forwardMessageToParent(ACTION_CHAT_TASK_PROGRESS);
        this.forwardMessageToParent(ACTION_DIAGNOSTIC_SCAN_TASK_PROGRESS);
        this.forwardMessageToParent(ACTION_DIAGNOSTIC_SCAN_TASK_COUNT);
        this.forwardMessageToParent(ACTION_SECUBOT_TASK_PROGRESS);
        this.forwardMessageToParent(ACTION_CODE_SEARCH);
        this.forwardMessageToParent(ACTION_BATCH_ACCEPT);
        this.forwardMessageToParent(ACTION_ISCAN_RESULT);
        this.forwardMessageToParent(ACTION_ISCAN_JOB_BUILD_ID);
        this.forwardMessageToParent(ACTION_SA_SCAN_DIAGNOSTIC_RESULT);

    }
}

export class PluginChannel extends Channel<PluginSession> {
    protected createSession(init: SessionInit) {
        return new PluginSession(init, this.implement);
    }
}
