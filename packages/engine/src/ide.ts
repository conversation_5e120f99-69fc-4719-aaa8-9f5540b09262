import {
    ACTION_CHAT_QUERY,
    ChatQueryPayload,
    Session,
    Channel,
    ChannelImplement,
    ChannelImplementMaybe,
    ACTION_REQUEST_PERMISSION,
    RequestPermissionPayload,
    ACTION_ASK_LLM,
    LlmPayload,
    ACTION_GET_PLUGIN_CONFIG,
    GetPluginConfigPayload,
    ACTION_INFORMATION_QUERY,
    InformationPayload,
    SessionInit,
    ACTION_START_BACKGROUND_SERVICE,
    StartBackgroundServicePayload,
    Execution,
    ACTION_ASK_LLM_STREAMING,
    ACTION_CUSTOM_COMMAND,
    CustomCommandPayload,
    ACTION_DIAGNOSTIC_SCAN,
    DiagnosticScanInvokePayload,
    ACTION_SECUBOT,
    SecubotQueryPayload,
    DebugAgentPluginPayload,
    ACTION_DEBUG_TASK_PROCESS,
    ACTION_ASK_RAG,
    RAGPayload,
    ACTION_CODE_SEARCH,
    CodeSearchPluginPayload,
    ACTION_BATCH_ACCEPT,
    BatchAcceptPluginPayload,
    ACTION_START_ISCAN,
    IScanInvokePayload,
    ACTION_SA_SCAN_DIAGNOSTIC,
    SADiagnosticScanInvokePayload,
} from '@comate/plugin-shared-internals';
import {PluginContainer} from './container.js';
import {EngineRegistry} from './registry.js';

interface ForwadPayloadMap {
    [ACTION_REQUEST_PERMISSION]: RequestPermissionPayload;
    [ACTION_ASK_LLM]: LlmPayload;
    [ACTION_ASK_RAG]: RAGPayload;
    [ACTION_ASK_LLM_STREAMING]: LlmPayload;
    [ACTION_GET_PLUGIN_CONFIG]: GetPluginConfigPayload;
    [ACTION_CHAT_QUERY]: ChatQueryPayload;
    [ACTION_CUSTOM_COMMAND]: CustomCommandPayload;
    [ACTION_INFORMATION_QUERY]: InformationPayload;
    [ACTION_START_BACKGROUND_SERVICE]: StartBackgroundServicePayload;
    [ACTION_DIAGNOSTIC_SCAN]: DiagnosticScanInvokePayload;
    [ACTION_SECUBOT]: SecubotQueryPayload;
    [ACTION_DEBUG_TASK_PROCESS]: DebugAgentPluginPayload;
    [ACTION_CODE_SEARCH]: CodeSearchPluginPayload;
    [ACTION_BATCH_ACCEPT]: BatchAcceptPluginPayload;
    [ACTION_START_ISCAN]: IScanInvokePayload;
    [ACTION_SA_SCAN_DIAGNOSTIC]: SADiagnosticScanInvokePayload;
}

interface SessionContext {
    plugins: Map<string, PluginContainer>;
    registry: EngineRegistry;
}

/**
 * 处理IDE与Engine之间的会话
 */
export class IdeSession extends Session<ForwadPayloadMap> {
    private readonly plugins: Map<string, PluginContainer>;

    constructor(init: SessionInit, implement: ChannelImplement, context: SessionContext) {
        super(init, implement);
        this.plugins = context.plugins;
    }

    protected initializeListeners() {
        super.initializeListeners();
        this.forwardSessionTask(ACTION_CHAT_QUERY);
        this.forwardSessionTask(ACTION_SECUBOT);
        this.forwardSessionTask(ACTION_CUSTOM_COMMAND);
        this.forwardSessionTask(ACTION_DIAGNOSTIC_SCAN);
        this.forwardSessionTask(ACTION_START_BACKGROUND_SERVICE);
        this.forwardExecution(ACTION_REQUEST_PERMISSION);
        this.forwardExecution(ACTION_ASK_LLM);
        this.forwardExecution(ACTION_ASK_RAG);
        this.forwardExecution(ACTION_ASK_LLM_STREAMING);
        this.forwardExecution(ACTION_DEBUG_TASK_PROCESS);
        this.forwardExecution(ACTION_GET_PLUGIN_CONFIG);
        this.forwardExecution(ACTION_INFORMATION_QUERY);
        this.forwardExecution(ACTION_CODE_SEARCH);
        this.forwardExecution(ACTION_BATCH_ACCEPT);
        this.forwardSessionTask(ACTION_START_ISCAN);
        this.forwardSessionTask(ACTION_SA_SCAN_DIAGNOSTIC);
    }

    private forwardExecution<A extends keyof ForwadPayloadMap>(action: A) {
        this.setListener(
            action,
            async (payload: {pluginName: string}, execution?: Execution) => {
                const plugin = this.plugins.get(payload.pluginName);
                if (plugin) {
                    await plugin.sendMessage(
                        this.sessionId,
                        {action, payload},
                        execution
                    );
                }
                else {
                    this.logPluginNotFound(payload.pluginName);
                }
            }
        );
    }

    private forwardSessionTask<A extends keyof ForwadPayloadMap>(action: A) {
        this.setListener(
            action,
            async (payload: {pluginName: string}) => {
                const plugin = this.plugins.get(payload.pluginName);
                if (plugin) {
                    await plugin.startSession(this, action, payload);
                    // TODO: 看下是不是要异常处理
                }
                else {
                    this.logPluginNotFound(payload.pluginName);
                }
            }
        );
    }

    private logPluginNotFound(pluginName: string) {
        this.log('error', this.constructor.name, 'PluginNotFound', {pluginName});
    }
}

export class IdeChannel extends Channel<IdeSession> {
    private readonly plugins: Map<string, PluginContainer>;
    private readonly registry: EngineRegistry;

    constructor(implement: ChannelImplementMaybe, registry: EngineRegistry, plugins: Map<string, PluginContainer>) {
        super(implement);
        this.registry = registry;
        this.plugins = plugins;
    }

    protected createSession(init: SessionInit): IdeSession {
        return new IdeSession(
            init,
            this.implement,
            {
                plugins: this.plugins,
                registry: this.registry,
            }
        );
    }
}
